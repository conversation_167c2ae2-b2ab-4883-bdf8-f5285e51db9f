.dn-editor {
    /*overflow-y: hidden;*/
}

.dn-editor .cm-scroller {
    /*overflow-y: hidden;*/
    padding: unset;
}

.dn-editor .workspace-leaf {
    all: unset;
}

.dn-editor .dn-content {
    display: none;
    /*margin: 0;*/
    /*border-radius: var(--he-popover-border-radius);*/
    /*overflow: hidden;*/
    /*height: 100%;*/
}

.dn-editor .workspace-leaf,
.dn-editor .workspace-split {
    height: 100%;
    width: 100%;
}

.dn-editor .markdown-source-view.mod-cm6 .cm-editor {
    min-height: auto;
}

.dn-editor .cm-content {
    padding-bottom: 0 !important;
    padding-top: 0 !important;
}

.dn-editor .view-content {
    background: none !important;
}

.dn-editor .cm-scroller {
    padding: 0 !important;
    overflow-y: clip;
}
.daily-note-view .daily-note-wrapper::before {
    content: "";
    display: block;
    height: 1px;
    width: var(--file-line-width);
    margin-bottom: 30px;
    margin-left: auto;
    margin-right: auto;
    background-color: var(--background-modifier-border);
}

.daily-note-view .daily-note-wrapper:first-of-type::before {
    height: 0px;
}

.daily-note-view .dn-range-indicator + .daily-note-container::before {
    height: 0px;
}

.is-popout-window .dn-editor .dn-content {
    margin: 0;
    border-radius: var(--he-popover-border-radius);
    overflow: hidden;
    height: auto;
}

.is-popout-window .dn-editor .workspace-leaf,
.is-popout-window .dn-editor .workspace-split {
    height: auto;
    width: 100%;
}

.is-popout-window .dn-editor .cm-scroller {
    height: auto;
}

.is-popout-window .dn-editor .markdown-source-view.mod-cm6 {
    height: auto;
}

.is-popout-window .dn-editor .view-content {
    height: auto;
}

.is-popout-window .dn-editor .workspace-leaf-content {
    height: auto;
}

.daily-note-view .embedded-backlinks {
    min-height: unset !important;
}

.daily-note-view {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-4);
    overflow-x: hidden;
}

body.daily-notes-hide-frontmatter
    .daily-note-view
    .markdown-source-view.is-live-preview.show-properties
    .metadata-container {
    display: none;
}

body.daily-notes-hide-backlinks .daily-note-view .embedded-backlinks {
    display: none;
}

/* Custom Range Modal Styles */
.custom-range-date-container {
    margin-bottom: var(--size-4-2);
    display: flex;
    align-items: center;
}

.custom-range-date-container span {
    width: 100px;
    display: inline-block;
}

.custom-range-date-container input {
    flex: 1;
}

.custom-range-button-container {
    display: flex;
    justify-content: flex-end;
    margin-top: var(--size-4-4);
    gap: var(--size-4-2);
}

/* Preset management styles */
.preset-container {
    margin-bottom: var(--size-4-4);
}

.no-presets-message {
    color: var(--text-muted);
    font-style: italic;
    margin: var(--size-4-2) 0;
}

.preset-list {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-2);
    margin: var(--size-4-2) 0;
}

.preset-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--size-2-4) var(--size-4-3);
    background-color: var(--background-secondary);
    border-radius: var(--radius-s);
}

.preset-info {
    display: flex;
    align-items: center;
}

.preset-type {
    font-weight: bold;
    margin-right: var(--size-2-2);
}

.preset-actions {
    display: flex;
    gap: var(--size-4-2);
}

.preset-action-button {
    padding: var(--size-2-2) var(--size-2-4);
    border-radius: var(--radius-s);
    font-size: var(--font-ui-small);
    cursor: pointer;
}

.preset-open-button {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.preset-delete-button {
    background-color: var(--background-modifier-error);
    color: var(--text-on-accent);
}

/* Add preset modal styles */
.setting-item {
    margin-bottom: var(--size-4-4);
}

.target-input {
    width: 100%;
    padding: var(--size-2-3);
    border-radius: var(--radius-s);
    border: 1px solid var(--background-modifier-border);
    background-color: var(--background-primary);
}

.modal-button-container {
    display: flex;
    justify-content: flex-end;
    gap: var(--size-4-2);
    margin-top: var(--size-4-4);
}

.is-phone .mod-root .workspace-tabs:not(.mod-visible):has(.daily-note-view) {
    display: flex !important;
}
.daily-note.svelte-1d2sruf.svelte-1d2sruf{margin-bottom:var(--size-4-5);padding-bottom:var(--size-4-8)}.daily-note.svelte-1d2sruf.svelte-1d2sruf:has(.daily-note-editor[data-collapsed="true"]){margin-bottom:0;padding-bottom:0}.daily-note-editor.svelte-1d2sruf.svelte-1d2sruf{min-height:100px}.daily-note-editor[data-collapsed="true"].svelte-1d2sruf.svelte-1d2sruf{display:none}.daily-note.svelte-1d2sruf .collapse-button.svelte-1d2sruf{display:none}.daily-note.svelte-1d2sruf:hover .collapse-button.svelte-1d2sruf{display:block}.daily-note.svelte-1d2sruf .collapse-button.svelte-1d2sruf{color:var(--text-muted)}.daily-note.svelte-1d2sruf .collapse-button.svelte-1d2sruf:hover{color:var(--text-normal)}.daily-note.svelte-1d2sruf:has(.is-readable-line-width) .daily-note-title.svelte-1d2sruf{max-width:calc(var(--file-line-width) + var(--size-4-4));width:calc(var(--file-line-width) + var(--size-4-4));margin-left:auto;margin-right:auto;margin-bottom:var(--size-4-8);display:flex;align-items:center;justify-content:start;gap:var(--size-4-2)}.collapse-button.svelte-1d2sruf.svelte-1d2sruf{margin-left:calc(var(--size-4-8) * -1)}.collapse-button[data-collapsed="true"].svelte-1d2sruf.svelte-1d2sruf{transform:rotate(-90deg);transition:transform 0.2s ease}.daily-note.svelte-1d2sruf:not(:has(.is-readable-line-width)) .daily-note-title.svelte-1d2sruf{display:flex;justify-content:start;align-items:center;width:100%;padding-left:calc(calc(100% - var(--file-line-width)) / 2 - var(--size-4-2));padding-right:calc(calc(100% - var(--file-line-width)) / 2 - var(--size-4-2));margin-top:var(--size-4-8);gap:var(--size-4-2)}.clickable-link.svelte-1d2sruf.svelte-1d2sruf{cursor:pointer;text-decoration:none}.clickable-link.svelte-1d2sruf.svelte-1d2sruf:hover{color:var(--color-accent);text-decoration:underline}.editor-placeholder.svelte-1d2sruf.svelte-1d2sruf{display:flex;justify-content:center;align-items:center;height:100px;color:var(--text-muted);font-style:italic}.collapse-button.svelte-1d2sruf.svelte-1d2sruf{cursor:pointer;display:flex;align-items:center;justify-content:center;width:24px;height:24px;border-radius:4px;color:var(--text-muted);transition:background-color 0.2s ease}.collapse-button.svelte-1d2sruf.svelte-1d2sruf:hover{color:var(--text-normal)}.dn-stock.svelte-4q3cv7{height:1000px;width:100%;display:flex;justify-content:center;align-items:center}.dn-stock-text.svelte-4q3cv7{text-align:center}.no-more-text.svelte-4q3cv7{margin-left:auto;margin-right:auto;text-align:center}.dn-blank-day.svelte-4q3cv7{display:flex;margin-left:auto;margin-right:auto;max-width:var(--file-line-width);color:var(--color-base-40);padding-top:20px;padding-bottom:20px;transition:all 300ms}.dn-blank-day.svelte-4q3cv7:hover{padding-top:40px;padding-bottom:40px;transition:padding 300ms}.dn-blank-day-text.svelte-4q3cv7{margin-left:auto;margin-right:auto;text-align:center}.daily-note-wrapper.svelte-4q3cv7{width:100%}