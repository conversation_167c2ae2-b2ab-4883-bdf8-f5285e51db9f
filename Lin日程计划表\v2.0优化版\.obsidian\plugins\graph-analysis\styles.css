td.internal-link,
div.internal-link,
span.internal-link {
  color: var(--text-accent);
  text-decoration-line: none !important;
}

td.internal-link:hover,
div.internal-link:hover,
span.internal-link:hover {
  color: var(--text-accent-hover, var(--text-accent));
}

tr.GA-linked td:first-child::before,
span.GA-linked::before,
div.GA-linked::before {
}
tr.GA-not-linked,
span.GA-not-linked {
  /* background-color: yellow; */
}

.GA-icon {
  color: var(--text-normal);
  width: 13px;
  height: 13px;
  display: inline-block;
  padding-top: 2px !important;
}

.GA-Global {
  color: red;
}
.GA-Global::before {
  content: '🌍';
}

td.GA-measure {
  /* display:none */
}
td.GA-node {
  /* background-color: yellow */
}

select.dropdown.GA-DD {
  line-height: normal;
  padding: 0.2em 1.9em 0.2em 0.4em;
  width: fit-content;
}

button.GA-Refresh-Button {
  margin-left: 5px;
  padding: 3px;
}

body.is-mobile .GA-details,
body.is-mobile .GA-details::before {
  font-size: larger;
}

.GA-highlight-sentence {
  animation: highlightSentence 1.5s 1;
  border-radius: 3px;
}

@keyframes highlightSentence {
  0% {
    background-color: transparent;
  }
  10% {
    background-color: var(--text-highlight-bg);
  }
  80% {
    background-color: var(--text-highlight-bg);
  }
  100% {
    background-color: transparent;
  }
}

.GA-View ::-webkit-scrollbar {
  width: 1em;
  height: 0px;
  border-radius: 10px;
  background-color: var(--scrollbar-bg);
}

.scrollContainer {
  padding: 0px 0px 4px 0px;
}
.scrollContainer:hover {
  padding: 0px;
}

.GA-View .scrollContainer:hover ::-webkit-scrollbar {
  height: 4px;
}
.GA-View ::-webkit-scrollbar-track {
  border-radius: 10px;
}

.GA-View ::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: var(--scrollbar-thumb-bg);
}

div.CC-sentence > ul,
div.CC-sentence > ol,
div.CC-sentence > p {
  margin-block-start: 0;
  margin-block-end: 0;
}

mark.CC-mark {
  color: var(--text-normal);
  background-color: var(--text-highlight-bg);
}
.CC-edit {
  margin-block-start: 0;
  margin-block-end: 0;
}

.CC-hr {
  margin-block-start: 0.3em;
  margin-block-end: 0.3em;
}