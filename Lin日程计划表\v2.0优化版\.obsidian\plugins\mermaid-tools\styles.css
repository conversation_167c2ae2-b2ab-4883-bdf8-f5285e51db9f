.mermaid-toolbar-container, .mermaid-toolbar-container * {
    max-width: 100%;
    max-height: 100%;
}

.mermaid-toolbar-top-row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
}

.mermaid-toolbar-elements-container {
    padding-top: 1rem;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

.mermaid-toolbar-element {
    font-size: var(--font-ui-small);
    cursor: pointer;
    padding: 2px 2px 2px 5px;
    border-radius: 3px;
    flex: 1 0 auto;
}

.mermaid-toolbar-element:hover {
    background-color: var(--interactive-hover);
}

.mermaid-tools-element-category-header::before {
    content: "▼  ";
    font-size: 70%;
    padding-bottom: 2px;
}

.mermaid-tools-element-category-header.collapsed::before {
    content: "▶  ";
    font-size: 70%;
    padding-bottom: 2px;
}

.mermaid-tools-element-container {
    padding-top: 6px;
    border-bottom: var(--border-width) solid var(--color-base-35);
}

.mermaid-tools-edit-element-modal > div {
    margin-bottom: 0.5rem;
}

.mermaid-tools-edit-element-modal label {
    margin-right: 1rem;
}