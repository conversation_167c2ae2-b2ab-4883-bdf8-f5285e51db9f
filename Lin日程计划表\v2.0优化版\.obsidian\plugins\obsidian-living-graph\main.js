/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// main.ts
__export(exports, {
  default: () => LivingGraphPlugin
});
var import_obsidian = __toModule(require("obsidian"));

// Functories.ts
var PeriodicFunctory = class {
  constructor(_func) {
    this._func = _func;
    this._scale = 1;
  }
  call(x, min, max, delta) {
    return this._func.apply(this, arguments);
  }
  period(dilation) {
    return dilation / this._scale * Math.PI;
  }
  static createHelp(type, ...args) {
    const instance = new type(...args);
    return Object.assign((...innerargs) => instance.call(...innerargs), { period: (dilation) => instance.period(dilation) });
  }
  static create(...args) {
    return PeriodicFunctory.createHelp(FunctoryStandin, ...args);
  }
};
var FunctoryStandin = class extends PeriodicFunctory {
  constructor(func) {
    super(func);
  }
};
var WaveFunctory = class extends PeriodicFunctory {
  constructor(func, coefficients, normalize = true) {
    super(func);
    this.coefficients = coefficients;
    this._scale = normalize ? coefficients.reduce((a, b) => a + Math.abs(b[1]), 0) : 1;
  }
  call(x, min, max, delta) {
    return this._func.apply(this, [x, min, max, delta, this.coefficients]);
  }
  static create(...args) {
    return super.createHelp(WaveFunctory, ...args);
  }
};
function scaledWave(x, min, max, delta, coefficients, normalize = true) {
  return min + (max - min) * wave.apply(this, [Math.sin, x, coefficients, normalize]);
}
function wave(func, x, coefficients, normalize = true) {
  if (normalize)
    x *= this._scale * 2;
  let sum = coefficients.reduce((a, b) => [a[0] + b[1] * func(x * b[0]), 0], [0, 0])[0];
  return normalize ? (sum / this._scale + 1) / 2 : sum;
}

// graphManip.ts
function getScaleConversion(from, for_) {
  var _a;
  const scaleConversions = new Map([
    ["setRepelStrength", [0, 20]],
    ["setLinkDistance", [30, 500]]
  ]);
  let [low, high] = (_a = scaleConversions.get(from.replace(/bound /, ""))) != null ? _a : [0, 1];
  let [lower, upper] = for_;
  lower *= high - low;
  upper *= high - low;
  return [lower, upper];
}
function setCenterForce(centerForce, leaves) {
  leaves.forEach((leaf) => getEngine(leaf).forceOptions.optionListeners.centerStrength(centerForce));
}
function setLinkDistance(linkDistance, leaves) {
  leaves.forEach((leaf) => getEngine(leaf).forceOptions.optionListeners.linkDistance(linkDistance));
}
function setLinkStrength(linkStrength, leaves) {
  leaves.forEach((leaf) => getEngine(leaf).forceOptions.optionListeners.linkStrength(linkStrength));
}
function setRepelStrength(repelStrength, leaves) {
  leaves.forEach((leaf) => getEngine(leaf).forceOptions.optionListeners.repelStrength(repelStrength));
}
function getEngine(leaf) {
  var _a;
  return (_a = leaf.view.dataEngine) != null ? _a : leaf.view.engine;
}

// interval.ts
var Interval = class {
  constructor(interval, plugin) {
    this.plugin = plugin;
    this._interval = null;
    this._desiredUpdatesPerPeriod = 24;
    this._desiredUpdatesPerSecond = 5;
    this._maxUpdatesPerSecond = 27;
    Object.assign(this, interval);
    [this.lower, this.upper] = getScaleConversion(this.g_x.name, [this.lower, this.upper]);
    this._inversionCenter = (this.lower + this.upper) / 2;
    this._periodMs = this.f_x.period(this.timeDilation) * 1e3;
    this._updatesPerSecond = Math.min(this._maxUpdatesPerSecond, Math.max(this._desiredUpdatesPerSecond, Math.ceil(1e3 * this._desiredUpdatesPerPeriod / this._periodMs)));
    this._updatePeriodMs = 1e3 / this._updatesPerSecond;
    this._tslices = Math.ceil(this._periodMs / this._updatePeriodMs);
    this.evaluator = this._tslices > 10 ? this.memodEvaluate.bind(this) : this.evaluate.bind(this);
    if (this._tslices > 10)
      this.memoizeInterval();
  }
  isActive() {
    return this._interval !== null;
  }
  start(leaves) {
    var _a;
    if (this._interval)
      return;
    let offset = Date.now() % this._periodMs + ((_a = this.relativeOfsset) != null ? _a : 0) * this._periodMs;
    this._interval = setInterval(() => {
      this.g_x(this.evaluator(Date.now() - offset), leaves);
    }, this._updatePeriodMs);
  }
  clear() {
    clearInterval(this._interval);
    this._interval = null;
  }
  evaluate(time) {
    var _a;
    let res = this.f_x(time / (this.timeDilation * 1e3), this.lower, this.upper, this._updatePeriodMs);
    res = this.invert ? this._inversionCenter + (this._inversionCenter - res) : res;
    res = this.multiplier ? (res - this._inversionCenter) * this.multiplier + this._inversionCenter : res;
    return ((_a = this.verticalOffset) != null ? _a : 0) + (this.preventClamp ? res : Math.clamp(res, this.lower, this.upper));
  }
  memodEvaluate(time) {
    if (!this._memoized)
      this.memoizeInterval();
    return this._memoized[Math.floor(time % this._periodMs / this._updatePeriodMs)];
  }
  memoizeInterval() {
    this._memoized = [];
    for (let i = 0, time = 0; i < this._tslices; i++, time += this._updatePeriodMs)
      this._memoized.push(this.evaluate(time));
  }
};

// main.ts
function scaledSine(x, min, max, delta) {
  return min + (max - min) * Math.sin(x);
}
function simpleSine2(x, min, max, delta) {
  return min + (max - min) * (2 * Math.sin(x));
}
var intervals = [
  {
    name: "Breathing",
    repelForce: 15.33 / 20,
    linkForce: 0.12,
    linkDistance: 0.2,
    intervals: [
      {
        lower: 0.25,
        upper: 0.4,
        timeDilation: 2,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[1, 0.5]], false)
      }
    ]
  },
  {
    name: "Breathing Network",
    repelForce: 18.5 / 20,
    linkForce: 0.92,
    linkDistance: 0.1,
    intervals: [
      {
        lower: 0.25,
        upper: 0.4,
        timeDilation: 2,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[1, 0.5]], false)
      }
    ]
  },
  {
    name: "Breathing Colonies",
    intervals: [
      {
        lower: 0.7,
        upper: 0.99,
        timeDilation: 0.35,
        g_x: setLinkDistance,
        f_x: PeriodicFunctory.create(scaledSine)
      },
      {
        lower: 0.45,
        upper: 0.5,
        timeDilation: 0.6,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [2, 1], [0.5, 1]])
      },
      {
        lower: 0.7,
        upper: 0.99,
        timeDilation: 0.35,
        g_x: setLinkStrength,
        f_x: PeriodicFunctory.create(scaledSine)
      },
      {
        lower: 0.5,
        upper: 0.55,
        timeDilation: 0.6,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [2, 1], [0.5, 1]])
      }
    ]
  },
  {
    name: "Jellyfish Bloom",
    centerForce: 0.1,
    linkDistance: 1,
    intervals: [
      {
        lower: 0.1,
        upper: 0.99,
        timeDilation: 50,
        g_x: setLinkStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 1], [5, 1], [7, 1]])
      },
      {
        lower: 0.85,
        upper: 0.99,
        timeDilation: 60,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 1], [5, 1], [7, 1]])
      }
    ]
  },
  {
    name: "Muscle",
    centerForce: 0.35,
    linkForce: 0.75,
    linkDistance: 0.1,
    intervals: [
      {
        lower: 0.6,
        upper: 1.2,
        timeDilation: 1.5,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[1, 0.5]], false)
      },
      {
        lower: 0.2,
        upper: 0.5,
        multiplier: 1.5,
        timeDilation: 1.5,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[1, 0.5]], false)
      }
    ]
  },
  {
    name: "Heart",
    intervals: [
      {
        lower: 0.7,
        upper: 1.1,
        timeDilation: 0.35 * 4,
        invert: true,
        g_x: setLinkDistance,
        f_x: PeriodicFunctory.create(simpleSine2)
      },
      {
        lower: 0.4,
        upper: 0.45,
        timeDilation: 0.6 * 4,
        multiplier: 1.5,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [2, 1], [0.5, 1]])
      },
      {
        lower: 0.7,
        upper: 1.1,
        timeDilation: 0.35 * 4,
        invert: true,
        g_x: setLinkStrength,
        f_x: PeriodicFunctory.create(simpleSine2)
      },
      {
        lower: 0.9,
        upper: 0.99,
        verticalOffset: -0.2,
        multiplier: 2,
        timeDilation: 0.6 * 4,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [2, 1], [0.5, 1]])
      }
    ]
  },
  {
    name: "Squid",
    intervals: [
      {
        lower: 0,
        upper: 1,
        timeDilation: 40,
        g_x: setLinkStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 5], [4.5, -6]])
      },
      {
        lower: 0.3,
        upper: 1,
        timeDilation: 40,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 5], [4.5, -6]])
      },
      {
        lower: 0.2,
        upper: 0.35,
        timeDilation: 40,
        invert: true,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 5], [4.5, -6]])
      },
      {
        lower: 0,
        upper: 1,
        invert: true,
        timeDilation: 40,
        g_x: setLinkDistance,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 5], [4.5, -6]])
      }
    ]
  },
  {
    name: "Organism",
    centerForce: 0.47,
    linkDistance: 0.35,
    intervals: [
      {
        lower: 0.15,
        upper: 1,
        timeDilation: 50,
        g_x: setLinkStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 5], [5, 4], [7, 2]])
      },
      {
        lower: 0.87,
        upper: 1,
        timeDilation: 60,
        relativeOfsset: 0.5,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 5], [5, 4], [7, 2]])
      }
    ]
  },
  {
    name: "Jump Roping",
    intervals: [
      {
        lower: 0.7,
        upper: 0.99,
        timeDilation: 0.4,
        g_x: setLinkDistance,
        f_x: PeriodicFunctory.create(scaledSine)
      },
      {
        lower: 0.4,
        upper: 0.55,
        timeDilation: 1,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [2, 1], [0.5, 1]])
      },
      {
        lower: 0.7,
        upper: 0.99,
        timeDilation: 0.35,
        g_x: setLinkStrength,
        f_x: PeriodicFunctory.create(scaledSine)
      },
      {
        lower: 0.9,
        upper: 0.99,
        timeDilation: 0.6,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [2, 1], [0.5, 1]])
      }
    ]
  },
  {
    name: "I like to move it",
    centerForce: 0.6,
    linkForce: 1,
    linkDistance: 0.3,
    intervals: [
      {
        lower: 0.3,
        upper: 1.2,
        timeDilation: 1.9,
        multiplier: 1.2,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [3, 1 / 3], [5, 1 / 5]])
      }
    ]
  },
  {
    name: "Boo!",
    centerForce: 0.5,
    linkForce: 1,
    linkDistance: 1,
    intervals: [
      {
        lower: 0.3,
        upper: 1,
        timeDilation: 2.8,
        multiplier: 5,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[1, 1], [3, 1 / 3], [5, 1 / 5], [7, 1 / 7], [9, 1 / 9], [11, 1 / 11], [13, 1 / 13]])
      }
    ]
  },
  {
    name: "Panting",
    intervals: [
      {
        lower: 0.3,
        upper: 1,
        timeDilation: -20,
        g_x: setLinkStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 3], [4.5, -6]])
      },
      {
        lower: 0.3,
        upper: 1,
        timeDilation: 20,
        g_x: setRepelStrength,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 3], [4.5, -6]])
      },
      {
        lower: 0.1,
        upper: 0.25,
        timeDilation: -20,
        g_x: setCenterForce,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 3], [4.5, -6]])
      },
      {
        lower: 0,
        upper: 1,
        timeDilation: -20,
        g_x: setLinkDistance,
        f_x: WaveFunctory.create(scaledWave, [[3, 2], [5, 3], [4.5, -6]])
      }
    ]
  }
];
var LivingGraphPlugin = class extends import_obsidian.Plugin {
  startIntervals() {
    let ags = this.activeGraphSetting;
    let setAttribute = (value, func) => {
      let [low, high] = getScaleConversion(func.name, [0, 1]);
      func(value * (high - low) + low, this.graphLeaves);
    };
    if (ags.centerForce)
      setAttribute(ags.centerForce, setCenterForce);
    if (ags.linkForce)
      setAttribute(ags.linkForce, setLinkStrength);
    if (ags.repelForce)
      setAttribute(ags.repelForce, setRepelStrength);
    if (ags.linkDistance)
      setAttribute(ags.linkDistance, setLinkDistance);
    if (this.intervals.first().isActive())
      setTimeout(this.clearIntervals.bind(this), 1500);
    else
      this.intervals.forEach((i) => i.start(this.graphLeaves));
  }
  clearIntervals() {
    var _a;
    (_a = this.intervals) == null ? void 0 : _a.forEach((i) => i.clear());
  }
  refreshLeaves() {
    var _a, _b;
    this.graphLeaves = [].concat((_a = this.app.workspace.getLeavesOfType("graph")) != null ? _a : []).concat(this.settings.includeLocal ? (_b = this.app.workspace.getLeavesOfType("localgraph")) != null ? _b : [] : []);
  }
  onload() {
    return __async(this, null, function* () {
      yield this.loadData();
      this.addSettingTab(new LivingGraphSettingTab(this.app, this));
      this.registerEvent(this.app.workspace.on("layout-change", this.refreshLeaves.bind(this)));
      this.app.workspace.onLayoutReady(this.refreshLeaves.bind(this));
      this.refreshLeaves();
      this.addCommand({
        id: "toggle",
        name: "Living Graph - Toggle",
        callback: this.startIntervals.bind(this)
      });
      this.updateIntervals();
    });
  }
  onunload() {
    return __async(this, null, function* () {
      this.clearIntervals();
    });
  }
  loadData() {
    var __superGet = (key) => super[key];
    return __async(this, null, function* () {
      this.settings = Object.assign({}, DEFAULT_SETTINGS, yield __superGet("loadData").call(this));
    });
  }
  saveData() {
    var __superGet = (key) => super[key];
    return __async(this, null, function* () {
      yield __superGet("saveData").call(this, this.settings);
    });
  }
  updateIntervals(which) {
    which != null ? which : which = new Set(this.settings.activeComposites);
    this.clearIntervals();
    this.refreshLeaves();
    this.intervals = intervals.filter((c) => {
      let use = which.has(c.name);
      if (use)
        this.activeGraphSetting = c;
      return use;
    }).map((c) => c.intervals.map((i) => new Interval(i, this))).flat();
  }
};
var DEFAULT_SETTINGS = {
  activeComposites: [intervals[0].name],
  includeLocal: true
};
var LivingGraphSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "Living Graph" });
    let saveAndRestart = (() => {
      var _a;
      let wasActive = (_a = this.plugin.intervals) == null ? void 0 : _a.first().isActive();
      this.plugin.updateIntervals();
      if (wasActive)
        this.plugin.startIntervals();
      this.plugin.saveData();
    }).bind(this);
    new import_obsidian.Setting(containerEl).setName("Presets").setDesc("Activate a living graph preset").addDropdown((cb) => {
      intervals.map((i) => i.name).forEach((name) => cb.addOption(name, name));
      cb.onChange((name) => __async(this, null, function* () {
        this.plugin.settings.activeComposites = [name];
        saveAndRestart();
      }));
      cb.setValue(this.plugin.settings.activeComposites[0]);
    });
    new import_obsidian.Setting(containerEl).setName("Include Local Graphs").addToggle((cb) => {
      cb.onChange((value) => __async(this, null, function* () {
        this.plugin.settings.includeLocal = value;
        saveAndRestart();
      }));
      cb.setValue(this.plugin.settings.includeLocal);
    });
  }
};

/* nosourcemap */