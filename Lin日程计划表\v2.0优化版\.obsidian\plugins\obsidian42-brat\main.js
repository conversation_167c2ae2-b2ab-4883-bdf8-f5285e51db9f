"use strict";var Kt=Object.create;var ge=Object.defineProperty;var Wt=Object.getOwnPropertyDescriptor;var Jt=Object.getOwnPropertyNames;var Qt=Object.getPrototypeOf,Zt=Object.prototype.hasOwnProperty;var S=(n,e)=>()=>(e||n((e={exports:{}}).exports,e),e.exports),ei=(n,e)=>{for(var t in e)ge(n,t,{get:e[t],enumerable:!0})},qe=(n,e,t,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of Jt(e))!Zt.call(n,s)&&s!==t&&ge(n,s,{get:()=>e[s],enumerable:!(i=Wt(e,s))||i.enumerable});return n};var ti=(n,e,t)=>(t=n!=null?Kt(Qt(n)):{},qe(e||!n||!n.__esModule?ge(t,"default",{value:n,enumerable:!0}):t,n)),ii=n=>qe(ge({},"__esModule",{value:!0}),n);var Le=S((ns,Xe)=>{var si=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...n)=>console.error("SEMVER",...n):()=>{};Xe.exports=si});var Ce=S((os,Ye)=>{var ni="2.0.0",oi=Number.MAX_SAFE_INTEGER||9007199254740991,ri=16,ai=250,li=["major","premajor","minor","preminor","patch","prepatch","prerelease"];Ye.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:ri,MAX_SAFE_BUILD_LENGTH:ai,MAX_SAFE_INTEGER:oi,RELEASE_TYPES:li,SEMVER_SPEC_VERSION:ni,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});var Se=S((x,Ke)=>{var{MAX_SAFE_COMPONENT_LENGTH:ke,MAX_SAFE_BUILD_LENGTH:ui,MAX_LENGTH:ci}=Ce(),gi=Le();x=Ke.exports={};var di=x.re=[],pi=x.safeRe=[],g=x.src=[],mi=x.safeSrc=[],d=x.t={},hi=0,$e="[a-zA-Z0-9-]",fi=[["\\s",1],["\\d",ci],[$e,ui]],bi=n=>{for(let[e,t]of fi)n=n.split(`${e}*`).join(`${e}{0,${t}}`).split(`${e}+`).join(`${e}{1,${t}}`);return n},f=(n,e,t)=>{let i=bi(e),s=hi++;gi(n,s,e),d[n]=s,g[s]=e,mi[s]=i,di[s]=new RegExp(e,t?"g":void 0),pi[s]=new RegExp(i,t?"g":void 0)};f("NUMERICIDENTIFIER","0|[1-9]\\d*");f("NUMERICIDENTIFIERLOOSE","\\d+");f("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${$e}*`);f("MAINVERSION",`(${g[d.NUMERICIDENTIFIER]})\\.(${g[d.NUMERICIDENTIFIER]})\\.(${g[d.NUMERICIDENTIFIER]})`);f("MAINVERSIONLOOSE",`(${g[d.NUMERICIDENTIFIERLOOSE]})\\.(${g[d.NUMERICIDENTIFIERLOOSE]})\\.(${g[d.NUMERICIDENTIFIERLOOSE]})`);f("PRERELEASEIDENTIFIER",`(?:${g[d.NUMERICIDENTIFIER]}|${g[d.NONNUMERICIDENTIFIER]})`);f("PRERELEASEIDENTIFIERLOOSE",`(?:${g[d.NUMERICIDENTIFIERLOOSE]}|${g[d.NONNUMERICIDENTIFIER]})`);f("PRERELEASE",`(?:-(${g[d.PRERELEASEIDENTIFIER]}(?:\\.${g[d.PRERELEASEIDENTIFIER]})*))`);f("PRERELEASELOOSE",`(?:-?(${g[d.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${g[d.PRERELEASEIDENTIFIERLOOSE]})*))`);f("BUILDIDENTIFIER",`${$e}+`);f("BUILD",`(?:\\+(${g[d.BUILDIDENTIFIER]}(?:\\.${g[d.BUILDIDENTIFIER]})*))`);f("FULLPLAIN",`v?${g[d.MAINVERSION]}${g[d.PRERELEASE]}?${g[d.BUILD]}?`);f("FULL",`^${g[d.FULLPLAIN]}$`);f("LOOSEPLAIN",`[v=\\s]*${g[d.MAINVERSIONLOOSE]}${g[d.PRERELEASELOOSE]}?${g[d.BUILD]}?`);f("LOOSE",`^${g[d.LOOSEPLAIN]}$`);f("GTLT","((?:<|>)?=?)");f("XRANGEIDENTIFIERLOOSE",`${g[d.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);f("XRANGEIDENTIFIER",`${g[d.NUMERICIDENTIFIER]}|x|X|\\*`);f("XRANGEPLAIN",`[v=\\s]*(${g[d.XRANGEIDENTIFIER]})(?:\\.(${g[d.XRANGEIDENTIFIER]})(?:\\.(${g[d.XRANGEIDENTIFIER]})(?:${g[d.PRERELEASE]})?${g[d.BUILD]}?)?)?`);f("XRANGEPLAINLOOSE",`[v=\\s]*(${g[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${g[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${g[d.XRANGEIDENTIFIERLOOSE]})(?:${g[d.PRERELEASELOOSE]})?${g[d.BUILD]}?)?)?`);f("XRANGE",`^${g[d.GTLT]}\\s*${g[d.XRANGEPLAIN]}$`);f("XRANGELOOSE",`^${g[d.GTLT]}\\s*${g[d.XRANGEPLAINLOOSE]}$`);f("COERCEPLAIN",`(^|[^\\d])(\\d{1,${ke}})(?:\\.(\\d{1,${ke}}))?(?:\\.(\\d{1,${ke}}))?`);f("COERCE",`${g[d.COERCEPLAIN]}(?:$|[^\\d])`);f("COERCEFULL",g[d.COERCEPLAIN]+`(?:${g[d.PRERELEASE]})?(?:${g[d.BUILD]})?(?:$|[^\\d])`);f("COERCERTL",g[d.COERCE],!0);f("COERCERTLFULL",g[d.COERCEFULL],!0);f("LONETILDE","(?:~>?)");f("TILDETRIM",`(\\s*)${g[d.LONETILDE]}\\s+`,!0);x.tildeTrimReplace="$1~";f("TILDE",`^${g[d.LONETILDE]}${g[d.XRANGEPLAIN]}$`);f("TILDELOOSE",`^${g[d.LONETILDE]}${g[d.XRANGEPLAINLOOSE]}$`);f("LONECARET","(?:\\^)");f("CARETTRIM",`(\\s*)${g[d.LONECARET]}\\s+`,!0);x.caretTrimReplace="$1^";f("CARET",`^${g[d.LONECARET]}${g[d.XRANGEPLAIN]}$`);f("CARETLOOSE",`^${g[d.LONECARET]}${g[d.XRANGEPLAINLOOSE]}$`);f("COMPARATORLOOSE",`^${g[d.GTLT]}\\s*(${g[d.LOOSEPLAIN]})$|^$`);f("COMPARATOR",`^${g[d.GTLT]}\\s*(${g[d.FULLPLAIN]})$|^$`);f("COMPARATORTRIM",`(\\s*)${g[d.GTLT]}\\s*(${g[d.LOOSEPLAIN]}|${g[d.XRANGEPLAIN]})`,!0);x.comparatorTrimReplace="$1$2$3";f("HYPHENRANGE",`^\\s*(${g[d.XRANGEPLAIN]})\\s+-\\s+(${g[d.XRANGEPLAIN]})\\s*$`);f("HYPHENRANGELOOSE",`^\\s*(${g[d.XRANGEPLAINLOOSE]})\\s+-\\s+(${g[d.XRANGEPLAINLOOSE]})\\s*$`);f("STAR","(<|>)?=?\\s*\\*");f("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");f("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});var Je=S((rs,We)=>{var wi=Object.freeze({loose:!0}),Ei=Object.freeze({}),Ti=n=>n?typeof n!="object"?wi:n:Ei;We.exports=Ti});var tt=S((as,et)=>{var Qe=/^[0-9]+$/,Ze=(n,e)=>{let t=Qe.test(n),i=Qe.test(e);return t&&i&&(n=+n,e=+e),n===e?0:t&&!i?-1:i&&!t?1:n<e?-1:1},yi=(n,e)=>Ze(e,n);et.exports={compareIdentifiers:Ze,rcompareIdentifiers:yi}});var he=S((ls,ot)=>{var de=Le(),{MAX_LENGTH:it,MAX_SAFE_INTEGER:pe}=Ce(),{safeRe:st,safeSrc:nt,t:me}=Se(),vi=Je(),{compareIdentifiers:U}=tt(),De=class n{constructor(e,t){if(t=vi(t),e instanceof n){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if(typeof e!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>it)throw new TypeError(`version is longer than ${it} characters`);de("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let i=e.trim().match(t.loose?st[me.LOOSE]:st[me.FULL]);if(!i)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>pe||this.major<0)throw new TypeError("Invalid major version");if(this.minor>pe||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>pe||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map(s=>{if(/^[0-9]+$/.test(s)){let o=+s;if(o>=0&&o<pe)return o}return s}):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(de("SemVer.compare",this.version,this.options,e),!(e instanceof n)){if(typeof e=="string"&&e===this.version)return 0;e=new n(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof n||(e=new n(e,this.options)),U(this.major,e.major)||U(this.minor,e.minor)||U(this.patch,e.patch)}comparePre(e){if(e instanceof n||(e=new n(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let i=this.prerelease[t],s=e.prerelease[t];if(de("prerelease compare",t,i,s),i===void 0&&s===void 0)return 0;if(s===void 0)return 1;if(i===void 0)return-1;if(i===s)continue;return U(i,s)}while(++t)}compareBuild(e){e instanceof n||(e=new n(e,this.options));let t=0;do{let i=this.build[t],s=e.build[t];if(de("build compare",t,i,s),i===void 0&&s===void 0)return 0;if(s===void 0)return 1;if(i===void 0)return-1;if(i===s)continue;return U(i,s)}while(++t)}inc(e,t,i){if(e.startsWith("pre")){if(!t&&i===!1)throw new Error("invalid increment argument: identifier is empty");if(t){let s=new RegExp(`^${this.options.loose?nt[me.PRERELEASELOOSE]:nt[me.PRERELEASE]}$`),o=`-${t}`.match(s);if(!o||o[1]!==t)throw new Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,i);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,i);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,i),this.inc("pre",t,i);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",t,i),this.inc("pre",t,i);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let s=Number(i)?1:0;if(this.prerelease.length===0)this.prerelease=[s];else{let o=this.prerelease.length;for(;--o>=0;)typeof this.prerelease[o]=="number"&&(this.prerelease[o]++,o=-2);if(o===-1){if(t===this.prerelease.join(".")&&i===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(s)}}if(t){let o=[t,s];i===!1&&(o=[t]),U(this.prerelease[0],t)===0?isNaN(this.prerelease[1])&&(this.prerelease=o):this.prerelease=o}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};ot.exports=De});var xe=S((us,at)=>{var rt=he(),Pi=(n,e,t)=>new rt(n,t).compare(new rt(e,t));at.exports=Pi});var ct=S((cs,ut)=>{var lt=he(),Ii=(n,e,t=!1)=>{if(n instanceof lt)return n;try{return new lt(n,e)}catch(i){if(!t)return null;throw i}};ut.exports=Ii});var Fe=S((gs,gt)=>{var Ri=he(),Ai=ct(),{safeRe:fe,t:be}=Se(),Ni=(n,e)=>{if(n instanceof Ri)return n;if(typeof n=="number"&&(n=String(n)),typeof n!="string")return null;e=e||{};let t=null;if(!e.rtl)t=n.match(e.includePrerelease?fe[be.COERCEFULL]:fe[be.COERCE]);else{let c=e.includePrerelease?fe[be.COERCERTLFULL]:fe[be.COERCERTL],l;for(;(l=c.exec(n))&&(!t||t.index+t[0].length!==n.length);)(!t||l.index+l[0].length!==t.index+t[0].length)&&(t=l),c.lastIndex=l.index+l[1].length+l[2].length;c.lastIndex=-1}if(t===null)return null;let i=t[2],s=t[3]||"0",o=t[4]||"0",r=e.includePrerelease&&t[5]?`-${t[5]}`:"",a=e.includePrerelease&&t[6]?`+${t[6]}`:"";return Ai(`${i}.${s}.${o}${r}${a}`,e)};gt.exports=Ni});var zt=S(w=>{"use strict";Object.defineProperty(w,"__esModule",{value:!0});var P=require("obsidian"),Ve="YYYY-MM-DD",He="gggg-[W]ww",St="YYYY-MM",Dt="YYYY-[Q]Q",xt="YYYY";function ie(n){var t,i;let e=window.app.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t[n])==null?void 0:i.enabled)}function se(){var n,e,t,i;try{let{internalPlugins:s,plugins:o}=window.app;if(ie("daily")){let{format:l,folder:u,template:p}=((e=(n=o.getPlugin("periodic-notes"))==null?void 0:n.settings)==null?void 0:e.daily)||{};return{format:l||Ve,folder:(u==null?void 0:u.trim())||"",template:(p==null?void 0:p.trim())||""}}let{folder:r,format:a,template:c}=((i=(t=s.getPluginById("daily-notes"))==null?void 0:t.instance)==null?void 0:i.options)||{};return{format:a||Ve,folder:(r==null?void 0:r.trim())||"",template:(c==null?void 0:c.trim())||""}}catch(s){console.info("No custom daily note settings found!",s)}}function ne(){var n,e,t,i,s,o,r;try{let a=window.app.plugins,c=(n=a.getPlugin("calendar"))==null?void 0:n.options,l=(t=(e=a.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.weekly;if(ie("weekly"))return{format:l.format||He,folder:((i=l.folder)==null?void 0:i.trim())||"",template:((s=l.template)==null?void 0:s.trim())||""};let u=c||{};return{format:u.weeklyNoteFormat||He,folder:((o=u.weeklyNoteFolder)==null?void 0:o.trim())||"",template:((r=u.weeklyNoteTemplate)==null?void 0:r.trim())||""}}catch(a){console.info("No custom weekly note settings found!",a)}}function oe(){var e,t,i,s;let n=window.app.plugins;try{let o=ie("monthly")&&((t=(e=n.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.monthly)||{};return{format:o.format||St,folder:((i=o.folder)==null?void 0:i.trim())||"",template:((s=o.template)==null?void 0:s.trim())||""}}catch(o){console.info("No custom monthly note settings found!",o)}}function re(){var e,t,i,s;let n=window.app.plugins;try{let o=ie("quarterly")&&((t=(e=n.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.quarterly)||{};return{format:o.format||Dt,folder:((i=o.folder)==null?void 0:i.trim())||"",template:((s=o.template)==null?void 0:s.trim())||""}}catch(o){console.info("No custom quarterly note settings found!",o)}}function ae(){var e,t,i,s;let n=window.app.plugins;try{let o=ie("yearly")&&((t=(e=n.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.yearly)||{};return{format:o.format||xt,folder:((i=o.folder)==null?void 0:i.trim())||"",template:((s=o.template)==null?void 0:s.trim())||""}}catch(o){console.info("No custom yearly note settings found!",o)}}function Ft(...n){let e=[];for(let i=0,s=n.length;i<s;i++)e=e.concat(n[i].split("/"));let t=[];for(let i=0,s=e.length;i<s;i++){let o=e[i];!o||o==="."||t.push(o)}return e[0]===""&&t.unshift(""),t.join("/")}function Di(n){let e=n.substring(n.lastIndexOf("/")+1);return e.lastIndexOf(".")!=-1&&(e=e.substring(0,e.lastIndexOf("."))),e}async function xi(n){let e=n.replace(/\\/g,"/").split("/");if(e.pop(),e.length){let t=Ft(...e);window.app.vault.getAbstractFileByPath(t)||await window.app.vault.createFolder(t)}}async function le(n,e){e.endsWith(".md")||(e+=".md");let t=P.normalizePath(Ft(n,e));return await xi(t),t}async function q(n){let{metadataCache:e,vault:t}=window.app,i=P.normalizePath(n);if(i==="/")return Promise.resolve(["",null]);try{let s=e.getFirstLinkpathDest(i,""),o=await t.cachedRead(s),r=window.app.foldManager.load(s);return[o,r]}catch(s){return console.error(`Failed to read the daily note template '${i}'`,s),new P.Notice("Failed to read the daily note template"),["",null]}}function k(n,e="day"){let t=n.clone().startOf(e).format();return`${e}-${t}`}function Ot(n){return n.replace(/\[[^\]]*\]/g,"")}function Fi(n,e){if(e==="week"){let t=Ot(n);return/w{1,2}/i.test(t)&&(/M{1,4}/.test(t)||/D{1,4}/.test(t))}return!1}function X(n,e){return Bt(n.basename,e)}function Oi(n,e){return Bt(Di(n),e)}function Bt(n,e){let i={day:se,week:ne,month:oe,quarter:re,year:ae}[e]().format.split("/").pop(),s=window.moment(n,i,!0);if(!s.isValid())return null;if(Fi(i,e)&&e==="week"){let o=Ot(i);if(/w{1,2}/i.test(o))return window.moment(n,i.replace(/M{1,4}/g,"").replace(/D{1,4}/g,""),!1)}return s}var Ue=class extends Error{};async function Mt(n){let e=window.app,{vault:t}=e,i=window.moment,{template:s,format:o,folder:r}=se(),[a,c]=await q(s),l=n.format(o),u=await le(r,l);try{let p=await t.create(u,a.replace(/{{\s*date\s*}}/gi,l).replace(/{{\s*time\s*}}/gi,i().format("HH:mm")).replace(/{{\s*title\s*}}/gi,l).replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(h,m,y,v,E,T)=>{let R=i(),$=n.clone().set({hour:R.get("hour"),minute:R.get("minute"),second:R.get("second")});return y&&$.add(parseInt(v,10),E),T?$.format(T.substring(1).trim()):$.format(o)}).replace(/{{\s*yesterday\s*}}/gi,n.clone().subtract(1,"day").format(o)).replace(/{{\s*tomorrow\s*}}/gi,n.clone().add(1,"d").format(o)));return e.foldManager.save(p,c),p}catch(p){console.error(`Failed to create file: '${u}'`,p),new P.Notice("Unable to create new file.")}}function Bi(n,e){var t;return(t=e[k(n,"day")])!=null?t:null}function Mi(){let{vault:n}=window.app,{folder:e}=se(),t=n.getAbstractFileByPath(P.normalizePath(e));if(!t)throw new Ue("Failed to find daily notes folder");let i={};return P.Vault.recurseChildren(t,s=>{if(s instanceof P.TFile){let o=X(s,"day");if(o){let r=k(o,"day");i[r]=s}}}),i}var _e=class extends Error{};function Vi(){let{moment:n}=window,e=n.localeData()._week.dow,t=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(;e;)t.push(t.shift()),e--;return t}function Hi(n){return Vi().indexOf(n.toLowerCase())}async function Vt(n){let{vault:e}=window.app,{template:t,format:i,folder:s}=ne(),[o,r]=await q(t),a=n.format(i),c=await le(s,a);try{let l=await e.create(c,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,h,m,y,v)=>{let E=window.moment(),T=n.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return h&&T.add(parseInt(m,10),y),v?T.format(v.substring(1).trim()):T.format(i)}).replace(/{{\s*title\s*}}/gi,a).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi,(u,p,h)=>{let m=Hi(p);return n.weekday(m).format(h.trim())}));return window.app.foldManager.save(l,r),l}catch(l){console.error(`Failed to create file: '${c}'`,l),new P.Notice("Unable to create new file.")}}function Ui(n,e){var t;return(t=e[k(n,"week")])!=null?t:null}function _i(){let n={};if(!Ut())return n;let{vault:e}=window.app,{folder:t}=ne(),i=e.getAbstractFileByPath(P.normalizePath(t));if(!i)throw new _e("Failed to find weekly notes folder");return P.Vault.recurseChildren(i,s=>{if(s instanceof P.TFile){let o=X(s,"week");if(o){let r=k(o,"week");n[r]=s}}}),n}var Ge=class extends Error{};async function Ht(n){let{vault:e}=window.app,{template:t,format:i,folder:s}=oe(),[o,r]=await q(t),a=n.format(i),c=await le(s,a);try{let l=await e.create(c,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,h,m,y,v)=>{let E=window.moment(),T=n.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return h&&T.add(parseInt(m,10),y),v?T.format(v.substring(1).trim()):T.format(i)}).replace(/{{\s*date\s*}}/gi,a).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,a));return window.app.foldManager.save(l,r),l}catch(l){console.error(`Failed to create file: '${c}'`,l),new P.Notice("Unable to create new file.")}}function Gi(n,e){var t;return(t=e[k(n,"month")])!=null?t:null}function ji(){let n={};if(!_t())return n;let{vault:e}=window.app,{folder:t}=oe(),i=e.getAbstractFileByPath(P.normalizePath(t));if(!i)throw new Ge("Failed to find monthly notes folder");return P.Vault.recurseChildren(i,s=>{if(s instanceof P.TFile){let o=X(s,"month");if(o){let r=k(o,"month");n[r]=s}}}),n}var je=class extends Error{};async function zi(n){let{vault:e}=window.app,{template:t,format:i,folder:s}=re(),[o,r]=await q(t),a=n.format(i),c=await le(s,a);try{let l=await e.create(c,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,h,m,y,v)=>{let E=window.moment(),T=n.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return h&&T.add(parseInt(m,10),y),v?T.format(v.substring(1).trim()):T.format(i)}).replace(/{{\s*date\s*}}/gi,a).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,a));return window.app.foldManager.save(l,r),l}catch(l){console.error(`Failed to create file: '${c}'`,l),new P.Notice("Unable to create new file.")}}function qi(n,e){var t;return(t=e[k(n,"quarter")])!=null?t:null}function Xi(){let n={};if(!Gt())return n;let{vault:e}=window.app,{folder:t}=re(),i=e.getAbstractFileByPath(P.normalizePath(t));if(!i)throw new je("Failed to find quarterly notes folder");return P.Vault.recurseChildren(i,s=>{if(s instanceof P.TFile){let o=X(s,"quarter");if(o){let r=k(o,"quarter");n[r]=s}}}),n}var ze=class extends Error{};async function Yi(n){let{vault:e}=window.app,{template:t,format:i,folder:s}=ae(),[o,r]=await q(t),a=n.format(i),c=await le(s,a);try{let l=await e.create(c,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,h,m,y,v)=>{let E=window.moment(),T=n.clone().set({hour:E.get("hour"),minute:E.get("minute"),second:E.get("second")});return h&&T.add(parseInt(m,10),y),v?T.format(v.substring(1).trim()):T.format(i)}).replace(/{{\s*date\s*}}/gi,a).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,a));return window.app.foldManager.save(l,r),l}catch(l){console.error(`Failed to create file: '${c}'`,l),new P.Notice("Unable to create new file.")}}function Ki(n,e){var t;return(t=e[k(n,"year")])!=null?t:null}function Wi(){let n={};if(!jt())return n;let{vault:e}=window.app,{folder:t}=ae(),i=e.getAbstractFileByPath(P.normalizePath(t));if(!i)throw new ze("Failed to find yearly notes folder");return P.Vault.recurseChildren(i,s=>{if(s instanceof P.TFile){let o=X(s,"year");if(o){let r=k(o,"year");n[r]=s}}}),n}function Ji(){var i,s;let{app:n}=window,e=n.internalPlugins.plugins["daily-notes"];if(e&&e.enabled)return!0;let t=n.plugins.getPlugin("periodic-notes");return t&&((s=(i=t.settings)==null?void 0:i.daily)==null?void 0:s.enabled)}function Ut(){var t,i;let{app:n}=window;if(n.plugins.getPlugin("calendar"))return!0;let e=n.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.weekly)==null?void 0:i.enabled)}function _t(){var t,i;let{app:n}=window,e=n.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.monthly)==null?void 0:i.enabled)}function Gt(){var t,i;let{app:n}=window,e=n.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.quarterly)==null?void 0:i.enabled)}function jt(){var t,i;let{app:n}=window,e=n.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.yearly)==null?void 0:i.enabled)}function Qi(n){let e={day:se,week:ne,month:oe,quarter:re,year:ae}[n];return e()}function Zi(n,e){return{day:Mt,month:Ht,week:Vt}[n](e)}w.DEFAULT_DAILY_NOTE_FORMAT=Ve;w.DEFAULT_MONTHLY_NOTE_FORMAT=St;w.DEFAULT_QUARTERLY_NOTE_FORMAT=Dt;w.DEFAULT_WEEKLY_NOTE_FORMAT=He;w.DEFAULT_YEARLY_NOTE_FORMAT=xt;w.appHasDailyNotesPluginLoaded=Ji;w.appHasMonthlyNotesPluginLoaded=_t;w.appHasQuarterlyNotesPluginLoaded=Gt;w.appHasWeeklyNotesPluginLoaded=Ut;w.appHasYearlyNotesPluginLoaded=jt;w.createDailyNote=Mt;w.createMonthlyNote=Ht;w.createPeriodicNote=Zi;w.createQuarterlyNote=zi;w.createWeeklyNote=Vt;w.createYearlyNote=Yi;w.getAllDailyNotes=Mi;w.getAllMonthlyNotes=ji;w.getAllQuarterlyNotes=Xi;w.getAllWeeklyNotes=_i;w.getAllYearlyNotes=Wi;w.getDailyNote=Bi;w.getDailyNoteSettings=se;w.getDateFromFile=X;w.getDateFromPath=Oi;w.getDateUID=k;w.getMonthlyNote=Gi;w.getMonthlyNoteSettings=oe;w.getPeriodicNoteSettings=Qi;w.getQuarterlyNote=qi;w.getQuarterlyNoteSettings=re;w.getTemplateInfo=q;w.getWeeklyNote=Ui;w.getWeeklyNoteSettings=ne;w.getYearlyNote=Ki;w.getYearlyNoteSettings=ae});var es={};ei(es,{default:()=>Ae});module.exports=ii(es);var Yt=require("obsidian");var F=require("obsidian");var C=class extends Error{constructor(t,i,s,o){let r=Math.ceil((s-Math.floor(Date.now()/1e3))/60);super(`GitHub API rate limit exceeded. Reset in ${r} minutes.`);this.limit=t;this.remaining=i;this.reset=s;this.requestUrl=o}getMinutesToReset(){return Math.ceil((this.reset-Math.floor(Date.now()/1e3))/60)}},D=class extends Error{constructor(e){var i,s;super(`GitHub API error ${e}: ${e.message}`),this.message=e.message;let t=e;this.status=(i=t.status)!=null?i:400,this.headers=(s=t.headers)!=null?s:{},this.name="GitHubResponseError"}};var B=require("obsidian");var Li=xe(),dt=Fe();var we=n=>{let e=n.replace(/https?:\/\/github\.com\//i,"");return e.toLowerCase().endsWith(".git")&&(e=e.slice(0,-4)),e},pt=["ghp_","github_pat_"],Ci=/^(gh[ps]_[a-zA-Z0-9]{36}|github_pat_[a-zA-Z0-9]{22}_[a-zA-Z0-9]{59})$/,mt=async(n,e)=>{var o,r,a,c,l,u,p,h,m,y,v;let t=["repo","public_repo","metadata=read"],i=pt.some(E=>n.toLowerCase().startsWith(E.toLowerCase())),s=Ci.test(n);if(!i||!s)return{validToken:!1,currentScopes:[],acceptedScopes:[],acceptedPermissions:[],expirationDate:null,rateLimit:{limit:0,remaining:0,reset:0,resource:"",used:0},error:{type:i?"invalid_format":"invalid_prefix",message:"Invalid token format",details:{validPrefixes:pt}}};try{let E=Date.now()%1e3,T=e||`user${E}/repo${E%100}`;if(await Ee({url:`https://api.github.com/repos/${T}`,headers:{Authorization:`Token ${n}`,Accept:"application/vnd.github.v3+json"}}),e)return{validToken:!0,currentScopes:[],acceptedScopes:[],acceptedPermissions:[],expirationDate:null,rateLimit:{limit:0,remaining:0,reset:0,resource:"",used:0},error:{type:"none",message:"No error",details:{}}};throw new Error("Expected request to fail")}catch(E){if(!(E instanceof D))throw E;let T=E.headers;if(!T)throw new Error("No headers in GitHub response");let R=T["github-authentication-token-expiration"],$=R?new Date(R):null,ce=$&&!Number.isNaN($.getTime())?$.toISOString():null,A={validToken:!1,currentScopes:(r=(o=T["x-oauth-scopes"])==null?void 0:o.split(", "))!=null?r:[],acceptedScopes:(c=(a=T["x-accepted-oauth-scopes"])==null?void 0:a.split(", "))!=null?c:[],acceptedPermissions:(u=(l=T["x-accepted-github-permissions"])==null?void 0:l.split(", "))!=null?u:[],expirationDate:ce,rateLimit:{limit:Number.parseInt((p=T["x-ratelimit-limit"])!=null?p:"0"),remaining:Number.parseInt((h=T["x-ratelimit-remaining"])!=null?h:"0"),reset:Number.parseInt((m=T["x-ratelimit-reset"])!=null?m:"0"),resource:(y=T["x-ratelimit-resource"])!=null?y:"",used:Number.parseInt((v=T["x-ratelimit-used"])!=null?v:"0")},error:{type:"none",message:"No error",details:{}}};return A.expirationDate&&new Date(A.expirationDate)<new Date?(A.error={type:"expired",message:"Token has expired",details:{expirationDate:A.expirationDate}},A):A.currentScopes.some(Ne=>t.includes(Ne))||A.acceptedPermissions.some(Ne=>t.includes(Ne))?(A.validToken=E.status===404,A):(A.error={type:"insufficient_scope",message:"Token lacks required scopes. Check documentation for requirements.",details:{currentScopes:[...A.acceptedScopes,...A.acceptedPermissions]}},A)}},Oe=async(n,e=!0,t="")=>{let i=`https://api.github.com/repos/${n}`;try{let s=await Ee({url:i,headers:t?{Authorization:`Token ${t}`}:{}});return(await JSON.parse(s)).private}catch(s){if(s instanceof C)throw s;return e&&console.log("error in isPrivateRepo",i,s),!1}},ht=async(n,e=!0,t="")=>{let i=`https://api.github.com/repos/${n}/releases`;try{let s=await Ee({url:`${i}?per_page=100`,headers:t?{Authorization:`Token ${t}`}:{}});return(await JSON.parse(s)).map(r=>({version:r.tag_name,prerelease:r.prerelease}))}catch(s){if(s instanceof C||s instanceof D)throw s;return e&&console.log("Error in fetchReleaseVersions",i,s),null}},Y=async(n,e,t=!0,i=!1,s="")=>{try{let o=n.assets.find(c=>c.name===e);if(!o)return null;let r={Accept:"application/octet-stream"};(i&&s||s)&&(r.Authorization=`Token ${s}`);let a=await(0,B.request)({url:o.url,headers:r});return a==="Not Found"||a==='{"error":"Not Found"}'?null:a}catch(o){if(o instanceof C)throw o;return t&&console.log("error in grabReleaseFileFromRepository",URL,o),null}},ft=async(n=!0)=>{let e="https://raw.githubusercontent.com/obsidianmd/obsidian-releases/HEAD/community-plugins.json";try{let t=await(0,B.request)({url:e});return t==="404: Not Found"?null:await JSON.parse(t)}catch(t){return n&&console.log("error in grabCommmunityPluginList",t),null}},bt=async(n=!0)=>{let e="https://raw.githubusercontent.com/obsidianmd/obsidian-releases/HEAD/community-css-themes.json";try{let t=await(0,B.request)({url:e});return t==="404: Not Found"?null:await JSON.parse(t)}catch(t){return n&&console.log("error in grabCommmunityThemesList",t),null}},_=async(n,e=!1,t=!1)=>{let i=`https://raw.githubusercontent.com/${n}/HEAD/theme${e?"-beta":""}.css`;try{let s=await(0,B.request)({url:i});return s==="404: Not Found"?null:s}catch(s){return t&&console.log("error in grabCommmunityThemeCssFile",s),null}},wt=async(n,e=!0)=>{let t=`https://raw.githubusercontent.com/${n}/HEAD/manifest.json`;try{let i=await(0,B.request)({url:t});return i==="404: Not Found"?null:i}catch(i){return e&&console.log("error in grabCommmunityThemeManifestFile",i),null}},ki=n=>{let e=0;for(let t=0;t<n.length;t++)e+=n.charCodeAt(t);return e},K=n=>ki(n).toString(),W=async(n,e,t)=>{let i=await _(n,e,t);return i?K(i):"0"},$i=async(n,e,t=!0)=>{let i=`https://api.github.com/repos/${n}/commits?path=${e}&page=1&per_page=1`;try{let s=await(0,B.request)({url:i});return s==="404: Not Found"?null:JSON.parse(s)}catch(s){return t&&console.log("error in grabLastCommitInfoForAFile",s),null}},Et=async(n,e)=>{var i;let t=await $i(n,e);return t&&t.length>0&&((i=t[0].commit.committer)!=null&&i.date)?t[0].commit.committer.date:""},Be=async(n,e,t=!1,i=!1,s=!1,o)=>{var r;try{let a=e&&e!=="latest"?`https://api.github.com/repos/${n}/releases/tags/${e}`:`https://api.github.com/repos/${n}/releases`,c={Accept:"application/vnd.github.v3+json"};(s&&o||o)&&(c.Authorization=`Token ${o}`);let l=await Ee({url:a,headers:c});if(l==="404: Not Found")return null;let u=e&&e!=="latest"?[JSON.parse(l)]:JSON.parse(l);return i&&console.log(`grabReleaseFromRepository for ${n}:`,u),(r=u.sort((p,h)=>{let m=dt(p.tag_name,{includePrerelease:!0,loose:!0}),y=dt(h.tag_name,{includePrerelease:!0,loose:!0});return Li(y,m)}).filter(p=>t||!p.prerelease)[0])!=null?r:null}catch(a){throw i&&console.log(`Error in grabReleaseFromRepository for ${n}:`,a),a}},Ee=async(n,e)=>{let t=0,i=0,s=0;try{return await(0,B.request)(n)}catch(o){let r=new D(o),a=r.headers;if(a&&(t=Number.parseInt(a["x-ratelimit-limit"]),i=Number.parseInt(a["x-ratelimit-remaining"]),s=Number.parseInt(a["x-ratelimit-reset"])),r.status===403&&i===0){let c=new C(t,i,s,n.url);throw e&&console.error(`BRAT
GitHub API rate limit exceeded:`,`
Request: ${c.requestUrl}`,`
Rate limits - Remaining: ${c.remaining}`,`
Reset in: ${c.getMinutesToReset()} minutes`),c}throw e&&console.log("GitHub request failed:",o),r}};var Me={pluginList:[],pluginSubListFrozenVersion:[],themesList:[],updateAtStartup:!0,updateThemesAtStartup:!0,enableAfterInstall:!0,loggingEnabled:!1,loggingPath:"BRAT-log",loggingVerboseEnabled:!1,debuggingMode:!1,notificationsEnabled:!0,personalAccessToken:""};function Tt(n,e,t="latest",i=""){let s=!1;n.settings.pluginList.contains(e)||(n.settings.pluginList.unshift(e),s=!0),n.settings.pluginSubListFrozenVersion.filter(o=>o.repo===e).length===0&&(n.settings.pluginSubListFrozenVersion.unshift({repo:e,version:t,token:i||void 0}),s=!0),s&&n.saveSettings()}function yt(n,e){return n.settings.pluginList.contains(e)}function vt(n,e,t){let i={repo:e,lastUpdate:K(t)};n.settings.themesList.unshift(i),n.saveSettings()}function Pt(n,e){return!!n.settings.themesList.find(i=>i.repo===e)}function It(n,e,t){for(let i of n.settings.themesList)i.repo===e&&(i.lastUpdate=t,n.saveSettings())}var N=require("obsidian");var G=class{constructor(e,t){this.tokenEl=e,this.statusEl=t}async validateToken(e,t){var i,s,o,r,a,c,l,u,p,h,m,y,v;if((i=this.tokenEl)==null||i.inputEl.removeClass("valid-input","invalid-input"),!e)return(s=this.statusEl)==null||s.setText("No token provided"),(o=this.statusEl)==null||o.addClass("invalid"),(r=this.statusEl)==null||r.removeClass("valid"),!1;try{let E=await mt(e,t);return(a=this.statusEl)==null||a.removeClass("invalid","valid"),(c=this.statusEl)==null||c.empty(),E.validToken?((l=this.tokenEl)==null||l.inputEl.addClass("valid-input"),(u=this.statusEl)==null||u.addClass("valid"),this.showValidTokenInfo(E),!0):((p=this.tokenEl)==null||p.inputEl.addClass("invalid-input"),(h=this.statusEl)==null||h.addClass("invalid"),this.showErrorMessage(E.error),!1)}catch(E){return console.error("Token validation error:",E),(m=this.tokenEl)==null||m.inputEl.addClass("invalid-input"),(y=this.statusEl)==null||y.setText("Failed to validate token"),(v=this.statusEl)==null||v.addClass("invalid"),!1}}showValidTokenInfo(e){var i,s;let t=(i=this.statusEl)==null?void 0:i.createDiv({cls:"brat-token-details"});if(t&&(t.createDiv({text:"\u2713 Valid token",cls:"brat-token-status valid"}),(s=e.currentScopes)!=null&&s.length&&t.createDiv({text:`Scopes: ${e.currentScopes.join(", ")}`,cls:"brat-token-scopes"}),e.rateLimit&&t.createDiv({text:`Rate Limit: ${e.rateLimit.remaining}/${e.rateLimit.limit}`,cls:"brat-token-rate"}),e.expirationDate)){let o=new Date(e.expirationDate),r=Math.ceil((o.getTime()-Date.now())/(1e3*60*60*24));r<7&&t.createDiv({text:`\u26A0\uFE0F Token expires in ${r} days`,cls:"brat-token-warning"})}}showErrorMessage(e){var i,s,o;let t=(i=this.statusEl)==null?void 0:i.createDiv({cls:"brat-token-error"});if(t&&(t.createDiv({text:e.message}),e.details))switch(e.type){case"invalid_prefix":t.createDiv({text:`Valid prefixes: ${(s=e.details.validPrefixes)==null?void 0:s.join(", ")}`});break;case"insufficient_scope":t.createDiv({text:`Required scopes: ${(o=e.details.requiredScopes)==null?void 0:o.join(", ")}`});break}}};function J(n,e){let t=new DocumentFragment,i=document.createElement("a");if(i.textContent=n,i.href=`https://github.com/${n}`,i.target="_blank",t.appendChild(i),e){let s=document.createTextNode(e);t.appendChild(s)}return t}function Rt({prependText:n,url:e,text:t,appendText:i}){let s=new DocumentFragment,o=document.createElement("a");if(o.textContent=t,o.href=e,n){let r=document.createTextNode(n);s.appendChild(r)}if(s.appendChild(o),i){let r=document.createTextNode(i);s.appendChild(r)}return s}var Te=require("obsidian");function b(n,e,t=10,i){if(!n.settings.notificationsEnabled)return;let s=i?Te.Platform.isDesktop?"(click=dismiss, right-click=Info)":"(click=dismiss)":"",o=new Te.Notice(`BRAT
${e}
${s}`,t*1e3);i&&(o.noticeEl.oncontextmenu=()=>{i()})}var j=(n,e=!0)=>{let t=n.createEl("div");t.style.float="right",e?(t.style.padding="15px",t.style.paddingLeft="15px",t.style.paddingRight="15px",t.style.marginLeft="15px"):(t.style.padding="10px",t.style.paddingLeft="15px",t.style.paddingRight="15px");let i=t.createDiv("coffee");i.addClass("ex-twitter-span"),i.style.paddingLeft="10px";let s=i.createDiv();s.innerText="Learn more about my work at:",i.appendChild(s);let o=i.createEl("a",{href:"https://tfthacker.com"});return o.innerText="https://tfthacker.com",t};var At=require("obsidian"),ye=class extends At.SuggestModal{constructor(e,t,i,s,o){super(e),this.versions=i,this.selected=s,this.onChoose=o,this.setTitle("Select a version"),this.setPlaceholder(`Type to search for a version for ${t}`),this.setInstructions([{command:"\u2191\u2193",purpose:"Navigate versions"},{command:"\u21B5",purpose:"Select version"},{command:"esc",purpose:"Dismiss modal"}])}getSuggestions(e){let t=e.toLowerCase();return this.versions.filter(i=>i.version.toLowerCase().contains(t))}renderSuggestion(e,t){t.createEl("div",{text:`${e.version} ${e.prerelease?"(Prerelease)":""}`})}onChooseSuggestion(e){this.onChoose(e.version)}onNoSuggestion(){this.onChoose(this.selected?this.selected:""),this.close()}};var M=class extends N.Modal{constructor(t,i,s=!1,o=!1,r="",a="",c=""){super(t.app);this.versionSetting=null;this.repositoryAddressEl=null;this.tokenInputEl=null;this.validateButton=null;this.validator=null;this.addPluginButton=null;this.cancelButton=null;this.plugin=t,this.betaPlugins=i,this.address=r,this.version=a,this.privateApiKey=c,this.usePrivateApiKey=!(c===""||c===void 0),this.openSettingsTabAfterwards=s,this.updateVersion=o,this.enableAfterInstall=t.settings.enableAfterInstall}async submitForm(){var o,r,a,c,l,u,p,h;if(this.address==="")return;let t=we(this.address),i=this.plugin.settings.pluginSubListFrozenVersion.find(m=>m.repo===t);if(i){await this.betaPlugins.addPlugin(t,!1,!1,!1,this.version,!0,this.enableAfterInstall,this.usePrivateApiKey?this.privateApiKey:void 0)&&(i.version=this.version,i.token=this.usePrivateApiKey?this.privateApiKey||"":void 0,await this.plugin.saveSettings(),this.close()),(o=this.cancelButton)==null||o.setDisabled(!1),(r=this.addPluginButton)==null||r.setDisabled(!1),(a=this.addPluginButton)==null||a.setButtonText("Add Plugin"),(c=this.versionSetting)==null||c.setDisabled(!1);return}if(!this.version&&yt(this.plugin,t)){b(this.plugin,"This plugin is already in the list for beta testing",10);return}await this.betaPlugins.addPlugin(t,!1,!1,!1,this.version,!1,this.enableAfterInstall,this.usePrivateApiKey?this.privateApiKey:void 0)&&this.close(),(l=this.cancelButton)==null||l.setDisabled(!1),(u=this.addPluginButton)==null||u.setDisabled(!1),(p=this.addPluginButton)==null||p.setButtonText("Add Plugin"),(h=this.versionSetting)==null||h.setDisabled(!1)}updateVersionDropdown(t,i,s=""){t.clear(),i.length<20||N.Platform.isMobile?t.addDropdown(r=>{r.addOption("","Select a version"),r.addOption("latest","Latest version");for(let a of i)r.addOption(a.version,`${a.version} ${a.prerelease?"(Prerelease)":""}`);r.setValue(s),r.onChange(a=>{this.version=a,this.updateAddButtonState()}),r.selectEl.addClass("brat-version-selector"),r.selectEl.style.width="100%"}):t.addButton(r=>{r.setButtonText(s==="latest"?"Latest version":s||"Select a version...").setClass("brat-version-selector").setClass("button").onClick(a=>{a.preventDefault();let l=[{version:"latest",prerelease:!1},...i];new ye(this.app,this.address,l,s,p=>{this.version=p,r.setButtonText(p==="latest"?"Latest version":p||"Select a version..."),this.updateAddButtonState()}).open()})})}updateAddButtonState(){this.addPluginButton&&this.addPluginButton.setDisabled(this.version==="")}onOpen(){let t=this.contentEl.createEl("h4");this.address?(t.appendText("Change plugin version: "),t.appendChild(J(this.address))):t.setText("Github repository for beta plugin:"),this.contentEl.createEl("form",{},i=>{i.addClass("brat-modal"),(!this.address||!this.updateVersion)&&new N.Setting(i).setClass("repository-setting").then(c=>{c.addText(l=>{this.repositoryAddressEl=l,l.setPlaceholder("Repository (example: https://github.com/GitubUserName/repository-name)"),l.setValue(this.address),l.onChange(u=>{var p,h;this.address=we(u.trim()),this.version!==""&&(!this.address||!this.isGitHubRepositoryMatch(this.address))&&this.versionSetting&&(this.updateVersionDropdown(this.versionSetting,[]),this.versionSetting.settingEl.classList.add("disabled-setting"),this.versionSetting.setDisabled(!0),l.inputEl.classList.remove("valid-repository"),l.inputEl.classList.remove("invalid-repository")),this.version||(this.isGitHubRepositoryMatch(this.address)?(p=this.addPluginButton)==null||p.setDisabled(!1):(h=this.addPluginButton)==null||h.setDisabled(!0))}),l.inputEl.addEventListener("keydown",async u=>{var p,h,m;u.key==="Enter"&&(this.address&&(this.updateVersion&&this.version!==""||!this.updateVersion)&&(u.preventDefault(),(p=this.addPluginButton)==null||p.setDisabled(!0),(h=this.cancelButton)==null||h.setDisabled(!0),(m=this.versionSetting)==null||m.setDisabled(!0),this.submitForm()),await this.updateRepositoryVersionInfo(this.version,s))}),this.updateVersion&&l.inputEl.addEventListener("blur",async()=>{await this.updateRepositoryVersionInfo(this.version,s)}),c.setDesc("Repository"),l.inputEl.style.width="100%"})});let s=i.createDiv("validation-status");this.address||s.setText("Enter a GitHub repository address to validate it."),this.versionSetting=new N.Setting(i).setClass("version-setting").setClass("disabled-setting"),this.updateVersionDropdown(this.versionSetting,[],this.version),this.versionSetting.setDisabled(!0),i.createDiv("modal-button-container",a=>{a.createEl("label",{cls:"mod-checkbox"},l=>{let u=l.createEl("input",{attr:{tabindex:-1},type:"checkbox"});u.checked=this.usePrivateApiKey,u.addEventListener("click",()=>{var p,h;this.usePrivateApiKey=u.checked,(p=this.validateButton)==null||p.setDisabled(!this.usePrivateApiKey||!this.validToken),(h=this.tokenInputEl)==null||h.setDisabled(!this.usePrivateApiKey),(!this.usePrivateApiKey||this.validToken&&this.usePrivateApiKey)&&this.updateRepositoryVersionInfo(this.version,s)}),l.appendText("Use token for this repository")}),this.tokenInputEl=new N.TextComponent(a).setPlaceholder("GitHub API key for private repository").setValue(this.privateApiKey).setDisabled(!this.usePrivateApiKey).onChange(async l=>{var u,p,h;this.privateApiKey=l.trim(),this.privateApiKey?((u=this.validateButton)==null||u.setButtonText("Validate"),(p=this.validateButton)==null||p.setDisabled(!1)):(h=this.validateButton)==null||h.setDisabled(!0)}),this.tokenInputEl.inputEl.type="password",i.createDiv("brat-token-validation-status")&&this.tokenInputEl.inputEl.parentElement&&(this.validateButton=new N.ButtonComponent(this.tokenInputEl.inputEl.parentElement).setButtonText("Validate").setDisabled(this.privateApiKey==="").onClick(async l=>{var u,p,h,m,y;l.preventDefault(),this.validToken=await((u=this.validator)==null?void 0:u.validateToken(this.privateApiKey,this.address)),this.validToken?((m=this.validateButton)==null||m.setButtonText("Valid"),(y=this.validateButton)==null||y.setDisabled(!0),this.address&&await this.updateRepositoryVersionInfo(this.version,s)):((p=this.validateButton)==null||p.setButtonText("Invalid"),(h=this.validateButton)==null||h.setDisabled(!1))}).then(async()=>{var l,u,p;this.validator=new G(this.tokenInputEl),this.validToken=await((l=this.validator)==null?void 0:l.validateToken(this.privateApiKey,this.address)),this.validToken&&this.usePrivateApiKey&&((u=this.validateButton)==null||u.setButtonText("Valid"),(p=this.validateButton)==null||p.setDisabled(!0))}))}),i.createDiv("modal-button-container",a=>{var c;a.createEl("label",{cls:"mod-checkbox"},l=>{let u=l.createEl("input",{attr:{tabindex:-1},type:"checkbox"});u.checked=this.enableAfterInstall,u.addEventListener("click",()=>{this.enableAfterInstall=u.checked}),l.appendText("Enable after installing the plugin")}),this.cancelButton=new N.ButtonComponent(a).setButtonText("Never mind").setClass("mod-cancel").onClick(l=>{this.close()}),this.addPluginButton=new N.ButtonComponent(a).setButtonText(this.updateVersion&&this.address?"Change version":"Add plugin").setCta().onClick(l=>{var u,p,h,m;l.preventDefault(),this.address!==""&&(this.updateVersion&&this.version!==""||!this.updateVersion)&&((u=this.addPluginButton)==null||u.setDisabled(!0),(p=this.addPluginButton)==null||p.setButtonText("Installing \u2026"),(h=this.cancelButton)==null||h.setDisabled(!0),(m=this.versionSetting)==null||m.setDisabled(!0),this.submitForm())}),(this.updateVersion||this.address==="")&&((c=this.addPluginButton)==null||c.setDisabled(!0))});let o=i.createDiv();o.style.borderTop="1px solid #ccc",o.style.marginTop="30px";let r=o.createSpan();r.innerHTML="BRAT by <a href='https://bit.ly/o42-twitter'>TFTHacker</a>",r.style.fontStyle="italic",o.appendChild(r),j(o,!1),window.setTimeout(()=>{let a=i.querySelectorAll(".brat-modal .setting-item-info");for(let c of Array.from(a))c.remove()},50),i.addEventListener("submit",a=>{var c;a.preventDefault(),this.address!==""&&(this.updateVersion&&this.version!==""||!this.updateVersion)&&((c=this.addPluginButton)==null||c.setDisabled(!0),this.submitForm())})}),this.address&&window.setTimeout(async()=>{await this.updateRepositoryVersionInfo(this.version)},100)}async updateRepositoryVersionInfo(t="",i){var r,a,c,l;let s=this.repositoryAddressEl;if(this.plugin.settings.debuggingMode&&console.log(`[BRAT] Updating version dropdown for ${this.address} with selected version ${t}`),!this.address){i==null||i.setText("Repository address is required."),i==null||i.addClass("validation-status-error");return}i==null||i.setText("Validating repository address..."),i==null||i.removeClass("validation-status-error"),this.versionSetting&&this.updateVersion&&this.updateVersionDropdown(this.versionSetting,[],t);let o=we(this.address);try{let u=await ht(o,this.plugin.settings.debuggingMode,this.usePrivateApiKey?this.privateApiKey:this.plugin.settings.personalAccessToken);u&&u.length>0?(s==null||s.inputEl.classList.remove("invalid-repository"),s==null||s.inputEl.classList.add("valid-repository"),i==null||i.setText(""),this.versionSetting&&(this.versionSetting.settingEl.classList.remove("disabled-setting"),this.versionSetting.setDisabled(!1),this.updateVersionDropdown(this.versionSetting,u,t))):(s==null||s.inputEl.classList.remove("valid-repository"),s==null||s.inputEl.classList.add("invalid-repository"),this.versionSetting&&(this.versionSetting.settingEl.classList.add("disabled-setting"),this.versionSetting.setDisabled(!0),(r=this.addPluginButton)==null||r.setDisabled(!0)))}catch(u){if(u instanceof C&&(s==null||s.inputEl.classList.remove("valid-repository"),s==null||s.inputEl.classList.add("validation-error"),i==null||i.setText(`GitHub API rate limit exceeded. Try again in ${u.getMinutesToReset()} minutes.`),this.versionSetting&&(this.versionSetting.settingEl.classList.add("disabled-setting"),this.versionSetting.setDisabled(!0),(a=this.addPluginButton)==null||a.setDisabled(!0)),b(this.plugin,`${u.message} Consider adding a personal access token in BRAT settings for higher limits. See documentation for details.`,20,()=>{window.open("https://github.com/TfTHacker/obsidian42-brat/blob/main/BRAT-DEVELOPER-GUIDE.md#github-api-rate-limits")})),u instanceof D){let p=u;switch(p.status){case 404:i==null||i.setText("Repository not found. Check the address or provide a valid token for access to a private repository.");break;case 403:i==null||i.setText("Access denied. Check your personal access token.");break;default:i==null||i.setText(`Error: ${p.message}`);break}i==null||i.addClass("validation-status-error"),(c=this.versionSetting)==null||c.setDisabled(!0),(l=this.addPluginButton)==null||l.setDisabled(!0),b(this.plugin,`${p.message} `,20)}}}onClose(){this.openSettingsTabAfterwards&&(this.plugin.app.setting.open(),this.plugin.app.setting.openTabById(this.plugin.APP_ID))}isGitHubRepositoryMatch(t){let i=t.trim().replace(/\.git$/,"").toLowerCase();return/^(?:https?:\/\/github\.com\/)?([a-zA-Z0-9._-]+)\/([a-zA-Z0-9._-]+)$/i.test(i)}};var Nt=require("obsidian");async function ve(){try{let n=await(0,Nt.requestUrl)(`https://obsidian.md/?${Math.random()}`);return n.status>=200&&n.status<300}catch(n){return!1}}var Lt=xe(),Pe=Fe(),Q=class{constructor(e){this.plugin=e}displayAddNewPluginModal(e=!1,t=!1,i="",s="",o=""){new M(this.plugin,this,e,t,i,s,o).open()}async validateRepository(e,t=!1,i=!1,s="",o=""){try{let a=await Oe(e,this.plugin.settings.debuggingMode,o||this.plugin.settings.personalAccessToken),c=await Be(e,s,t,this.plugin.settings.debuggingMode,a,o||this.plugin.settings.personalAccessToken);if(!c)return i&&(b(this.plugin,`${e}
This does not seem to be an obsidian plugin with valid releases, as there are no releases available.`,15),console.error("BRAT: validateRepository",e,t,i)),null;let l=await Y(c,"manifest.json",this.plugin.settings.debuggingMode,a,o||this.plugin.settings.personalAccessToken);if(!l)return i&&(b(this.plugin,`${e}
This does not seem to be an obsidian plugin, as there is no manifest.json file.`,15),console.error("BRAT: validateRepository",e,t,i)),null;let u=JSON.parse(l);if(!("id"in u))return i&&b(this.plugin,`${e}
The plugin id attribute for the release is missing from the manifest file`,15),null;if(!("version"in u))return i&&b(this.plugin,`${e}
The version attribute for the release is missing from the manifest file`,15),null;let p=Pe(c.tag_name,{includePrerelease:!0,loose:!0}),h=Pe(u.version,{includePrerelease:!0,loose:!0});return Lt(p,h)!==0&&(i&&b(this.plugin,`${e}
Version mismatch detected:
Release tag version: ${c.tag_name}
Manifest version: ${u.version}

The release tag version will be used to ensure consistency.`,15),u.version=p.version),u}catch(a){if(a instanceof C){let c=`GitHub API rate limit exceeded. Reset in ${a.getMinutesToReset()} minutes.`;throw i&&b(this.plugin,c,15),console.error(`BRAT: validateRepository ${a}`),b(this.plugin,`${a.message} Consider adding a personal access token in BRAT settings for higher limits. See documentation for details.`,20,()=>{window.open("https://github.com/TfTHacker/obsidian42-brat/blob/main/BRAT-DEVELOPER-GUIDE.md#github-api-rate-limits")}),a}if(a instanceof D)throw i&&(a.status===401?b(this.plugin,`${e}
GitHub API Authentication error. Please verify that your personal access token is valid and set correctly.`,15):b(this.plugin,`${e}
GitHub API error ${a.status}: ${a.message}`,15)),console.error(`BRAT: validateRepository ${a}`),a;return i&&b(this.plugin,`${e}
Unspecified error encountered: ${a}, verify debug for more information.`,15),null}}async getAllReleaseFiles(e,t,i,s="",o=""){let r=await Oe(e,this.plugin.settings.debuggingMode,o),a=await Be(e,s,i,this.plugin.settings.debuggingMode,r,o||this.plugin.settings.personalAccessToken);if(!a)return Promise.reject("No release found");let c=i||s!=="";return console.log({reallyGetManifestOrNot:c,version:a.tag_name}),{mainJs:await Y(a,"main.js",this.plugin.settings.debuggingMode,r,o||this.plugin.settings.personalAccessToken),manifest:c?await Y(a,"manifest.json",this.plugin.settings.debuggingMode,r,o||this.plugin.settings.personalAccessToken):"",styles:await Y(a,"styles.css",this.plugin.settings.debuggingMode,r,o||this.plugin.settings.personalAccessToken)}}async writeReleaseFilesToPluginFolder(e,t){var o,r;let i=`${(0,F.normalizePath)(`${this.plugin.app.vault.configDir}/plugins/${e}`)}/`,{adapter:s}=this.plugin.app.vault;await s.exists(i)||await s.mkdir(i),await s.write(`${i}main.js`,(o=t.mainJs)!=null?o:""),await s.write(`${i}manifest.json`,(r=t.manifest)!=null?r:""),t.styles&&await s.write(`${i}styles.css`,t.styles)}async addPlugin(e,t=!1,i=!1,s=!1,o="",r=!1,a=this.plugin.settings.enableAfterInstall,c=""){try{this.plugin.settings.debuggingMode&&console.log("BRAT: addPlugin",e,t,i,s,o,r,a,c?"private":"public");let l=10,u=await this.validateRepository(e,!0,!0,o,c),p=!!u;if(p||(u=await this.validateRepository(e,!1,!0,o,c)),u===null){let m=`${e}
A manifest.json file does not exist in the latest release of the repository. This plugin cannot be installed.`;return await this.plugin.log(m,!0),b(this.plugin,m,l),!1}if(!Object.hasOwn(u,"version")){let m=`${e}
The manifest.json file in the latest release or pre-release of the repository does not have a version number in the file. This plugin cannot be installed.`;return await this.plugin.log(m,!0),b(this.plugin,m,l),!1}if(Object.hasOwn(u,"minAppVersion")&&!(0,F.requireApiVersion)(u.minAppVersion)){let m=`Plugin: ${e}

The manifest.json for this plugin indicates that the Obsidian version of the app needs to be ${u.minAppVersion}, but this installation of Obsidian is ${F.apiVersion}. 

You will need to update your Obsidian to use this plugin or contact the plugin developer for more information.`;return await this.plugin.log(m,!0),b(this.plugin,m,30),!1}let h=async()=>{let m=await this.getAllReleaseFiles(e,u,p,o,c);if(console.log("rFiles",m),(p||m.manifest==="")&&(m.manifest=JSON.stringify(u)),this.plugin.settings.debuggingMode&&console.log("BRAT: rFiles.manifest",p,m),m.mainJs===null){let y=`${e}
The release is not complete and cannot be download. main.js is missing from the Release`;return await this.plugin.log(y,!0),b(this.plugin,y,l),null}return m};if(!t||r){let m=await h();if(m===null)return!1;if(await this.writeReleaseFilesToPluginFolder(u.id,m),r||Tt(this.plugin,e,o,c),a){let{plugins:y}=this.plugin.app,v=(0,F.normalizePath)(`${y.getPluginFolder()}/${u.id}`);await y.loadManifest(v),await y.enablePluginAndSave(u.id)}if(await this.plugin.app.plugins.loadManifests(),r)await this.reloadPlugin(u.id),await this.plugin.log(`${e} reinstalled`,!0),b(this.plugin,`${e}
Plugin has been reinstalled and reloaded with version ${u.version}`,l);else{let y=o===""?"":` (version: ${o})`,v=`${e}${y}
The plugin has been registered with BRAT.`;a||(v+=" You may still need to enable it the Community Plugin List."),await this.plugin.log(v,!0),b(this.plugin,v,l)}}else{let m=`${this.plugin.app.vault.configDir}/plugins/${u.id}/`,y="";try{y=await this.plugin.app.vault.adapter.read(`${m}manifest.json`)}catch(R){if(R.errno===-4058||R.errno===-2)return await this.addPlugin(e,!1,p,!1,o,!1,a,c),!0;console.log("BRAT - Local Manifest Load",u.id,JSON.stringify(R,null,2))}if(o!==""&&o!=="latest")return b(this.plugin,`The version of ${e} is frozen, not updating.`,3),!1;let v=await JSON.parse(y),E=Pe(v.version,{includePrerelease:!0,loose:!0}),T=Pe(u.version,{includePrerelease:!0,loose:!0});if(Lt(E,T)===-1){let R=await h();if(R===null)return!1;if(i){let ce=`There is an update available for ${u.id} from version ${v.version} to ${u.version}. `;return await this.plugin.log(`${ce}[Release Info](https://github.com/${e}/releases/tag/${u.version})`,!0),b(this.plugin,ce,30,()=>{u&&window.open(`https://github.com/${e}/releases/tag/${u.version}`)}),!1}await this.writeReleaseFilesToPluginFolder(u.id,R),await this.plugin.app.plugins.loadManifests(),await this.reloadPlugin(u.id);let $=`${u.id}
Plugin has been updated from version ${v.version} to ${u.version}. `;return await this.plugin.log(`${$}[Release Info](https://github.com/${e}/releases/tag/${u.version})`,!0),b(this.plugin,$,30,()=>{u&&window.open(`https://github.com/${e}/releases/tag/${u.version}`)}),!0}return s&&b(this.plugin,`No update available for ${e}`,3),!0}}catch(l){console.error(`BRAT: Error adding plugin ${e}:`,{error:l,updatePluginFiles:t,seeIfUpdatedOnly:i,specifyVersion:o,forceReinstall:r});let u=l instanceof Error?l.message:"Unknown error occurred";return await this.plugin.log(`Error ${t?"updating":"adding"} plugin ${e}: ${u}`,!0),!1}return!0}async reloadPlugin(e){let{plugins:t}=this.plugin.app;try{await t.disablePlugin(e),await t.enablePlugin(e)}catch(i){this.plugin.settings.debuggingMode&&console.log("reload plugin",i)}}async updatePlugin(e,t=!1,i=!1,s=!1,o=""){let r=await this.addPlugin(e,!0,t,i,"",s,!1,o);return!r&&!t&&b(this.plugin,`${e}
Update of plugin failed.`),r}async checkForPluginUpdatesAndInstallUpdates(e=!1,t=!1){var a,c;if(!await ve()){console.log("BRAT: No internet detected.");return}let i,s="Checking for plugin updates STARTED";await this.plugin.log(s,!0),e&&this.plugin.settings.notificationsEnabled&&(i=new F.Notice(`BRAT
${s}`,3e4));let o=new Map(this.plugin.settings.pluginSubListFrozenVersion.map(l=>[l.repo,{version:l.version,token:l.token}]));for(let l of this.plugin.settings.pluginList)o.has(l)&&((a=o.get(l))==null?void 0:a.version)!=="latest"||await this.updatePlugin(l,t,!1,!1,(c=o.get(l))==null?void 0:c.token);let r="Checking for plugin updates COMPLETED";await this.plugin.log(r,!0),e&&(i&&i.hide(),b(this.plugin,r,10))}deletePlugin(e){let t=`Removed ${e} from BRAT plugin list`;this.plugin.log(t,!0),this.plugin.settings.pluginList=this.plugin.settings.pluginList.filter(i=>i!==e),this.plugin.settings.pluginSubListFrozenVersion=this.plugin.settings.pluginSubListFrozenVersion.filter(i=>i.repo!==e),this.plugin.saveSettings()}getEnabledDisabledPlugins(e){let t=this.plugin.app.plugins,i=Object.values(t.manifests),s=Object.values(t.plugins).map(o=>o.manifest);return e?i.filter(o=>s.find(r=>o.id===r.id)):i.filter(o=>!s.find(r=>o.id===r.id))}};var V=require("obsidian");var Z=async(n,e,t)=>{let i=await _(e,!0,n.settings.debuggingMode);if(i||(i=await _(e,!1,n.settings.debuggingMode)),!i)return b(n,"There is no theme.css or theme-beta.css file in the root path of this repository, so there is no theme to install."),!1;let s=await wt(e,n.settings.debuggingMode);if(!s)return b(n,"There is no manifest.json file in the root path of this repository, so theme cannot be installed."),!1;let o=await JSON.parse(s),r=(0,V.normalizePath)(Si(n)+o.name),{adapter:a}=n.app.vault;await a.exists(r)||await a.mkdir(r),await a.write((0,V.normalizePath)(`${r}/theme.css`),i),await a.write((0,V.normalizePath)(`${r}/manifest.json`),s),It(n,e,K(i));let c="";return t?(vt(n,e,i),c=`${o.name} theme installed from ${e}. `,setTimeout(()=>{n.app.customCss.setTheme(o.name)},500)):c=`${o.name} theme updated from ${e}.`,n.log(`${c}[Theme Info](https://github.com/${e})`,!1),b(n,c,20,()=>{window.open(`https://github.com/${e}`)}),!0},z=async(n,e)=>{if(!await ve()){console.log("BRAT: No internet detected.");return}let t,i="Checking for beta theme updates STARTED";await n.log(i,!0),e&&n.settings.notificationsEnabled&&(t=new V.Notice(`BRAT
${i}`,3e4));for(let o of n.settings.themesList){let r=await W(o.repo,!0,n.settings.debuggingMode);r==="0"&&(r=await W(o.repo,!1,n.settings.debuggingMode)),console.log("BRAT: lastUpdateOnline",r),r!==o.lastUpdate&&await Z(n,o.repo,!1)}let s="Checking for beta theme updates COMPLETED";(async()=>await n.log(s,!0))(),e&&(n.settings.notificationsEnabled&&t&&t.hide(),b(n,s))},Ie=(n,e)=>{n.settings.themesList=n.settings.themesList.filter(i=>i.repo!==e),n.saveSettings();let t=`Removed ${e} from BRAT themes list and will no longer be updated. However, the theme files still exist in the vault. To remove them, go into Settings > Appearance and remove the theme.`;n.log(t,!0),b(n,t)},Si=n=>`${(0,V.normalizePath)(`${n.app.vault.configDir}/themes`)}/`;var H=require("obsidian");var O=class extends H.Modal{constructor(e,t=!1){super(e.app),this.plugin=e,this.address="",this.openSettingsTabAfterwards=t}async submitForm(){if(this.address==="")return;let e=this.address.replace("https://github.com/","");if(Pt(this.plugin,e)){b(this.plugin,"This theme is already in the list for beta testing",10);return}await Z(this.plugin,e,!0)&&this.close()}onOpen(){this.contentEl.createEl("h4",{text:"Github repository for beta theme:"}),this.contentEl.createEl("form",{},e=>{e.addClass("brat-modal"),new H.Setting(e).addText(s=>{s.setPlaceholder("Repository (example: https://github.com/GitubUserName/repository-name"),s.setValue(this.address),s.onChange(o=>{this.address=o.trim()}),s.inputEl.addEventListener("keydown",o=>{o.key==="Enter"&&this.address!==" "&&(o.preventDefault(),this.submitForm())}),s.inputEl.style.width="100%",window.setTimeout(()=>{let o=document.querySelector(".setting-item-info");o&&o.remove(),s.inputEl.focus()},10)}),e.createDiv("modal-button-container",s=>{new H.ButtonComponent(s).setButtonText("Never mind").onClick(()=>{this.close()}),new H.ButtonComponent(s).setButtonText("Add theme").setCta().onClick(o=>{o.preventDefault(),console.log("Add theme button clicked"),this.address!==""&&this.submitForm()})});let t=e.createDiv();t.style.borderTop="1px solid #ccc",t.style.marginTop="30px";let i=t.createSpan();i.innerHTML="BRAT by <a href='https://bit.ly/o42-twitter'>TFTHacker</a>",i.style.fontStyle="italic",t.appendChild(i),j(t,!1),window.setTimeout(()=>{let s=e.querySelectorAll(".brat-modal .setting-item-info");for(let o of Array.from(s))o.remove()},50)})}onClose(){this.openSettingsTabAfterwards&&(this.plugin.app.setting.openTab(),this.plugin.app.setting.openTabById(this.plugin.APP_ID))}};var Ct=require("obsidian"),L=class extends Ct.FuzzySuggestModal{constructor(t){super(t.app);this.data=[];this.scope.register(["Shift"],"Enter",i=>{this.enterTrigger(i)}),this.scope.register(["Ctrl"],"Enter",i=>{this.enterTrigger(i)})}setSuggesterData(t){this.data=t}display(t){this.callbackFunction=t,this.open()}getItems(){return this.data}getItemText(t){return t.display}onChooseItem(){}renderSuggestion(t,i){i.createEl("div",{text:t.item.display})}enterTrigger(t){var o;let i=(o=document.querySelector(".suggestion-item.is-selected div"))==null?void 0:o.textContent,s=this.data.find(r=>r.display===i);s&&(this.invokeCallback(s,t),this.close())}onChooseSuggestion(t,i){this.invokeCallback(t.item,i)}invokeCallback(t,i){typeof this.callbackFunction=="function"&&this.callbackFunction(t,i)}};var ee=class{constructor(e){this.bratCommands=[{id:"AddBetaPlugin",icon:"BratIcon",name:"Plugins: Add a beta plugin for testing (with or without version)",showInRibbon:!0,callback:()=>{this.plugin.betaPlugins.displayAddNewPluginModal(!1,!0)}},{id:"checkForUpdatesAndUpdate",icon:"BratIcon",name:"Plugins: Check for updates to all beta plugins and UPDATE",showInRibbon:!0,callback:async()=>{await this.plugin.betaPlugins.checkForPluginUpdatesAndInstallUpdates(!0,!1)}},{id:"checkForUpdatesAndDontUpdate",icon:"BratIcon",name:"Plugins: Only check for updates to beta plugins, but don't Update",showInRibbon:!0,callback:async()=>{await this.plugin.betaPlugins.checkForPluginUpdatesAndInstallUpdates(!0,!0)}},{id:"updateOnePlugin",icon:"BratIcon",name:"Plugins: Choose a single plugin version to update",showInRibbon:!0,callback:()=>{let e=new Map(this.plugin.settings.pluginSubListFrozenVersion.map(s=>[s.repo,{version:s.version,token:s.token}])),t=Object.values(this.plugin.settings.pluginList).filter(s=>{let o=e.get(s);return!(o!=null&&o.version)||o.version==="latest"}).map(s=>{let o=e.get(s);return{display:s,info:s}}),i=new L(this.plugin);i.setSuggesterData(t),i.display(s=>{let o=`Checking for updates for ${s.info}`,r=e.get(s.info);this.plugin.log(o,!0),b(this.plugin,`
${o}`,3),this.plugin.betaPlugins.updatePlugin(s.info,!1,!0,!1,r==null?void 0:r.token)})}},{id:"reinstallOnePlugin",icon:"BratIcon",name:"Plugins: Choose a single plugin to reinstall",showInRibbon:!0,callback:()=>{let e=new Set(this.plugin.settings.pluginSubListFrozenVersion.map(s=>s.repo)),t=Object.values(this.plugin.settings.pluginList).filter(s=>!e.has(s)).map(s=>({display:s,info:s})),i=new L(this.plugin);i.setSuggesterData(t),i.display(s=>{let o=`Reinstalling ${s.info}`;b(this.plugin,`
${o}`,3),this.plugin.log(o,!0),this.plugin.betaPlugins.updatePlugin(s.info,!1,!1,!0)})}},{id:"restartPlugin",icon:"BratIcon",name:"Plugins: Restart a plugin that is already installed",showInRibbon:!0,callback:()=>{let e=Object.values(this.plugin.app.plugins.manifests).map(i=>({display:i.id,info:i.id})),t=new L(this.plugin);t.setSuggesterData(e),t.display(i=>{b(this.plugin,`${i.info}
Plugin reloading .....`,5),this.plugin.betaPlugins.reloadPlugin(i.info)})}},{id:"disablePlugin",icon:"BratIcon",name:"Plugins: Disable a plugin - toggle it off",showInRibbon:!0,callback:()=>{let e=this.plugin.betaPlugins.getEnabledDisabledPlugins(!0).map(i=>({display:`${i.name} (${i.id})`,info:i.id})),t=new L(this.plugin);t.setSuggesterData(e),t.display(i=>{this.plugin.log(`${i.display} plugin disabled`,!1),this.plugin.settings.debuggingMode&&console.log(i.info),this.plugin.app.plugins.disablePluginAndSave(i.info)})}},{id:"enablePlugin",icon:"BratIcon",name:"Plugins: Enable a plugin - toggle it on",showInRibbon:!0,callback:()=>{let e=this.plugin.betaPlugins.getEnabledDisabledPlugins(!1).map(i=>({display:`${i.name} (${i.id})`,info:i.id})),t=new L(this.plugin);t.setSuggesterData(e),t.display(i=>{this.plugin.log(`${i.display} plugin enabled`,!1),this.plugin.app.plugins.enablePluginAndSave(i.info)})}},{id:"openGitHubZRepository",icon:"BratIcon",name:"Plugins: Open the GitHub repository for a plugin",showInRibbon:!0,callback:async()=>{let e=await ft(this.plugin.settings.debuggingMode);if(e){let t=Object.values(e).map(o=>({display:`Plugin: ${o.name}  (${o.repo})`,info:o.repo})),i=Object.values(this.plugin.settings.pluginList).map(o=>({display:`BRAT: ${o}`,info:o}));for(let o of t)i.push(o);let s=new L(this.plugin);s.setSuggesterData(i),s.display(o=>{o.info&&window.open(`https://github.com/${o.info}`)})}}},{id:"openGitHubRepoTheme",icon:"BratIcon",name:"Themes: Open the GitHub repository for a theme (appearance)",showInRibbon:!0,callback:async()=>{let e=await bt(this.plugin.settings.debuggingMode);if(e){let t=Object.values(e).map(s=>({display:`Theme: ${s.name}  (${s.repo})`,info:s.repo})),i=new L(this.plugin);i.setSuggesterData(t),i.display(s=>{s.info&&window.open(`https://github.com/${s.info}`)})}}},{id:"opentPluginSettings",icon:"BratIcon",name:"Plugins: Open Plugin Settings Tab",showInRibbon:!0,callback:()=>{let e=this.plugin.app.setting,t=Object.values(e.pluginTabs).map(o=>({display:`Plugin: ${o.name}`,info:o.id})),i=new L(this.plugin),s=Object.values(e.settingTabs).map(o=>({display:`Core: ${o.name}`,info:o.id}));for(let o of t)s.push(o);i.setSuggesterData(s),i.display(o=>{e.open(),e.openTabById(o.info)})}},{id:"GrabBetaTheme",icon:"BratIcon",name:"Themes: Grab a beta theme for testing from a Github repository",showInRibbon:!0,callback:()=>{new O(this.plugin).open()}},{id:"updateBetaThemes",icon:"BratIcon",name:"Themes: Update beta themes",showInRibbon:!0,callback:async()=>{await z(this.plugin,!0)}},{id:"allCommands",icon:"BratIcon",name:"All Commands list",showInRibbon:!1,callback:()=>{this.ribbonDisplayCommands()}}];this.plugin=e;for(let t of this.bratCommands)this.plugin.addCommand({id:t.id,name:t.name,icon:t.icon,callback:()=>{t.callback()}})}ribbonDisplayCommands(){let e=[];for(let r of this.bratCommands)r.showInRibbon&&e.push({display:r.name,info:r.callback});let t=new L(this.plugin),i=this.plugin.app.setting,s=Object.values(i.settingTabs).map(r=>({display:`Core: ${r.name}`,info:()=>{i.open(),i.openTabById(r.id)}})),o=Object.values(i.pluginTabs).map(r=>({display:`Plugin: ${r.name}`,info:()=>{i.open(),i.openTabById(r.id)}}));e.push({display:"---- Core Plugin Settings ----",info:()=>{this.ribbonDisplayCommands()}});for(let r of s)e.push(r);e.push({display:"---- Plugin Settings ----",info:()=>{this.ribbonDisplayCommands()}});for(let r of o)e.push(r);t.setSuggesterData(e),t.display(r=>{typeof r.info=="function"&&r.info()})}};var I=require("obsidian");var Re=class extends I.PluginSettingTab{constructor(t,i){super(t,i);this.accessTokenSetting=null;this.accessTokenButton=null;this.tokenInfo=null;this.validator=null;this.plugin=i}display(){let{containerEl:t}=this;t.empty(),t.addClass("brat-settings"),new I.Setting(t).setName("Auto-enable plugins after installation").setDesc('If enabled beta plugins will be automatically enabled after installtion by default. Note: you can toggle this on and off for each plugin in the "Add Plugin" form.').addToggle(s=>{s.setValue(this.plugin.settings.enableAfterInstall).onChange(async o=>{this.plugin.settings.enableAfterInstall=o,await this.plugin.saveSettings()})}),new I.Setting(t).setName("Auto-update plugins at startup").setDesc("If enabled all beta plugins will be checked for updates each time Obsidian starts. Note: this does not update frozen version plugins.").addToggle(s=>{s.setValue(this.plugin.settings.updateAtStartup).onChange(async o=>{this.plugin.settings.updateAtStartup=o,await this.plugin.saveSettings()})}),new I.Setting(t).setName("Auto-update themes at startup").setDesc("If enabled all beta themes will be checked for updates each time Obsidian starts.").addToggle(s=>{s.setValue(this.plugin.settings.updateThemesAtStartup).onChange(async o=>{this.plugin.settings.updateThemesAtStartup=o,await this.plugin.saveSettings()})}),j(t,!0),t.createEl("hr"),new I.Setting(t).setName("Beta plugin list").setHeading(),t.createEl("div",{text:'The following is a list of beta plugins added via the command "Add a beta plugin for testing". You can chose to add the latest version or a frozen version. A frozen version is a specific release of a plugin based on its release tag.'}),t.createEl("p"),t.createEl("div",{text:"Click the 'Edit' button next to a plugin to change the installed version and the x button next to a plugin to remove it from the list."}),t.createEl("p"),t.createEl("span").createEl("b",{text:"Note: "}),t.createSpan({text:"Removing from the list does not delete the plugin, this should be done from the Community Plugins tab in Settings."}),new I.Setting(t).addButton(s=>{s.setButtonText("Add beta plugin").setCta().onClick(()=>{this.plugin.betaPlugins.displayAddNewPluginModal(!0,!0)})});let i=new Map(this.plugin.settings.pluginSubListFrozenVersion.map(s=>[s.repo,{version:s.version,token:s.token}]));for(let s of this.plugin.settings.pluginList){let o=i.get(s),r=new I.Setting(t).setName(J(s)).setDesc(o!=null&&o.version?` Tracked version: ${o.version} ${o.version==="latest"?"":"(frozen)"}`:"");(!(o!=null&&o.version)||o.version==="latest")&&r.addButton(a=>{a.setIcon("sync").setTooltip("Check and update plugin").onClick(async()=>{await this.plugin.betaPlugins.updatePlugin(s,!1,!0,!1,o==null?void 0:o.token)})}),r.addButton(a=>{a.setIcon("edit").setTooltip("Change version").onClick(()=>{this.plugin.betaPlugins.displayAddNewPluginModal(!0,!0,s,o==null?void 0:o.version,o==null?void 0:o.token),this.plugin.app.setting.updatePluginSection()})}).addButton(a=>{a.setIcon("cross").setTooltip("Remove this beta plugin").setWarning().onClick(()=>{if(a.buttonEl.textContent==="")a.setButtonText("Click once more to confirm removal");else{let{buttonEl:c}=a,{parentElement:l}=c;l!=null&&l.parentElement&&(l.parentElement.remove(),this.plugin.betaPlugins.deletePlugin(s))}})})}new I.Setting(t).setName("Beta themes list").setHeading(),new I.Setting(t).addButton(s=>{s.setButtonText("Add beta theme").setCta().onClick(()=>{this.plugin.app.setting.close(),new O(this.plugin).open()})});for(let s of this.plugin.settings.themesList)new I.Setting(t).setName(J(s.repo)).addButton(o=>{o.setIcon("cross").setTooltip("Delete this beta theme").onClick(()=>{if(o.buttonEl.textContent==="")o.setButtonText("Click once more to confirm removal");else{let{buttonEl:r}=o,{parentElement:a}=r;a!=null&&a.parentElement&&(a.parentElement.remove(),Ie(this.plugin,s.repo))}})});new I.Setting(t).setName("Monitoring").setHeading(),new I.Setting(t).setName("Enable notifications").setDesc("BRAT will provide popup notifications for its various activities. Turn this off means  no notifications from BRAT.").addToggle(s=>{s.setValue(this.plugin.settings.notificationsEnabled),s.onChange(async o=>{this.plugin.settings.notificationsEnabled=o,await this.plugin.saveSettings()})}),new I.Setting(t).setName("Enable logging").setDesc("Plugin updates will be logged to a file in the log file.").addToggle(s=>{s.setValue(this.plugin.settings.loggingEnabled).onChange(async o=>{this.plugin.settings.loggingEnabled=o,await this.plugin.saveSettings()})}),new I.Setting(this.containerEl).setName("BRAT log file location").setDesc("Logs will be saved to this file. Don't add .md to the file name.").addSearch(s=>{s.setPlaceholder("Example: BRAT-log").setValue(this.plugin.settings.loggingPath).onChange(async o=>{this.plugin.settings.loggingPath=o,await this.plugin.saveSettings()})}),new I.Setting(t).setName("Enable verbose logging").setDesc("Get a lot  more information in  the log.").addToggle(s=>{s.setValue(this.plugin.settings.loggingVerboseEnabled).onChange(async o=>{this.plugin.settings.loggingVerboseEnabled=o,await this.plugin.saveSettings()})}),new I.Setting(t).setName("Debugging mode").setDesc("Atomic Bomb level console logging. Can be used for troubleshoting and development.").addToggle(s=>{s.setValue(this.plugin.settings.debuggingMode).onChange(async o=>{this.plugin.settings.debuggingMode=o,await this.plugin.saveSettings()})}),new I.Setting(t).setName("Personal access token").setDesc(Rt({prependText:"Set a personal access token to increase rate limits for public repositories on GitHub. You can create one in ",url:"https://github.com/settings/tokens/new?scopes=public_repo",text:"your GitHub account settings",appendText:" and then add it here. Please consult the documetation for more details."})).addText(s=>{var o;this.accessTokenSetting=s,s.setPlaceholder("Enter your personal access token").setValue((o=this.plugin.settings.personalAccessToken)!=null?o:"").onChange(async r=>{var a,c,l;r===""?(this.plugin.settings.personalAccessToken="",this.plugin.saveSettings(),(a=this.accessTokenButton)==null||a.setDisabled(!0),(c=this.validator)==null||c.validateToken("")):(l=this.accessTokenButton)==null||l.setDisabled(!1)}),s.inputEl.addClass("brat-token-input")}).addButton(s=>{this.accessTokenButton=s,s.setButtonText("Validate").setCta().onClick(async()=>{var r,a,c;let o=(r=this.accessTokenSetting)==null?void 0:r.inputEl.value;o&&await((a=this.validator)==null?void 0:a.validateToken(o))&&(this.plugin.settings.personalAccessToken=o,this.plugin.saveSettings(),(c=this.accessTokenButton)==null||c.setDisabled(!0))})}).then(()=>{var s,o;this.tokenInfo=this.createTokenInfoElement(t),this.validator=new G(this.accessTokenSetting,this.tokenInfo),(o=this.validator)==null||o.validateToken((s=this.plugin.settings.personalAccessToken)!=null?s:"").then(r=>{var a;(a=this.accessTokenButton)==null||a.setDisabled(r||this.plugin.settings.personalAccessToken==="")})})}createTokenInfoElement(t){let i=t.createDiv({cls:"brat-token-info"});return i.createDiv({cls:"brat-token-status"}),i.createDiv({cls:"brat-token-details"}),i}};var kt=require("obsidian");function $t(){(0,kt.addIcon)("BratIcon",'<path fill="currentColor" stroke="currentColor"  d="M 41.667969 41.667969 C 41.667969 39.367188 39.800781 37.5 37.5 37.5 C 35.199219 37.5 33.332031 39.367188 33.332031 41.667969 C 33.332031 43.96875 35.199219 45.832031 37.5 45.832031 C 39.800781 45.832031 41.667969 43.96875 41.667969 41.667969 Z M 60.417969 58.582031 C 59.460938 58.023438 58.320312 57.867188 57.25 58.148438 C 56.179688 58.429688 55.265625 59.125 54.707031 60.082031 C 53.746094 61.777344 51.949219 62.820312 50 62.820312 C 48.050781 62.820312 46.253906 61.777344 45.292969 60.082031 C 44.734375 59.125 43.820312 58.429688 42.75 58.148438 C 41.679688 57.867188 40.539062 58.023438 39.582031 58.582031 C 37.597656 59.726562 36.910156 62.257812 38.042969 64.25 C 40.5 68.53125 45.0625 71.171875 50 71.171875 C 54.9375 71.171875 59.5 68.53125 61.957031 64.25 C 63.089844 62.257812 62.402344 59.726562 60.417969 58.582031 Z M 62.5 37.5 C 60.199219 37.5 58.332031 39.367188 58.332031 41.667969 C 58.332031 43.96875 60.199219 45.832031 62.5 45.832031 C 64.800781 45.832031 66.667969 43.96875 66.667969 41.667969 C 66.667969 39.367188 64.800781 37.5 62.5 37.5 Z M 50 8.332031 C 26.988281 8.332031 8.332031 26.988281 8.332031 50 C 8.332031 73.011719 26.988281 91.667969 50 91.667969 C 73.011719 91.667969 91.667969 73.011719 91.667969 50 C 91.667969 26.988281 73.011719 8.332031 50 8.332031 Z M 50 83.332031 C 33.988281 83.402344 20.191406 72.078125 17.136719 56.363281 C 14.078125 40.644531 22.628906 24.976562 37.5 19.042969 C 37.457031 19.636719 37.457031 20.238281 37.5 20.832031 C 37.5 27.738281 43.097656 33.332031 50 33.332031 C 52.300781 33.332031 54.167969 31.46875 54.167969 29.167969 C 54.167969 26.867188 52.300781 25 50 25 C 47.699219 25 45.832031 23.132812 45.832031 20.832031 C 45.832031 18.53125 47.699219 16.667969 50 16.667969 C 68.410156 16.667969 83.332031 31.589844 83.332031 50 C 83.332031 68.410156 68.410156 83.332031 50 83.332031 Z M 50 83.332031 " />')}var te=class{constructor(e){this.console=(e,...t)=>{console.log(`BRAT: ${e}`,...t)};this.themes={themeseCheckAndUpates:async e=>{await z(this.plugin,e)},themeInstallTheme:async e=>{let t=e.replace("https://github.com/","");await Z(this.plugin,t,!0)},themesDelete:e=>{let t=e.replace("https://github.com/","");Ie(this.plugin,t)},grabCommmunityThemeCssFile:async(e,t=!1)=>await _(e,t,this.plugin.settings.debuggingMode),grabChecksumOfThemeCssFile:async(e,t=!1)=>await W(e,t,this.plugin.settings.debuggingMode),grabLastCommitDateForFile:async(e,t)=>await Et(e,t)};this.plugin=e}};var ue=require("obsidian"),qt=ti(zt());async function Xt(n,e,t=!1){if(n.settings.debuggingMode&&console.log(`BRAT: ${e}`),n.settings.loggingEnabled){if(!n.settings.loggingVerboseEnabled&&t)return;let i=`${n.settings.loggingPath}.md`,s=`[[${(0,ue.moment)().format((0,qt.getDailyNoteSettings)().format).toString()}]] ${(0,ue.moment)().format("HH:mm")}`,o=window.require("os"),r=ue.Platform.isDesktop?o.hostname():"MOBILE",a=`${s} ${r} ${e.replace(`
`," ")}
`,c=n.app.vault.getAbstractFileByPath(i);c?await n.app.vault.append(c,a):c=await n.app.vault.create(i,a)}}var Ae=class extends Yt.Plugin{constructor(){super(...arguments);this.APP_NAME="BRAT";this.APP_ID="obsidian42-brat";this.settings=Me;this.betaPlugins=new Q(this);this.commands=new ee(this);this.bratApi=new te(this);this.obsidianProtocolHandler=t=>{if(!t.plugin&&!t.theme){b(this,"Could not locate the repository from the URL.",10);return}for(let i of["plugin","theme"])if(t[i]){let s;switch(i){case"plugin":s=new M(this,this.betaPlugins,!0,!1,t[i],t.version?t.version:void 0),s.open();break;case"theme":s=new O(this),s.address=t[i],s.open();break}return}}}onload(){console.log(`loading ${this.APP_NAME}`),$t(),this.addRibbonIcon("BratIcon","BRAT",()=>{this.commands.ribbonDisplayCommands()}),this.loadSettings().then(()=>{this.app.workspace.onLayoutReady(()=>{this.addSettingTab(new Re(this.app,this)),this.registerObsidianProtocolHandler("brat",this.obsidianProtocolHandler),this.settings.updateAtStartup&&setTimeout(()=>{this.betaPlugins.checkForPluginUpdatesAndInstallUpdates(!1)},6e4),this.settings.updateThemesAtStartup&&setTimeout(()=>{z(this,!1)},12e4),setTimeout(()=>{window.bratAPI=this.bratApi},500)})}).catch(t=>{console.error("Failed to load settings:",t)})}async log(t,i=!1){await Xt(this,t,i)}onunload(){console.log(`unloading ${this.APP_NAME}`)}async loadSettings(){this.settings=Object.assign({},Me,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};

/* nosourcemap */