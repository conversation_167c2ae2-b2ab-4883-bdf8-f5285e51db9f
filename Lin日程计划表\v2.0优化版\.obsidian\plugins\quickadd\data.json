{"choices": [{"id": "today-expense-choice-001", "name": "💸 今日支出", "type": "Macro", "command": true, "macroId": "today-expense-macro-001"}, {"id": "today-income-choice-001", "name": "💰 今日收入", "type": "Macro", "command": true, "macroId": "today-income-macro-001"}, {"id": "backlog-expense-choice-001", "name": "📅 补录支出", "type": "Macro", "command": true, "macroId": "backlog-expense-macro-001"}, {"id": "backlog-income-choice-001", "name": "📅 补录收入", "type": "Macro", "command": true, "macroId": "backlog-income-macro-001"}, {"id": "timestamp-choice-001", "name": "⏰ 插入时间节点", "type": "Template", "command": true, "templatePath": "", "fileNameFormat": {"enabled": false, "format": ""}, "folder": {"enabled": false, "folders": [], "chooseWhenCreatingNote": false, "createInSameFolderAsActiveFile": false, "chooseFromSubfolders": false}, "appendLink": false, "openFileInNewTab": {"enabled": false, "direction": "vertical", "focus": true}, "openFile": false, "openFileInMode": "default", "fileExistsMode": "Increment the file name", "setFileExistsBehavior": false, "format": "⏰ {{DATE:YYYY-MM-DD HH:mm:ss}} - ", "insertAfter": {"enabled": false, "after": "", "insertAtEnd": false, "considerSubsections": false, "createIfNotFound": false, "createIfNotFoundLocation": "top"}}, {"id": "path-test-choice-001", "name": "🔍 路径测试", "type": "Macro", "command": true, "macroId": "path-test-macro-001"}], "macros": [{"name": "今日支出宏", "id": "today-expense-macro-001", "commands": [{"name": "今日支出.js", "type": "UserScript", "id": "today-expense-script-001", "path": "QuickAdd脚本/今日支出.js", "settings": {}}], "runOnStartup": false}, {"name": "今日收入宏", "id": "today-income-macro-001", "commands": [{"name": "今日收入.js", "type": "UserScript", "id": "today-income-script-001", "path": "QuickAdd脚本/今日收入.js", "settings": {}}], "runOnStartup": false}, {"name": "补录支出宏", "id": "backlog-expense-macro-001", "commands": [{"name": "补录支出.js", "type": "UserScript", "id": "backlog-expense-script-001", "path": "QuickAdd脚本/补录支出.js", "settings": {}}], "runOnStartup": false}, {"name": "补录收入宏", "id": "backlog-income-macro-001", "commands": [{"name": "补录收入.js", "type": "UserScript", "id": "backlog-income-script-001", "path": "QuickAdd脚本/补录收入.js", "settings": {}}], "runOnStartup": false}, {"name": "路径测试宏", "id": "path-test-macro-001", "commands": [{"name": "路径测试.js", "type": "UserScript", "id": "path-test-script-001", "path": "QuickAdd脚本/路径测试.js", "settings": {}}], "runOnStartup": false}], "inputPrompt": "single-line", "devMode": false, "templateFolderPath": "", "announceUpdates": true, "version": "1.18.1", "disableOnlineFeatures": true, "enableRibbonIcon": false, "ai": {"defaultModel": "Ask me", "defaultSystemPrompt": "As an AI assistant within Obsidian, your primary goal is to help users manage their ideas and knowledge more effectively. Format your responses using Markdown syntax. Please use the [[Obsidian]] link format. You can write aliases for the links by writing [[Obsidian|the alias after the pipe symbol]]. To use mathematical notation, use LaTeX syntax. LaTeX syntax for larger equations should be on separate lines, surrounded with double dollar signs ($$). You can also inline math expressions by wrapping it in $ symbols. For example, use $$w_{ij}^{\text{new}}:=w_{ij}^{\text{current}}+etacdotdelta_jcdot x_{ij}$$ on a separate line, but you can write \"($eta$ = learning rate, $delta_j$ = error term, $x_{ij}$ = input)\" inline.", "promptTemplatesFolderPath": "", "showAssistant": true, "providers": [{"name": "OpenAI", "endpoint": "https://api.openai.com/v1", "apiKey": "", "models": [{"name": "text-davinci-003", "maxTokens": 4096}, {"name": "gpt-3.5-turbo", "maxTokens": 4096}, {"name": "gpt-3.5-turbo-16k", "maxTokens": 16384}, {"name": "gpt-3.5-turbo-1106", "maxTokens": 16385}, {"name": "gpt-4", "maxTokens": 8192}, {"name": "gpt-4-32k", "maxTokens": 32768}, {"name": "gpt-4-1106-preview", "maxTokens": 128000}, {"name": "gpt-4-turbo", "maxTokens": 128000}, {"name": "gpt-4o", "maxTokens": 128000}, {"name": "gpt-4o-mini", "maxTokens": 128000}]}]}, "migrations": {"migrateToMacroIDFromEmbeddedMacro": true, "useQuickAddTemplateFolder": true, "incrementFileNameSettingMoveToDefaultBehavior": true, "mutualExclusionInsertAfterAndWriteToBottomOfFile": true, "setVersionAfterUpdateModalRelease": true, "addDefaultAIProviders": true}}