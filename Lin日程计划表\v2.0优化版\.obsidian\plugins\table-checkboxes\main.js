/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => TableCheckboxesPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian2 = require("obsidian");

// settings.ts
var import_obsidian = require("obsidian");
var TableCheckboxesPluginSettingsTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    new import_obsidian.Setting(containerEl).setName("Convert checkboxes outside tables").setDesc("Convert checkboxes outside tables to HTML checkboxes").addToggle((toggle) => toggle.setValue(this.plugin.settings.convertCheckboxesOutsideTables).onChange(async (value) => {
      this.plugin.settings.convertCheckboxesOutsideTables = value;
      await this.plugin.saveSettings();
    }));
  }
};

// main.ts
var DEFAULT_SETTINGS = {
  convertCheckboxesOutsideTables: false
};
var TableCheckboxesPlugin = class extends import_obsidian2.Plugin {
  constructor() {
    super(...arguments);
    this.setupWindowHandlers = (_workspaceWindow, win) => {
      this.registerDomEvent(win, "input", (evt) => {
        if (evt.data !== "]") {
          return;
        }
        const view = this.app.workspace.activeEditor;
        if (!view || !view.editor) {
          return;
        }
        const location = view.editor.getCursor("anchor");
        const rowValue = view.editor.getLine(location.line);
        if (!this.isMDCheckboxInTable(rowValue) || this.closingBracketIsTooFar(rowValue, location.ch)) {
          return;
        }
        const checkbox = this.getCheckboxLength(rowValue, location.ch);
        if (!checkbox) {
          return;
        }
        this.handleCheckboxReplacement(view.editor, location, checkbox);
      });
      this.registerDomEvent(win, "change", async (evt) => {
        const changeEl = evt.target;
        if (changeEl.instanceOf(HTMLInputElement) && changeEl.id && changeEl.hasAttribute("data-task") === false) {
          const view = this.app.workspace.activeEditor;
          if (!view || !view.editor || !view.file) {
            return;
          }
          if (changeEl.getAttribute("type") === "checkbox") {
            const page = await this.app.vault.read(view.file);
            const id = changeEl.id;
            this.toggleCheckbox(page, view.file, changeEl.checked, id);
          }
        }
      });
    };
  }
  async onload() {
    this.app.workspace.on("window-open", this.setupWindowHandlers);
    this.setupWindowHandlers(void 0, activeWindow);
    await this.loadSettings();
    this.addCommand({
      id: "convert-checkboxes",
      name: "Convert all checkboxes in the current file to HTML checkboxes",
      callback: () => {
        this.convertAllCheckboxes();
      }
    });
    this.addCommand({
      id: "regenerate-checkbox-ids",
      name: "Regenerate all checkbox IDs",
      callback: () => {
        this.regenerateCheckboxIds();
      }
    });
    this.addSettingTab(new TableCheckboxesPluginSettingsTab(this.app, this));
  }
  async onunload() {
    this.app.workspace.off("window-open", this.setupWindowHandlers);
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
  handleCheckboxReplacement(editor, location, checkbox) {
    const completeCheckbox = checkbox.endsWith("]");
    location.ch = completeCheckbox ? location.ch + 1 : location.ch;
    const start = { ...location };
    start.ch -= checkbox.length;
    editor.setSelection(start, location);
    const checkboxId = this.generateUniqueCheckboxId(editor.getDoc().getValue());
    editor.replaceSelection(`<input type="checkbox" unchecked id="${checkboxId}">`);
  }
  generateUniqueCheckboxId(page) {
    let id = crypto.randomUUID().slice(-6);
    while (this.idExistsInFile(id, page)) {
      id = crypto.randomUUID();
    }
    return id;
  }
  idExistsInFile(id, page) {
    const idIndex = page.search(id);
    return idIndex !== -1;
  }
  isMDCheckboxInTable(rowValue) {
    const tableRegex = /^(\s|>)*\|.*-\s?(?:\[\s?\]|\[).*/m;
    if (rowValue.match(tableRegex)) {
      return true;
    }
    return false;
  }
  closingBracketIsTooFar(rowValue, ch) {
    if (rowValue[ch - 1] === "[" || rowValue[ch - 2] === "[") {
      return false;
    }
    return true;
  }
  getCheckboxLength(viewData, ch) {
    const completeCheckbox = viewData[ch] === "]";
    const areaToCheck = viewData.slice(ch - 4, completeCheckbox ? ch + 1 : ch);
    const checkboxRegex = /-\s{0,1}\[\s{0,1}\]?/;
    const checkboxMatch = areaToCheck.match(checkboxRegex);
    if (!checkboxMatch) {
      return null;
    }
    return checkboxMatch[0];
  }
  toggleCheckbox(page, file, isChecked, checkboxId) {
    page = page.replace(new RegExp(`<input type="checkbox" (un)?checked id="${checkboxId}">`), `<input type="checkbox" ${isChecked ? "" : "un"}checked id="${checkboxId}">`);
    this.app.vault.modify(file, page);
  }
  convertAllCheckboxes() {
    const view = this.app.workspace.activeEditor;
    if (!view || !view.editor) {
      return;
    }
    const page = view.editor.getDoc().getValue();
    const checkboxes = this.getCheckboxesToConvert(page, this.settings.convertCheckboxesOutsideTables);
    this.convertCheckboxes(view.editor, checkboxes);
  }
  getCheckboxesToConvert(page, convertOutsideTables) {
    const checkboxes = [];
    const lines = page.split("\n");
    let lineCount = 0;
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (!convertOutsideTables && !this.isMDCheckboxInTable(line)) {
        lineCount++;
        continue;
      }
      const checkboxRegex = /-\s{0,1}\[\s{0,1}\]/g;
      let match;
      while ((match = checkboxRegex.exec(line)) !== null) {
        const from = {
          line: lineCount,
          ch: match.index
        };
        const to = {
          line: lineCount,
          ch: match.index + match[0].length
        };
        checkboxes.push({ from, to });
      }
      lineCount++;
    }
    return checkboxes;
  }
  convertCheckboxes(editor, checkboxes) {
    const checkboxIds = Array.from({ length: checkboxes.length }, () => this.generateUniqueCheckboxId(editor.getDoc().getValue()));
    const selections = checkboxes.map((checkbox) => ({
      anchor: checkbox.from,
      head: checkbox.to
    }));
    editor.setSelections(selections);
    editor.replaceSelection("!!PLACEHOLDER_TO_BE_REPLACED_WITH_CHECKBOX!!");
    let page = editor.getDoc().getValue();
    checkboxIds.forEach((id) => {
      page = page.replace(/!!PLACEHOLDER_TO_BE_REPLACED_WITH_CHECKBOX!!/, `<input type="checkbox" unchecked id="${id}">`);
    });
    editor.getDoc().setValue(page);
  }
  regenerateCheckboxIds() {
    const view = this.app.workspace.activeEditor;
    if (!view || !view.editor) {
      return;
    }
    let page = view.editor.getDoc().getValue();
    const checkboxRegex = /<input type="checkbox"[^>]*id="[^"]*"[^>]*>/g;
    let match;
    while ((match = checkboxRegex.exec(page)) !== null) {
      const oldCheckbox = match[0];
      const newId = this.generateUniqueCheckboxId(page);
      const newCheckbox = oldCheckbox.replace(/id="[^"]*"/, `id="${newId}"`);
      page = page.replace(oldCheckbox, newCheckbox);
    }
    view.editor.setValue(page);
  }
};

/* nosourcemap */