/* 智能隐藏YAML Frontmatter - 方案2实现 (修正版) */
/* Smart Hide YAML Frontmatter - Solution 2 Implementation (Fixed) */

/*
 * 功能说明：
 * - 在所有查看模式下隐藏YAML frontmatter
 * - 兼容新旧版本的Obsidian
 * - 不影响meta-bind等插件的后台功能
 * - 支持Properties和传统frontmatter
 * - 基于2024年最新的Obsidian CSS选择器
 */

/* =================================== */
/* 主要隐藏规则 - Main Hiding Rules */
/* =================================== */

/* 隐藏Properties容器 - 最新版本Obsidian */
.metadata-properties {
    display: none !important;
}

/* 隐藏Properties在阅读模式 */
.markdown-preview-view .metadata-properties {
    display: none !important;
}

/* 隐藏Properties在实时预览模式 */
.markdown-reading-view .metadata-properties {
    display: none !important;
}

/* 隐藏Properties在源码模式 */
.markdown-source-view .metadata-properties {
    display: none !important;
}

/* 隐藏传统frontmatter容器 */
.frontmatter-container {
    display: none !important;
}

/* =================================== */
/* Properties系统详细隐藏 - Detailed Properties Hiding */
/* =================================== */

/* 隐藏Properties标题和编辑器 */
.metadata-properties-heading,
.metadata-properties-title,
.metadata-properties-editor {
    display: none !important;
}

/* 隐藏所有Properties项目 */
.metadata-property,
div[data-property-key] {
    display: none !important;
}

/* 隐藏Properties添加按钮 */
.metadata-add-button,
.metadata-properties .clickable-icon {
    display: none !important;
}

/* 覆盖任何折叠状态 */
.metadata-properties[data-collapsed="true"],
.metadata-properties[data-collapsed="false"] {
    display: none !important;
}

/* =================================== */
/* 全面覆盖选择器 - Comprehensive Selectors */
/* =================================== */

/* 确保所有可能的frontmatter容器都被隐藏 */
.frontmatter,
.yaml-frontmatter,
.metadata-section,
.metadata-container,
.frontmatter-container {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 移动端兼容 */
.is-mobile .metadata-properties,
.is-mobile .frontmatter-container {
    display: none !important;
}

/* 编辑器兼容 */
.cm-editor .metadata-properties,
.workspace-leaf-content[data-type="markdown"] .metadata-properties {
    display: none !important;
}

/* =================================== */
/* 调试模式（可选启用） - Debug Mode */
/* =================================== */

/* 
 * 如果需要临时显示YAML进行调试，
 * 可以在body标签添加 debug-yaml 类
 */
/*
body.debug-yaml .metadata-container,
body.debug-yaml .frontmatter-container {
    display: block !important;
    border: 2px solid red;
    background: rgba(255, 0, 0, 0.1);
}
*/

/* =================================== */
/* 版本信息 - Version Info */
/* =================================== */

/*
 * 文件名: hide-yaml-frontmatter.css
 * 版本: v1.0
 * 创建时间: 2025-07-31
 * 作用: 智能隐藏Obsidian中的YAML frontmatter
 * 兼容性: Obsidian v1.0+ (新旧Properties系统)
 * 方案: 方案2 - 智能隐藏（在不同模式下隐藏YAML）
 */
