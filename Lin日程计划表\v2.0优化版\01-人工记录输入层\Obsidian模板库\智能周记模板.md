---
date: {{date:YYYY-MM-DD}}
display_date: {{date:YYYY年MM月DD日}}
week: {{date:w}}
year: {{date:YYYY}}
week_start: <% tp.date.weekday("YYYY-MM-DD", 0) %>
week_end: <% tp.date.weekday("YYYY-MM-DD", 6) %>
tags: [周记, {{date:YYYY}}, 第{{date:w}}周]
---

# 📅 {{date:YYYY}}年第{{date:w}}周周记

> **本周时间范围**: <% tp.date.weekday("YYYY年MM月DD日", 0) %> - <% tp.date.weekday("YYYY年MM月DD日", 6) %>

---

## 📊 本周数据汇总

### 🗓️ 日记完成情况

```dataviewjs
// 计算本周日期范围
const today = new Date();
const currentWeekday = today.getDay(); // 0=周日, 1=周一...
const mondayOffset = currentWeekday === 0 ? -6 : 1 - currentWeekday;
const mondayDate = new Date(today);
mondayDate.setDate(today.getDate() + mondayOffset);

// 生成本周7天的日期
const weekDays = [];
for (let i = 0; i < 7; i++) {
    const date = new Date(mondayDate);
    date.setDate(mondayDate.getDate() + i);
    weekDays.push({
        date: date,
        dateStr: date.toISOString().split('T')[0],
        dayName: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()],
        dayNum: date.getDay()
    });
}

// 查找本周的日记文件
const weekFiles = dv.pages('"v2.0优化版/01-人工记录输入层/记录界面/日记/2025"')
    .where(p => p.week && p.week == dv.current().week);

// 创建日记状态表格
dv.paragraph("| 日期 | 星期 | 日记状态 | 运动完成 | 工作完成 | 学习完成 | 数据完成 |");
dv.paragraph("|------|------|----------|----------|----------|----------|----------|");

weekDays.forEach(day => {
    const dayFile = weekFiles.find(f => {
        const fileDate = new Date(f.date);
        return fileDate.toISOString().split('T')[0] === day.dateStr;
    });
    
    if (dayFile) {
        // 有日记文件，分析任务完成情况
        const tasks = dayFile.file.tasks || [];
        const exerciseCompleted = tasks.filter(t => t.completed && t.text.includes('#exercise')).length;
        const exerciseTotal = tasks.filter(t => t.text.includes('#exercise')).length;
        const workCompleted = tasks.filter(t => t.completed && t.text.includes('#work')).length;
        const workTotal = tasks.filter(t => t.text.includes('#work')).length;
        const studyCompleted = tasks.filter(t => t.completed && t.text.includes('#study')).length;
        const studyTotal = tasks.filter(t => t.text.includes('#study')).length;
        const dataCompleted = tasks.filter(t => t.completed && t.text.includes('#data')).length;
        const dataTotal = tasks.filter(t => t.text.includes('#data')).length;
        
        const exerciseStatus = exerciseTotal > 0 ? `${exerciseCompleted}/${exerciseTotal}` : "无";
        const workStatus = workTotal > 0 ? `${workCompleted}/${workTotal}` : "无";
        const studyStatus = studyTotal > 0 ? `${studyCompleted}/${studyTotal}` : "无";
        const dataStatus = dataTotal > 0 ? `${dataCompleted}/${dataTotal}` : "无";
        
        dv.paragraph(`| ${day.dateStr} | ${day.dayName} | ✅ [[${dayFile.file.name}\\|已创建]] | ${exerciseStatus} | ${workStatus} | ${studyStatus} | ${dataStatus} |`);
    } else {
        // 没有日记文件
        const isToday = day.dateStr === today.toISOString().split('T')[0];
        const isFuture = new Date(day.dateStr) > today;
        
        if (isFuture) {
            dv.paragraph(`| ${day.dateStr} | ${day.dayName} | ⏳ 未来 | - | - | - | - |`);
        } else if (isToday) {
            dv.paragraph(`| ${day.dateStr} | ${day.dayName} | 🔄 今日 | - | - | - | - |`);
        } else {
            dv.paragraph(`| ${day.dateStr} | ${day.dayName} | ❌ 缺失 | - | - | - | - |`);
        }
    }
});
```

### 📈 本周进度统计

```dataviewjs
// 获取本周所有日记文件
const weekFiles = dv.pages('"v2.0优化版/01-人工记录输入层/记录界面/日记/2025"')
    .where(p => p.week && p.week == dv.current().week);

if (weekFiles.length > 0) {
    // 汇总所有任务
    let allTasks = [];
    weekFiles.forEach(file => {
        if (file.file.tasks) {
            allTasks = allTasks.concat(file.file.tasks);
        }
    });
    
    // 按类别统计
    const categories = [
        { name: "🏃 运动任务", tag: "#exercise" },
        { name: "💼 工作任务", tag: "#work" },
        { name: "📚 学习任务", tag: "#study" },
        { name: "📊 数据记录", tag: "#data" }
    ];
    
    categories.forEach(cat => {
        const catTasks = allTasks.filter(t => t.text.includes(cat.tag));
        const completed = catTasks.filter(t => t.completed).length;
        const total = catTasks.length;
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
        
        let progressBar = "";
        for (let i = 0; i < 10; i++) {
            progressBar += i < (percentage / 10) ? "🟢" : "⚪";
        }
        
        dv.paragraph(`**${cat.name}**: ${progressBar} **${percentage}%** (${completed}/${total})`);
    });
    
    // 总体完成度
    const allCompleted = allTasks.filter(t => t.completed).length;
    const allTotal = allTasks.length;
    const overallPercentage = allTotal > 0 ? Math.round((allCompleted / allTotal) * 100) : 0;
    
    let overallBar = "";
    for (let i = 0; i < 20; i++) {
        overallBar += i < (overallPercentage / 5) ? "🔥" : "⚪";
    }
    
    dv.paragraph(`**🌟 本周总体完成度**: ${overallBar}`);
    dv.paragraph(`**完成率**: ${overallPercentage}% (${allCompleted}/${allTotal})`);
    
} else {
    dv.paragraph("📝 本周暂无日记数据");
}
```

---

## 🎯 本周目标回顾

### 📋 本周计划的三件大事
1. 
2. 
3. 

### ✅ 实际完成情况
- [ ] 第一件事完成情况：____
- [ ] 第二件事完成情况：____
- [ ] 第三件事完成情况：____

---

## 📊 本周数据详细分析

### 💪 运动数据汇总

```dataviewjs
// 汇总本周运动数据
const today = new Date();
const currentWeekday = today.getDay();
const mondayOffset = currentWeekday === 0 ? -6 : 1 - currentWeekday;
const mondayDate = new Date(today);
mondayDate.setDate(today.getDate() + mondayOffset);

const sundayDate = new Date(mondayDate);
sundayDate.setDate(mondayDate.getDate() + 6);

const weekFiles = dv.pages('"v2.0优化版/01-人工记录输入层/记录界面/日记/2025"')
    .where(p => {
        if (!p.date) return false;
        const fileDate = new Date(p.date);
        return fileDate >= mondayDate && fileDate <= sundayDate;
    });

if (weekFiles.length > 0) {
    dv.paragraph("| 日期 | 运动类型 | 完成情况 | 备注 |");
    dv.paragraph("|------|----------|----------|------|");
    
    weekFiles.forEach(file => {
        const fileDate = new Date(file.date);
        const dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][fileDate.getDay()];
        const exerciseTasks = file.file.tasks ? file.file.tasks.filter(t => t.text.includes('#exercise')) : [];
        const completed = exerciseTasks.filter(t => t.completed).length;
        const total = exerciseTasks.length;
        
        let exerciseType = "";
        if (fileDate.getDay() === 1) exerciseType = "胸部+背部训练";
        else if (fileDate.getDay() === 3) exerciseType = "肩膀+腹部训练";
        else if (fileDate.getDay() === 5) exerciseType = "腿部训练";
        else if (fileDate.getDay() === 2 || fileDate.getDay() === 4) exerciseType = "走路1万步";
        else if (fileDate.getDay() === 6) exerciseType = "自由运动";
        else exerciseType = "休息日";
        
        const status = total > 0 ? `${completed}/${total}` : "无记录";
        
        dv.paragraph(`| ${fileDate.toISOString().split('T')[0]} (${dayName}) | ${exerciseType} | ${status} | [[${file.file.name}\\|查看详情]] |`);
    });
} else {
    dv.paragraph("📝 本周暂无运动数据");
}
```

### 💰 财务数据汇总

```dataviewjs
// 汇总本周财务数据
const weekFiles = dv.pages('"v2.0优化版/01-人工记录输入层/记录界面/日记/2025"')
    .where(p => {
        if (!p.date) return false;
        const fileDate = new Date(p.date);
        const today = new Date();
        const currentWeekday = today.getDay();
        const mondayOffset = currentWeekday === 0 ? -6 : 1 - currentWeekday;
        const mondayDate = new Date(today);
        mondayDate.setDate(today.getDate() + mondayOffset);
        const sundayDate = new Date(mondayDate);
        sundayDate.setDate(mondayDate.getDate() + 6);
        return fileDate >= mondayDate && fileDate <= sundayDate;
    });

let totalIncome = 0;
let totalExpense = 0;
let recordDays = 0;

if (weekFiles.length > 0) {
    dv.paragraph("| 日期 | 收入 | 支出 | 净收入 |");
    dv.paragraph("|------|------|------|--------|");
    
    weekFiles.forEach(file => {
        // 这里需要根据您的实际数据结构调整
        // 假设收入和支出数据存储在frontmatter中
        const income = file.income || 0;
        const expense = file.expense || 0;
        const net = income - expense;
        
        totalIncome += income;
        totalExpense += expense;
        recordDays++;
        
        dv.paragraph(`| ${file.date} | ${income}元 | ${expense}元 | ${net}元 |`);
    });
    
    dv.paragraph(`| **本周合计** | **${totalIncome}元** | **${totalExpense}元** | **${totalIncome - totalExpense}元** |`);
} else {
    dv.paragraph("📝 本周暂无财务数据");
}
```

---

## 🤔 本周反思

### 🎯 成功经验
- **做得好的地方**：
- **值得继续的习惯**：
- **意外的收获**：

### 📈 改进空间
- **需要改进的地方**：
- **遇到的挑战**：
- **解决方案**：

### 💡 下周计划
- **下周重点目标**：
- **需要调整的地方**：
- **新的尝试**：

---

## 🔗 相关链接

**本周日记链接**：
- [[<% tp.date.weekday("YYYY-MM-DD", 0) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 0) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 1) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 1) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 2) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 2) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 3) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 3) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 4) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 4) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 5) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 5) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 6) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 6) %>]]

**数据传输节点**：
- 月度汇总：[[{{date:YYYY年MM月}}月度总结]]
- 季度回顾：[[{{date:YYYY}}年Q<% Math.ceil(parseInt(tp.date.now("MM")) / 3) %>季度回顾]]
- 年度总结：[[{{date:YYYY}}年度总结]]

---

**创建时间**：{{time}}
**最后更新**：____
