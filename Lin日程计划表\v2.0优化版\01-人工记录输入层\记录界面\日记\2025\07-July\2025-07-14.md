---
date: 2025-07-14
display_date: 2025年07月14日 星期一
created: 2025-07-14
week: 29
weekday: 1
tags: [日记, 2025, 07月]
---

# 📅 2025年07月14日 - 星期一 - 第29周

## 📝 今日记录
*测试日记 - 周一*

---

## 🏃 今日运动安排

### 💪 核心力量训练（周一专属）
**今日运动**：胸部 + 背部训练
**目标时长**：30分钟
**训练内容**：
- [x] 热身（5分钟） #exercise
- [x] 胸部训练：俯卧撑/哑铃推胸（10分钟） #exercise
- [x] 背部训练：引体向上/哑铃划船（10分钟） #exercise
- [x] 拉伸放松（5分钟） #exercise

**实际完成**：
- 实际用时：32分钟
- 胸部训练组数：3组
- 背部训练组数：3组
- 完成质量：很好👍

---

## 💼 工作任务

### 📋 今日工作安排
- [x] 完成项目A的设计方案 #work
- [x] 参加团队会议 #work
- [ ] 整理工作文档 #work

**工作时长**：
- 实际工作时间：6小时

---

## 📚 学习任务

### 📖 今日学习计划
- [x] 学习新技术文档 #study
- [x] 完成在线课程 #study

**学习时长**：
- 实际学习时间：2小时

---

## 📊 数据记录

### 💰 财务记录
- [x] 收入：0元 #data
- [x] 支出：50元 #data
- [x] 记录完成 #data

### 😴 生活数据
- [x] 睡眠：7.5小时 #data
- [x] 步数：8500步 #data

---

## 📈 今日进度

```dataviewjs
const tasks = dv.current().file.tasks || [];

// 快速进度条函数
function quickProgress(tasks, tag, name) {
    const filtered = tasks.filter(t => t.text.includes(tag));
    if (filtered.length === 0) return `${name}: 暂无任务`;
    const completed = filtered.filter(t => t.completed).length;
    const total = filtered.length;
    const percentage = Math.round((completed / total) * 100);
    
    let bar = "";
    for (let i = 0; i < 10; i++) {
        bar += i < (percentage / 10) ? "🟢" : "⚪";
    }
    return `${name}: ${bar} ${percentage}%`;
}

dv.paragraph(quickProgress(tasks, '#exercise', '🏃 运动'));
dv.paragraph(quickProgress(tasks, '#work', '💼 工作'));
dv.paragraph(quickProgress(tasks, '#study', '📚 学习'));
dv.paragraph(quickProgress(tasks, '#data', '📊 数据'));

// 综合完成度
const allTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allTasks.length > 0) {
    const allCompleted = allTasks.filter(t => t.completed).length;
    const allTotal = allTasks.length;
    const overallPercentage = Math.round((allCompleted / allTotal) * 100);
    
    let overallBar = "";
    for (let i = 0; i < 20; i++) {
        overallBar += i < (overallPercentage / 5) ? "🔥" : "⚪";
    }
    
    dv.paragraph(`**🌟 今日总进度**: ${overallBar}`);
    dv.paragraph(`**完成度**: ${overallPercentage}% (${allCompleted}/${allTotal})`);
}
```

---

## 🤔 今日反思

**今日亮点**：
- 运动计划完成得很好
- 工作效率较高

**需要改进**：
- 工作文档整理没完成

**明日计划**：
- 继续保持运动习惯
- 完成未完成的工作任务

---

**完成时间**：2025-07-14 18:30
