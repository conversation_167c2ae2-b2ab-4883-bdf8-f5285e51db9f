---
date: 2025-07-15
display_date: 2025年07月15日 星期二
created: 2025-07-15
week: 29
weekday: 2
tags: [日记, 2025, 07月]
---

# 📅 2025年07月15日 - 星期二 - 第29周

## 📝 今日记录
*测试日记 - 周二*

---

## 🏃 今日运动安排

### 🚶 有氧运动（周二专属）
**今日运动**：走路1万步
**目标时长**：60分钟
**运动内容**：
- [x] 选择路线 #exercise
- [x] 走路1万步 #exercise
- [x] 拉伸放松 #exercise

**实际完成**：
- 实际用时：65分钟
- 实际步数：10500步
- 完成质量：很好👍

---

## 💼 工作任务

### 📋 今日工作安排
- [x] 完成项目B的初步设计 #work
- [x] 客户沟通会议 #work
- [x] 整理昨日未完成的工作文档 #work

**工作时长**：
- 实际工作时间：7小时

---

## 📚 学习任务

### 📖 今日学习计划
- [x] 阅读专业书籍 #study
- [ ] 练习编程题目 #study

**学习时长**：
- 实际学习时间：1.5小时

---

## 📊 数据记录

### 💰 财务记录
- [x] 收入：0元 #data
- [x] 支出：80元 #data
- [x] 记录完成 #data

### 😴 生活数据
- [x] 睡眠：7小时 #data
- [x] 步数：10500步 #data

---

## 📈 今日进度

```dataviewjs
const tasks = dv.current().file.tasks || [];

// 快速进度条函数
function quickProgress(tasks, tag, name) {
    const filtered = tasks.filter(t => t.text.includes(tag));
    if (filtered.length === 0) return `${name}: 暂无任务`;
    const completed = filtered.filter(t => t.completed).length;
    const total = filtered.length;
    const percentage = Math.round((completed / total) * 100);
    
    let bar = "";
    for (let i = 0; i < 10; i++) {
        bar += i < (percentage / 10) ? "🟢" : "⚪";
    }
    return `${name}: ${bar} ${percentage}%`;
}

dv.paragraph(quickProgress(tasks, '#exercise', '🏃 运动'));
dv.paragraph(quickProgress(tasks, '#work', '💼 工作'));
dv.paragraph(quickProgress(tasks, '#study', '📚 学习'));
dv.paragraph(quickProgress(tasks, '#data', '📊 数据'));

// 综合完成度
const allTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allTasks.length > 0) {
    const allCompleted = allTasks.filter(t => t.completed).length;
    const allTotal = allTasks.length;
    const overallPercentage = Math.round((allCompleted / allTotal) * 100);
    
    let overallBar = "";
    for (let i = 0; i < 20; i++) {
        overallBar += i < (overallPercentage / 5) ? "🔥" : "⚪";
    }
    
    dv.paragraph(`**🌟 今日总进度**: ${overallBar}`);
    dv.paragraph(`**完成度**: ${overallPercentage}% (${allCompleted}/${allTotal})`);
}
```

---

## 🤔 今日反思

**今日亮点**：
- 超额完成步数目标
- 工作任务全部完成

**需要改进**：
- 编程练习没有完成

**明日计划**：
- 继续保持运动习惯
- 补上编程练习

---

**完成时间**：2025-07-15 19:00
