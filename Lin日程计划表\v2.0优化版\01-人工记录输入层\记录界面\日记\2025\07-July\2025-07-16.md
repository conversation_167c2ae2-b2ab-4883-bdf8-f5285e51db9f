---
date: 2025-07-16
display_date: 2025年07月16日 星期三
created: 2025-07-16
week: 29
weekday: 3
tags: [日记, 2025, 07月]
---

# 📅 2025年07月16日 - 星期三 - 第29周

## 📝 今日记录
*想写什么就写什么*

---

## 🏃 今日运动安排

### 💪 核心力量训练（周三专属）
**今日运动**：肩膀 + 腹部训练
**目标时长**：30分钟
**训练内容**：
- [x] 热身（5分钟） #exercise
- [x] 肩膀训练：哑铃推举/侧平举（10分钟） #exercise
- [ ] 腹部训练：卷腹/平板支撑（10分钟） #exercise
- [x] 拉伸放松（5分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 肩膀训练组数：____组
- 腹部训练时长：____分钟
- 完成质量：很好👍 / 一般👌 / 不佳👎
- 训练感受：轻松😊 / 适中😐 / 困难😓

---

## 📊 今日数据录入

> [!info] 🎯 阅读模式交互输入
> **安装Meta Bind插件后，可在阅读模式下直接输入数据：**
> - 数字输入框：直接点击输入数字
> - 滑块：拖动选择数值
> - 数据自动保存到文件
>
> **插件安装**：设置 → 社区插件 → 搜索"Meta Bind" → 安装启用

### 💰 财务
- [x] 收入记录：0元 #data
- [x] 支出记录：35元 #data

### 😴 睡眠
- [x] 睡眠时长记录：7.2小时 #data

### 🚶 步数
- [x] 步数记录：7800步 #data

### 💼 工作学习
- [x] 工作时长记录：5小时 #work
- [x] 学习时长记录：2小时 #study
- [x] 工作任务完成 #work
- [ ] 学习目标达成 #study

### 😊 心情
- [ ] 心情评分：`INPUT[slider:mood]` (1-10分) #data
- [ ] 心情记录完成 #data

---

## 📈 智能进度条系统

> [!tip] 💡 进度条更新方法
> **阅读模式下勾选任务后，进度条需要手动刷新：**
> - 方法1：按 **Ctrl+R** (Windows) 或 **Cmd+R** (Mac) 刷新页面
> - 方法2：切换到编辑模式再切回阅读模式
> - 方法3：按 **F5** 刷新整个页面
>
> **推荐**：勾选完所有任务后统一刷新一次即可

### 🎯 主要任务状态

```dataviewjs
// 智能主任务状态显示
const tasks = dv.current().file.tasks || [];

// 获取各类子任务
const exerciseTasks = tasks.filter(t => t.text.includes('#exercise'));
const workTasks = tasks.filter(t => t.text.includes('#work'));
const studyTasks = tasks.filter(t => t.text.includes('#study'));
const dataTasks = tasks.filter(t => t.text.includes('#data'));

// 计算完成状态
function getTaskStatus(subTasks, taskName) {
    if (subTasks.length === 0) return `- ⚪ ${taskName} (暂无子任务)`;

    const completed = subTasks.filter(t => t.completed).length;
    const total = subTasks.length;
    const percentage = Math.round((completed / total) * 100);

    let statusIcon, statusText;
    if (percentage === 100) {
        statusIcon = "✅"; statusText = "已完成";
    } else if (percentage >= 75) {
        statusIcon = "🎯"; statusText = "接近完成";
    } else if (percentage >= 50) {
        statusIcon = "💪"; statusText = "进行中";
    } else if (percentage >= 25) {
        statusIcon = "🚀"; statusText = "已开始";
    } else {
        statusIcon = "⏰"; statusText = "待开始";
    }

    return `- ${statusIcon} ${taskName} ${statusText} (${completed}/${total})`;
}

// 获取当前星期对应的运动任务名称
const dayNum = new Date().getDay();
let exerciseTaskName = "";
if (dayNum === 1) exerciseTaskName = "胸部+背部训练";
else if (dayNum === 3) exerciseTaskName = "肩膀+腹部训练";
else if (dayNum === 5) exerciseTaskName = "腿部训练";
else if (dayNum === 2 || dayNum === 4) exerciseTaskName = "走路1万步";
else if (dayNum === 6) exerciseTaskName = "自由运动";
else exerciseTaskName = "休息恢复";

// 显示智能主任务状态
dv.paragraph(getTaskStatus(exerciseTasks, exerciseTaskName));
dv.paragraph(getTaskStatus(workTasks, "工作任务"));
dv.paragraph(getTaskStatus(studyTasks, "学习目标"));
dv.paragraph(getTaskStatus(dataTasks, "数据记录"));
```

### 📊 详细进度统计

```dataviewjs
// 获取当前页面的所有任务
const tasks = dv.current().file.tasks || [];

// 创建智能进度条函数
function createProgressBar(completed, total) {
    if (total === 0) return "⚪ 暂无任务";
    
    const percentage = Math.round((completed / total) * 100);
    const width = 10;
    const filledBars = Math.floor((percentage / 100) * width);
    const emptyBars = width - filledBars;
    
    let fillChar, emptyChar, statusIcon;
    
    if (percentage === 100) {
        fillChar = "🟢"; emptyChar = "⚪"; statusIcon = "✅";
    } else if (percentage >= 75) {
        fillChar = "🟡"; emptyChar = "⚪"; statusIcon = "🎯";
    } else if (percentage >= 50) {
        fillChar = "🔵"; emptyChar = "⚪"; statusIcon = "💪";
    } else if (percentage >= 25) {
        fillChar = "🟠"; emptyChar = "⚪"; statusIcon = "🚀";
    } else {
        fillChar = "🔴"; emptyChar = "⚪"; statusIcon = "⏰";
    }
    
    const progressBar = fillChar.repeat(Math.max(1, filledBars)) + emptyChar.repeat(emptyBars);
    return `${statusIcon} ${progressBar} **${percentage}%** (${completed}/${total})`;
}

// 获取智能建议
function getSuggestion(completed, total, category) {
    const percentage = Math.round((completed / total) * 100);
    const remaining = total - completed;
    
    if (percentage === 100) {
        return `🎉 ${category}全部完成！`;
    } else if (percentage >= 75) {
        return `👍 ${category}接近完成，还剩${remaining}项`;
    } else if (percentage >= 50) {
        return `💪 ${category}过半完成，继续加油`;
    } else if (percentage >= 25) {
        return `🚀 ${category}已开始，还需努力`;
    } else {
        return `⏰ ${category}需要开始行动了`;
    }
}

// 第二层：分类任务进度
dv.header(4, "📋 分类任务进度");

const categories = [
    { 
        name: "🏃 运动任务", 
        mainTag: "#task-exercise",
        subTag: "#exercise",
        mainTasks: tasks.filter(t => t.text.includes('#task-exercise')),
        subTasks: tasks.filter(t => t.text.includes('#exercise'))
    },
    { 
        name: "💼 工作任务", 
        mainTag: "#task-work",
        subTag: "#work",
        mainTasks: tasks.filter(t => t.text.includes('#task-work')),
        subTasks: tasks.filter(t => t.text.includes('#work'))
    },
    { 
        name: "📚 学习任务", 
        mainTag: "#task-study",
        subTag: "#study",
        mainTasks: tasks.filter(t => t.text.includes('#task-study')),
        subTasks: tasks.filter(t => t.text.includes('#study'))
    },
    { 
        name: "📊 数据记录", 
        mainTag: "#task-data",
        subTag: "#data",
        mainTasks: tasks.filter(t => t.text.includes('#task-data')),
        subTasks: tasks.filter(t => t.text.includes('#data'))
    }
];

categories.forEach(cat => {
    if (cat.subTasks.length > 0) {
        const subCompleted = cat.subTasks.filter(t => t.completed).length;
        const subTotal = cat.subTasks.length;
        const mainCompleted = cat.mainTasks.filter(t => t.completed).length;
        const mainTotal = cat.mainTasks.length;
        
        dv.paragraph(`**${cat.name}**:`);
        dv.paragraph(`└─ 子任务: ${createProgressBar(subCompleted, subTotal)}`);
        if (mainTotal > 0) {
            dv.paragraph(`└─ 主任务: ${createProgressBar(mainCompleted, mainTotal)}`);
        }
        dv.paragraph(`💡 ${getSuggestion(subCompleted, subTotal, cat.name.split(' ')[1])}`);
        dv.paragraph("");
    }
});

// 第三层：今日总进度
dv.header(4, "🎯 今日总进度统计");

const allMainTasks = tasks.filter(t => t.text.includes('#task-'));
const allSubTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allMainTasks.length > 0 || allSubTasks.length > 0) {
    const mainCompleted = allMainTasks.filter(t => t.completed).length;
    const mainTotal = allMainTasks.length;
    const subCompleted = allSubTasks.filter(t => t.completed).length;
    const subTotal = allSubTasks.length;
    
    // 计算综合完成度（主任务权重60%，子任务权重40%）
    let overallPercentage = 0;
    if (mainTotal > 0 && subTotal > 0) {
        const mainPercentage = (mainCompleted / mainTotal) * 100;
        const subPercentage = (subCompleted / subTotal) * 100;
        overallPercentage = Math.round(mainPercentage * 0.6 + subPercentage * 0.4);
    } else if (mainTotal > 0) {
        overallPercentage = Math.round((mainCompleted / mainTotal) * 100);
    } else if (subTotal > 0) {
        overallPercentage = Math.round((subCompleted / subTotal) * 100);
    }
    
    dv.paragraph(`**🌟 今日综合完成度**: ${createProgressBar(overallPercentage, 100).replace(/\(\d+\/\d+\)/, `(综合评分: ${overallPercentage}%)`)}`);
    dv.paragraph(`├─ 主要任务: ${createProgressBar(mainCompleted, mainTotal)}`);
    dv.paragraph(`└─ 详细任务: ${createProgressBar(subCompleted, subTotal)}`);
    
    // 综合建议
    let overallSuggestion = "";
    if (overallPercentage >= 90) {
        overallSuggestion = "🏆 今天表现卓越！所有任务都完成得很好！";
    } else if (overallPercentage >= 75) {
        overallSuggestion = "🎯 今天进展很好！再坚持一下就能完美收官！";
    } else if (overallPercentage >= 50) {
        overallSuggestion = "💪 今天过半完成！保持节奏，继续加油！";
    } else if (overallPercentage >= 25) {
        overallSuggestion = "🚀 今天已经开始！建议重点关注未完成的重要任务！";
    } else {
        overallSuggestion = "⏰ 今天需要加速了！建议从最重要的任务开始！";
    }
    
    dv.paragraph(`🎪 **综合建议**: ${overallSuggestion}`);
    
} else {
    dv.paragraph("⚪ 暂无任务，开始勾选上面的任务吧！");
}
```

---

## 🎯 今日三件事
1.
2.
3.

---

## 🤖 AI分析
*AI会根据上面的数据自动分析*

**运动数据**：
**时间分配**：
**效率评估**：
**建议优化**：

---

**完成时间**：14:18
