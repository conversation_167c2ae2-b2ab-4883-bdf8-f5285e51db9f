---
date: 2025-07-19
display_date: 2025年07月19日 星期六
created: 2025-07-19
week: 29
weekday: 6
tags: [日记, 2025, 07月]
---

# 📅 2025年07月19日 - 星期六 - 第29周

## 🎯 今日目标提醒（四象限法则）

> [!tip] 💡 重要性-紧急性四象限
> **优先级排序，时刻提醒自己专注重要的事情：**
>
> | 🔥 **重要且紧急** | 📋 **重要不紧急** |
> |------------------|------------------|
> | 立即处理的危机事件 | 计划、预防、能力建设 |
> | 截止日期压力的项目 | 人际关系、新机会 |
> | 紧急问题解决 | 休息、娱乐 |
>
> | ⚡ **紧急不重要** | 🗑️ **不重要不紧急** |
> |------------------|------------------|
> | 打断、电话、邮件 | 琐事、娱乐过度 |
> | 某些会议、活动 | 无意义的网络浏览 |
> | 紧急但可委托的事 | 时间浪费活动 |

**🎯 今日重点关注（重要不紧急象限）**：
1. ____________________
2. ____________________
3. ____________________

## 📝 今日记录
*想写什么就写什么*

---

## 🏃 今日运动安排

### 🎯 自由运动（周六专属）
**今日运动**：自选运动
**运动内容**：
- [ ] 运动类型：____ #exercise
- [ ] 运动时长：____分钟 #exercise
- [ ] 运动地点：____ #exercise

**实际完成**：
- 运动选择原因：____
- 运动感受：____

---

## 📊 今日数据录入

> [!info] 🎯 阅读模式交互输入
> **安装Meta Bind插件后，可在阅读模式下直接输入数据：**
> - 数字输入框：直接点击输入数字
> - 滑块：拖动选择数值
> - 数据自动保存到文件
>
> **插件安装**：设置 → 社区插件 → 搜索"Meta Bind" → 安装启用

### 😴 睡眠
- [ ] 睡眠时长记录：`INPUT[number:sleep]`小时 #data

### 🚶 步数
- [ ] 步数记录：`INPUT[number:steps]`步 #data

### 💼 工作学习
- [ ] 工作时长记录：`INPUT[number:work_hours]`小时 #work
- [ ] 学习时长记录：`INPUT[number:study_hours]`小时 #study
- [ ] 工作任务完成 #work
- [ ] 学习目标达成 #study

### 😊 心情
- [ ] 心情评分：`INPUT[slider:mood]` (1-10分) #data
- [ ] 心情记录完成 #data

---

## 📈 智能进度条系统

> [!tip] 💡 进度条更新方法
> **阅读模式下勾选任务后，进度条需要手动刷新：**
> - 方法1：按 **Ctrl+R** (Windows) 或 **Cmd+R** (Mac) 刷新页面
> - 方法2：切换到编辑模式再切回阅读模式
> - 方法3：按 **F5** 刷新整个页面
>
> **推荐**：勾选完所有任务后统一刷新一次即可

### 🎯 主要任务状态

```dataviewjs
// 智能主任务状态显示
const tasks = dv.current().file.tasks || [];

// 获取各类子任务
const exerciseTasks = tasks.filter(t => t.text.includes('#exercise'));
const workTasks = tasks.filter(t => t.text.includes('#work'));
const studyTasks = tasks.filter(t => t.text.includes('#study'));
const dataTasks = tasks.filter(t => t.text.includes('#data'));

// 计算完成状态
function getTaskStatus(subTasks, taskName) {
    if (subTasks.length === 0) return `- ⚪ ${taskName} (暂无子任务)`;

    const completed = subTasks.filter(t => t.completed).length;
    const total = subTasks.length;
    const percentage = Math.round((completed / total) * 100);

    let statusIcon, statusText;
    if (percentage === 100) {
        statusIcon = "✅"; statusText = "已完成";
    } else if (percentage >= 75) {
        statusIcon = "🎯"; statusText = "接近完成";
    } else if (percentage >= 50) {
        statusIcon = "💪"; statusText = "进行中";
    } else if (percentage >= 25) {
        statusIcon = "🚀"; statusText = "已开始";
    } else {
        statusIcon = "⏰"; statusText = "待开始";
    }

    return `- ${statusIcon} ${taskName} ${statusText} (${completed}/${total})`;
}

// 获取文件日期对应的运动任务名称
const fileDate = new Date(dv.current().date);
const dayNum = fileDate.getDay();
let exerciseTaskName = "";
if (dayNum === 1) exerciseTaskName = "胸部+背部训练";
else if (dayNum === 3) exerciseTaskName = "肩膀+腹部训练";
else if (dayNum === 5) exerciseTaskName = "腿部训练";
else if (dayNum === 2 || dayNum === 4) exerciseTaskName = "走路1万步";
else if (dayNum === 6) exerciseTaskName = "自由运动";
else exerciseTaskName = "休息恢复";

// 显示智能主任务状态
dv.paragraph(getTaskStatus(exerciseTasks, exerciseTaskName));
dv.paragraph(getTaskStatus(workTasks, "工作任务"));
dv.paragraph(getTaskStatus(studyTasks, "学习目标"));
dv.paragraph(getTaskStatus(dataTasks, "数据记录"));
```

### 📊 详细进度统计

```dataviewjs
// 获取当前页面的所有任务
const tasks = dv.current().file.tasks || [];

// 创建智能进度条函数
function createProgressBar(completed, total) {
    if (total === 0) return "⚪ 暂无任务";
    
    const percentage = Math.round((completed / total) * 100);
    const width = 10;
    const filledBars = Math.floor((percentage / 100) * width);
    const emptyBars = width - filledBars;
    
    let fillChar, emptyChar, statusIcon;
    
    if (percentage === 100) {
        fillChar = "🟢"; emptyChar = "⚪"; statusIcon = "✅";
    } else if (percentage >= 75) {
        fillChar = "🟡"; emptyChar = "⚪"; statusIcon = "🎯";
    } else if (percentage >= 50) {
        fillChar = "🔵"; emptyChar = "⚪"; statusIcon = "💪";
    } else if (percentage >= 25) {
        fillChar = "🟠"; emptyChar = "⚪"; statusIcon = "🚀";
    } else {
        fillChar = "🔴"; emptyChar = "⚪"; statusIcon = "⏰";
    }
    
    const progressBar = fillChar.repeat(Math.max(1, filledBars)) + emptyChar.repeat(emptyBars);
    return `${statusIcon} ${progressBar} **${percentage}%** (${completed}/${total})`;
}

// 获取智能建议
function getSuggestion(completed, total, category) {
    const percentage = Math.round((completed / total) * 100);
    const remaining = total - completed;
    
    if (percentage === 100) {
        return `🎉 ${category}全部完成！`;
    } else if (percentage >= 75) {
        return `👍 ${category}接近完成，还剩${remaining}项`;
    } else if (percentage >= 50) {
        return `💪 ${category}过半完成，继续加油`;
    } else if (percentage >= 25) {
        return `🚀 ${category}已开始，还需努力`;
    } else {
        return `⏰ ${category}需要开始行动了`;
    }
}

// 第二层：分类任务进度
dv.header(4, "📋 分类任务进度");

const categories = [
    { 
        name: "🏃 运动任务", 
        mainTag: "#task-exercise",
        subTag: "#exercise",
        mainTasks: tasks.filter(t => t.text.includes('#task-exercise')),
        subTasks: tasks.filter(t => t.text.includes('#exercise'))
    },
    { 
        name: "💼 工作任务", 
        mainTag: "#task-work",
        subTag: "#work",
        mainTasks: tasks.filter(t => t.text.includes('#task-work')),
        subTasks: tasks.filter(t => t.text.includes('#work'))
    },
    { 
        name: "📚 学习任务", 
        mainTag: "#task-study",
        subTag: "#study",
        mainTasks: tasks.filter(t => t.text.includes('#task-study')),
        subTasks: tasks.filter(t => t.text.includes('#study'))
    },
    { 
        name: "📊 数据记录", 
        mainTag: "#task-data",
        subTag: "#data",
        mainTasks: tasks.filter(t => t.text.includes('#task-data')),
        subTasks: tasks.filter(t => t.text.includes('#data'))
    }
];

categories.forEach(cat => {
    if (cat.subTasks.length > 0) {
        const subCompleted = cat.subTasks.filter(t => t.completed).length;
        const subTotal = cat.subTasks.length;
        const mainCompleted = cat.mainTasks.filter(t => t.completed).length;
        const mainTotal = cat.mainTasks.length;
        
        dv.paragraph(`**${cat.name}**:`);
        dv.paragraph(`└─ 子任务: ${createProgressBar(subCompleted, subTotal)}`);
        if (mainTotal > 0) {
            dv.paragraph(`└─ 主任务: ${createProgressBar(mainCompleted, mainTotal)}`);
        }
        dv.paragraph(`💡 ${getSuggestion(subCompleted, subTotal, cat.name.split(' ')[1])}`);
        dv.paragraph("");
    }
});

// 第三层：今日总进度
dv.header(4, "🎯 今日总进度统计");

const allMainTasks = tasks.filter(t => t.text.includes('#task-'));
const allSubTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allMainTasks.length > 0 || allSubTasks.length > 0) {
    const mainCompleted = allMainTasks.filter(t => t.completed).length;
    const mainTotal = allMainTasks.length;
    const subCompleted = allSubTasks.filter(t => t.completed).length;
    const subTotal = allSubTasks.length;
    
    // 计算综合完成度（主任务权重60%，子任务权重40%）
    let overallPercentage = 0;
    if (mainTotal > 0 && subTotal > 0) {
        const mainPercentage = (mainCompleted / mainTotal) * 100;
        const subPercentage = (subCompleted / subTotal) * 100;
        overallPercentage = Math.round(mainPercentage * 0.6 + subPercentage * 0.4);
    } else if (mainTotal > 0) {
        overallPercentage = Math.round((mainCompleted / mainTotal) * 100);
    } else if (subTotal > 0) {
        overallPercentage = Math.round((subCompleted / subTotal) * 100);
    }
    
    dv.paragraph(`**🌟 今日综合完成度**: ${createProgressBar(overallPercentage, 100).replace(/\(\d+\/\d+\)/, `(综合评分: ${overallPercentage}%)`)}`);
    dv.paragraph(`├─ 主要任务: ${createProgressBar(mainCompleted, mainTotal)}`);
    dv.paragraph(`└─ 详细任务: ${createProgressBar(subCompleted, subTotal)}`);
    
    // 综合建议
    let overallSuggestion = "";
    if (overallPercentage >= 90) {
        overallSuggestion = "🏆 今天表现卓越！所有任务都完成得很好！";
    } else if (overallPercentage >= 75) {
        overallSuggestion = "🎯 今天进展很好！再坚持一下就能完美收官！";
    } else if (overallPercentage >= 50) {
        overallSuggestion = "💪 今天过半完成！保持节奏，继续加油！";
    } else if (overallPercentage >= 25) {
        overallSuggestion = "🚀 今天已经开始！建议重点关注未完成的重要任务！";
    } else {
        overallSuggestion = "⏰ 今天需要加速了！建议从最重要的任务开始！";
    }
    
    dv.paragraph(`🎪 **综合建议**: ${overallSuggestion}`);
    
} else {
    dv.paragraph("⚪ 暂无任务，开始勾选上面的任务吧！");
}
```

---

## 🧠 情绪感受记录

### 📝 今日情绪事件（有什么触发情绪的事就简单记一下）

**事件1**：突然间开始想的有点多了，嗯，这个想的都是事实上是，这种关系已经早就结束，但是为什么我突然又在回忆思念呢？
- 🧠 脑子想的：就是我跟yy。就是觉得这个关系已经结束了，她已经结婚了，我不应该再有任何想法，而且我觉得。我觉得就是我想这么多又有什么意义，没有任何实际作用，我的感受好像没有办法控制。就会感受绑架我的思想，然后去做一些很奇怪的事情。
- 💓 身体感受：今天打完魔兽世界以后呢，然后去休息休息的时候，嗯就突然开始想很多啊就。有点开始往好的方向想，我也不知道为什么突然想着一些很奇怪的东西，嗯，现在突然就给忘了在想什么，然后我就突然去和ai去弄那个八字命理，然后后来我不是之前我的出生日期也是填错了后来唉也在yy的日期，然后我就重新搞了一下之前关系叫做容清变化是有摔，但现在是查的时候是本命有衰，然后变化是安坏，然后而且是双安坏，然后就又又好奇起来，好像又继续感受下去，然后又去找一下去搞这个八字全学这种东西，我觉得。太奇怪了，我就是我的感受好像这样才能够舒服一些。
- 🗣️ 嘴上说的：在上面两个就是我嘴上说的呗，就是我觉得我有点冲突矛盾，然后现在我又很想赚钱。嗯，就。我感觉就是我有点控制不了自己感受，所以我为什么想着如果发生了这些真实事实的一些东西，我就会想要记录下来，我想知道我的潜意识无意识当中会是怎么样的一个东西。如果从说以前到现在，其实我的变化也是很明显，就是慢慢好像是接受了这段感情已经丢失了，就是能够接受这些东西，以前是好像没有不能够接受。之前会被影响，现在就不会被影响。然后我一直在想着更加专注的去执行自己的这些计划和目标，能够真正成成功。这样有点逆天改命哈哈哈哈哈哈有点鸡汤就是唉，说到这里就顺便通知我就觉得当时如果我越去强求一些东西的时候越会很奇怪的，不太可能成功，但现在在想。那。我不强求。不就没有目标吗？顺其自然，那么强求又不能成功，不强求又tmd没有目标感，这就很奇怪，我应该怎么做呢？怎么说就是强求，我就有一种很刻意的心理，因为这条路该走啊，我就拼命的干，然后最后没有人成功我心里就会失衡，那么然后这时候就会想着，唉，为了心里不适合，那我就啊。就不要目标了，就是顺其自然，然后就会慢了，然后最后啊果然当时按这样子做就这样就成就是我好像有点难调。平衡，这种这种之间的关系就是同样的，我就觉得为什么总是事情差一点感觉就是这样子。
- 🏃 行动上的：嗯，最后花了一个小时去搞这种命理玄学。。

**事件2**：____________________
- 🧠 脑子想的：____________________
- 💓 身体感受：____________________
- 🗣️ 嘴上说的：____________________
- 🏃 行动上的：____________________

### 🎭 今日主要情绪（勾选就行）
- [ ] 😊 开心 #emotion
- [ ] 😔 难过 #emotion
- [x] 😰 焦虑 #emotion
- [ ] 😠 生气 #emotion
- [ ] 😴 疲惫 #emotion
- [ ] 😌 平静 #emotion
- [x] 🤔 困惑 #emotion
- [x] 😤 烦躁 #emotion

### 💭 今日感受总结
今天的情绪状态比较复杂，主要体现在内心的矛盾和冲突上。一方面，理性上已经接受了过往感情的结束，知道不应该再有想法；但另一方面，感受似乎有自己的节奏，会突然涌现一些思念和回忆。

这种状态让我感到困惑和焦虑，特别是在"强求"与"顺其自然"之间难以找到平衡点。想要有目标和动力去成功，但又担心过度强求会导致失衡；想要顺其自然保持平静，但又怕失去目标感和动力。

今天花了时间去算命理，可能是潜意识里还在寻找某种"可能性"或"答案"，这反映了内心深处还没有完全放下。虽然行为上有些不理性，但这可能是哀伤整合过程中的正常表现。

总的来说，今天是一个自我觉察的日子，虽然有些烦躁和困惑，但通过记录和反思，对自己的内心状态有了更清晰的认识。

---

## 🎯 今日三件事
1.
2.
3.

---

## 🤖 AI分析
*AI会根据上面的数据自动分析*

**运动数据**：
**时间分配**：
**效率评估**：
**建议优化**：

---

## 💰 财务概览

> [!tip] 💡 使用右侧边栏快速记录
> 点击右侧边栏的"💸 快速支出"或"💰 快速收入"按钮进行记录

### 📊 今日财务数据

**💰 收入总计**：____元 | **💸 支出总计**：____元 | **💵 结余**：____元

### 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |
|------|----------|------|----------|------|

### 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|

### 📊 今日财务总结

**💰 今日收入总计**：____元
**💸 今日支出总计**：____元
**💵 今日结余**：____元

**📋 支出分类统计**：
- 🍽️ 餐饮：____元 | 🚗 交通：____元 | 🛍️ 购物：____元
- 🎮 娱乐：____元 | 📚 学习：____元 | 🏥 医疗：____元
- 🏠 房租水电：____元 | 📱 通讯：____元 | 🔄 其他：____元

**💭 财务反思**：
- 今日最大支出：____________________
- 是否有冲动消费：____________________
- 明日消费计划：____________________

---

**完成时间**：00:03
