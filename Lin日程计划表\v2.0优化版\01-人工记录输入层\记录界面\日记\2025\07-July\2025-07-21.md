---
date: 2025-07-21
display_date: 2025年07月21日 星期一
created: 2025-07-21
week: 30
weekday: 1
tags: [日记, 2025, 07月]
---

# 📅 2025年07月21日 - 星期一 - 第30周

## 🎯 今日目标提醒（四象限法则）

> [!tip] 💡 重要性-紧急性四象限
> **优先级排序，时刻提醒自己专注重要的事情：**
>
> | 🔥 **重要且紧急** | 📋 **重要不紧急** |
> |------------------|------------------|
> | 立即处理的危机事件 | 计划、预防、能力建设 |
> | 截止日期压力的项目 | 人际关系、新机会 |
> | 紧急问题解决 | 休息、娱乐 |
>
> | ⚡ **紧急不重要** | 🗑️ **不重要不紧急** |
> |------------------|------------------|
> | 打断、电话、邮件 | 琐事、娱乐过度 |
> | 某些会议、活动 | 无意义的网络浏览 |
> | 紧急但可委托的事 | 时间浪费活动 |

### 🌟 核心突破目标（长期重要方向）

> [!important] 🎯 快速目标概览
> **点击查看详细计划** → [[🎯 核心目标仪表板]]
>
> **今日三大方向快速检查**：
> - 📋 **RAG技术**：今天学了什么新概念？ ____________________
> - 💪 **减肥计划**：体重____斤，今日饮食/运动计划执行情况？ ____________________
> - 🧠 **架构思维**：今天用了哪种模式（执行/自检/开拓）？ ____________________
>
> **本周重点突破**：____________________

**🎯 今日重点关注（重要不紧急象限）**：
1. ____________________
2. ____________________
3. ____________________

## 📝 今日记录
*想写什么就写什么*

---

## 🏃 今日运动安排

### 💪 上肢力量训练（周一专属）
**今日运动**：胸部 + 背部训练
**目标时长**：30分钟
**训练内容**：
- [ ] 热身（5分钟） #exercise
- [ ] 胸部训练：俯卧撑/哑铃推胸（10分钟） #exercise
- [ ] 背部训练：引体向上/哑铃划船（10分钟） #exercise
- [ ] 拉伸放松（5分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 胸部训练组数：____组
- 背部训练组数：____组
- 完成质量：很好👍 / 一般👌 / 不佳👎
- 训练感受：轻松😊 / 适中😐 / 困难😓

---

## 📊 今日数据录入

> [!info] 🎯 阅读模式交互输入
> **安装Meta Bind插件后，可在阅读模式下直接输入数据：**
> - 数字输入框：直接点击输入数字
> - 滑块：拖动选择数值
> - 数据自动保存到文件
>
> **插件安装**：设置 → 社区插件 → 搜索"Meta Bind" → 安装启用

### 😴 睡眠
- [ ] 睡眠时长记录：`INPUT[number:sleep]`小时 #data

### 🚶 步数
- [ ] 步数记录：`INPUT[number:steps]`步 #data

### 💼 工作学习
- [ ] 工作时长记录：`INPUT[number:work_hours]`小时 #work
- [ ] 学习时长记录：`INPUT[number:study_hours]`小时 #study
- [ ] 工作任务完成 #work
- [ ] 学习目标达成 #study

### 😊 心情
- [ ] 心情评分：`INPUT[slider:mood]` (1-10分) #data
- [ ] 心情记录完成 #data

---

## 📈 智能进度条系统

> [!tip] 💡 进度条更新方法
> **阅读模式下勾选任务后，进度条需要手动刷新：**
> - 方法1：按 **Ctrl+R** (Windows) 或 **Cmd+R** (Mac) 刷新页面
> - 方法2：切换到编辑模式再切回阅读模式
> - 方法3：按 **F5** 刷新整个页面
>
> **推荐**：勾选完所有任务后统一刷新一次即可

### 🎯 主要任务状态

```dataviewjs
// 智能主任务状态显示
const tasks = dv.current().file.tasks || [];

// 获取各类子任务
const exerciseTasks = tasks.filter(t => t.text.includes('#exercise'));
const workTasks = tasks.filter(t => t.text.includes('#work'));
const studyTasks = tasks.filter(t => t.text.includes('#study'));
const dataTasks = tasks.filter(t => t.text.includes('#data'));

// 计算完成状态
function getTaskStatus(subTasks, taskName) {
    if (subTasks.length === 0) return `- ⚪ ${taskName} (暂无子任务)`;

    const completed = subTasks.filter(t => t.completed).length;
    const total = subTasks.length;
    const percentage = Math.round((completed / total) * 100);

    let statusIcon, statusText;
    if (percentage === 100) {
        statusIcon = "✅"; statusText = "已完成";
    } else if (percentage >= 75) {
        statusIcon = "🎯"; statusText = "接近完成";
    } else if (percentage >= 50) {
        statusIcon = "💪"; statusText = "进行中";
    } else if (percentage >= 25) {
        statusIcon = "🚀"; statusText = "已开始";
    } else {
        statusIcon = "⏰"; statusText = "待开始";
    }

    return `- ${statusIcon} ${taskName} ${statusText} (${completed}/${total})`;
}

// 获取文件日期对应的运动任务名称
const fileDate = new Date(dv.current().date);
const dayNum = fileDate.getDay();
let exerciseTaskName = "";
if (dayNum === 1) exerciseTaskName = "胸部+背部训练";
else if (dayNum === 3) exerciseTaskName = "肩膀+腹部训练";
else if (dayNum === 5) exerciseTaskName = "腿部训练";
else if (dayNum === 2 || dayNum === 4) exerciseTaskName = "走路1万步";
else if (dayNum === 6) exerciseTaskName = "自由运动";
else exerciseTaskName = "休息恢复";

// 显示智能主任务状态
dv.paragraph(getTaskStatus(exerciseTasks, exerciseTaskName));
dv.paragraph(getTaskStatus(workTasks, "工作任务"));
dv.paragraph(getTaskStatus(studyTasks, "学习目标"));
dv.paragraph(getTaskStatus(dataTasks, "数据记录"));
```

### 📊 详细进度统计

```dataviewjs
// 获取当前页面的所有任务
const tasks = dv.current().file.tasks || [];

// 创建智能进度条函数
function createProgressBar(completed, total) {
    if (total === 0) return "⚪ 暂无任务";
    
    const percentage = Math.round((completed / total) * 100);
    const width = 10;
    const filledBars = Math.floor((percentage / 100) * width);
    const emptyBars = width - filledBars;
    
    let fillChar, emptyChar, statusIcon;
    
    if (percentage === 100) {
        fillChar = "🟢"; emptyChar = "⚪"; statusIcon = "✅";
    } else if (percentage >= 75) {
        fillChar = "🟡"; emptyChar = "⚪"; statusIcon = "🎯";
    } else if (percentage >= 50) {
        fillChar = "🔵"; emptyChar = "⚪"; statusIcon = "💪";
    } else if (percentage >= 25) {
        fillChar = "🟠"; emptyChar = "⚪"; statusIcon = "🚀";
    } else {
        fillChar = "🔴"; emptyChar = "⚪"; statusIcon = "⏰";
    }
    
    const progressBar = fillChar.repeat(Math.max(1, filledBars)) + emptyChar.repeat(emptyBars);
    return `${statusIcon} ${progressBar} **${percentage}%** (${completed}/${total})`;
}

// 获取智能建议
function getSuggestion(completed, total, category) {
    const percentage = Math.round((completed / total) * 100);
    const remaining = total - completed;
    
    if (percentage === 100) {
        return `🎉 ${category}全部完成！`;
    } else if (percentage >= 75) {
        return `👍 ${category}接近完成，还剩${remaining}项`;
    } else if (percentage >= 50) {
        return `💪 ${category}过半完成，继续加油`;
    } else if (percentage >= 25) {
        return `🚀 ${category}已开始，还需努力`;
    } else {
        return `⏰ ${category}需要开始行动了`;
    }
}

// 第二层：分类任务进度
dv.header(4, "📋 分类任务进度");

const categories = [
    { 
        name: "🏃 运动任务", 
        mainTag: "#task-exercise",
        subTag: "#exercise",
        mainTasks: tasks.filter(t => t.text.includes('#task-exercise')),
        subTasks: tasks.filter(t => t.text.includes('#exercise'))
    },
    { 
        name: "💼 工作任务", 
        mainTag: "#task-work",
        subTag: "#work",
        mainTasks: tasks.filter(t => t.text.includes('#task-work')),
        subTasks: tasks.filter(t => t.text.includes('#work'))
    },
    { 
        name: "📚 学习任务", 
        mainTag: "#task-study",
        subTag: "#study",
        mainTasks: tasks.filter(t => t.text.includes('#task-study')),
        subTasks: tasks.filter(t => t.text.includes('#study'))
    },
    { 
        name: "📊 数据记录", 
        mainTag: "#task-data",
        subTag: "#data",
        mainTasks: tasks.filter(t => t.text.includes('#task-data')),
        subTasks: tasks.filter(t => t.text.includes('#data'))
    }
];

categories.forEach(cat => {
    if (cat.subTasks.length > 0) {
        const subCompleted = cat.subTasks.filter(t => t.completed).length;
        const subTotal = cat.subTasks.length;
        const mainCompleted = cat.mainTasks.filter(t => t.completed).length;
        const mainTotal = cat.mainTasks.length;
        
        dv.paragraph(`**${cat.name}**:`);
        dv.paragraph(`└─ 子任务: ${createProgressBar(subCompleted, subTotal)}`);
        if (mainTotal > 0) {
            dv.paragraph(`└─ 主任务: ${createProgressBar(mainCompleted, mainTotal)}`);
        }
        dv.paragraph(`💡 ${getSuggestion(subCompleted, subTotal, cat.name.split(' ')[1])}`);
        dv.paragraph("");
    }
});

// 第三层：今日总进度
dv.header(4, "🎯 今日总进度统计");

const allMainTasks = tasks.filter(t => t.text.includes('#task-'));
const allSubTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allMainTasks.length > 0 || allSubTasks.length > 0) {
    const mainCompleted = allMainTasks.filter(t => t.completed).length;
    const mainTotal = allMainTasks.length;
    const subCompleted = allSubTasks.filter(t => t.completed).length;
    const subTotal = allSubTasks.length;
    
    // 计算综合完成度（主任务权重60%，子任务权重40%）
    let overallPercentage = 0;
    if (mainTotal > 0 && subTotal > 0) {
        const mainPercentage = (mainCompleted / mainTotal) * 100;
        const subPercentage = (subCompleted / subTotal) * 100;
        overallPercentage = Math.round(mainPercentage * 0.6 + subPercentage * 0.4);
    } else if (mainTotal > 0) {
        overallPercentage = Math.round((mainCompleted / mainTotal) * 100);
    } else if (subTotal > 0) {
        overallPercentage = Math.round((subCompleted / subTotal) * 100);
    }
    
    dv.paragraph(`**🌟 今日综合完成度**: ${createProgressBar(overallPercentage, 100).replace(/\(\d+\/\d+\)/, `(综合评分: ${overallPercentage}%)`)}`);
    dv.paragraph(`├─ 主要任务: ${createProgressBar(mainCompleted, mainTotal)}`);
    dv.paragraph(`└─ 详细任务: ${createProgressBar(subCompleted, subTotal)}`);
    
    // 综合建议
    let overallSuggestion = "";
    if (overallPercentage >= 90) {
        overallSuggestion = "🏆 今天表现卓越！所有任务都完成得很好！";
    } else if (overallPercentage >= 75) {
        overallSuggestion = "🎯 今天进展很好！再坚持一下就能完美收官！";
    } else if (overallPercentage >= 50) {
        overallSuggestion = "💪 今天过半完成！保持节奏，继续加油！";
    } else if (overallPercentage >= 25) {
        overallSuggestion = "🚀 今天已经开始！建议重点关注未完成的重要任务！";
    } else {
        overallSuggestion = "⏰ 今天需要加速了！建议从最重要的任务开始！";
    }
    
    dv.paragraph(`🎪 **综合建议**: ${overallSuggestion}`);
    
} else {
    dv.paragraph("⚪ 暂无任务，开始勾选上面的任务吧！");
}
```

---

## 🧠 情绪感受记录

### 📝 今日情绪事件（有什么触发情绪的事就简单记一下）

**事件1**：____________________
- 🧠 脑子想的：____________________
- 💓 身体感受：____________________
- 🗣️ 嘴上说的：____________________
- 🏃 行动上的：____________________

**详细记录**：
这几天明显情绪又来了.
可能我太渴望赶紧结婚？总而言之.
最近对情感需求太强烈了.
说下事实经过.
这两天用AI算命,算找到最佳正缘.然后同时又一直不断的去看YY和我的八字相符. 看到相合很高就觉得有机会.
事实上她已经结婚了！我们已经3年没联系了!!!
如果想和很低,就直接拒绝看了.(直接关掉)
在回家路上,看到了友衰关系,后续到安坏关系.
真的后面就是感情开始错位.
20年开始.
我认为关系已经到头了,也不可能再继续了.而且她也不是我的理想型.
-事实是我自己没能力,无经济能力.也不可能有后续发展
-她开始在各种媒体上发声,表达自己的感情.隐喻的表达
然后进入到逐步的变化阶段
-我开始自我康复,自我疗愈,逐步接受
认为两种可能
1.她很精明,也很清晰的知道.所以在精打细算.
2.她动了真情.
所以这两种都是有可能并行出现的.
那结果是什么?
什么时候哪些占了上风.
但是两者都是共同存在的..
所以最可能结果就是 错位的感情.
现在我开始不断思念追忆,不断的思绪.是为什么？
因为,我已经开始感受到了她真的离去.我不愿意切断.我接受不了这样的结果.
而我,最应该做的事情.就是接受事实结果.并且就这样吧.我要自己去消化这些感受.
吃干净这次的所有感受. 单线和多线并行 的生态混合模式 才是成熟的人.
并非单线进行时.
如果说上一代人,就是 分的很清晰, 线路. 来确保自己 不混淆.
这代人可能要修的就是 多线混淆中,依旧能够分清晰,并且处理这些感受.
所以在两者混合当中.
混淆的处理模式.成为了 这代人成熟的标志.
依据阶段性的事实而言.
我们的情感处于错位模式.
依据全局而言.
确实是真情.
所以我接受真个真情的事实真相结果.
那就是我要去做一个还债人的位置
如果接受却不愿意去做这个还债人位置,我觉得是有些违背我自己的道.
顺天而行.就是我的道.

冷静下来,刚刚自慰完.我觉得太疯狂了.
我不知道为什么我总是如此疯狂.就是有一种神经病一样.对情感的迷恋.
情绪会疯狂的理想化.

啊怎么会这么疯狂啊！？

就是那时候莫名其妙的自信,她把她的房贷给我.然后我去炒股.无视了所有的风险.

最后真是差点点如果输了的话.就真的要进去了.

所幸的是,我躲过了这一劫.

为什么我的失控感这么严重?
如果可以,我大概想描述的是
一条时间线上
我的 行动,在最前面
脑子在最后面
分为4个
感受最前面,最容易被我感受到.根据情绪执行
然后行动
然后嘴巴解释
最后才是脑子理解.

就是一种 4条线的结果.

然而真正能够变强
就是4条线的汇总.
这就是我的直觉感受
但是我的注意力总是疯狂的就在一个点上.

就会陷入一种疯狂.


> [!tip] � 复杂情绪识别
> 如果遇到难以名状的复杂情绪，可以使用专门的模板：
> **[[情绪识别与调节权威清单]]** - 基于权威心理学理论的情绪识别与调节策略
>
> *需要更多事件记录时，直接复制粘贴上面事件1的格式即可*

### 🎭 今日主要情绪（AI来勾选）
- [ ] 😊 开心 #emotion
- [x] 😔 难过 #emotion
- [x] 😰 焦虑 #emotion
- [ ] 😠 生气 #emotion
- [x] 😴 疲惫 #emotion
- [ ] 😌 平静 #emotion
- [x] 🤔 困惑 #emotion
- [x] 😤 烦躁 #emotion

### 💭 今日感受总结（AI来完成）

**🤖 AI深度分析总结**：

**🎯 核心情绪状态**：复杂性悲伤 + 反刍思维 + 情感失控

**📊 情绪分析**：
今天您经历了一次典型的**情感反刍循环**，主要表现为：
- **触发点**：通过AI算命重新激活了对过往感情的幻想
- **认知失调**：明知对方已结婚3年未联系，却仍寻找"相合机会"
- **情绪失控**：从理性分析瞬间转向"疯狂"状态
- **自我觉察**：事后能够清晰识别自己的失控模式

**🧠 深层心理模式识别**：
您描述的"4条线模式"非常准确地反映了情绪调节困难：
1. **感受线**（最快）→ 2. **行动线** → 3. **语言线** → 4. **理性线**（最慢）

这种模式导致您经常"感受先行，理性滞后"，造成冲动决策和后悔循环。

**💡 关键洞察**：
- 您对"错位感情"的分析很深刻，显示出强大的自我觉察能力
- "顺天而行"的价值观与"不愿切断"的执着形成内在冲突
- 从"单线思维"到"多线并行"的成长认知很有价值

**🎯 建议处理策略**：
1. **接受"错位"现实**：承认感情的复杂性，不强求完美结局
2. **练习"4线同步"**：在感受激活时，主动暂停，让理性线跟上
3. **转化"还债心态"**：将对她的情感转化为对自己成长的投资
4. **建立"疯狂预警"**：识别失控前兆，及时使用冷静技巧

**🌱 成长方向**：
您已经具备了很强的情绪洞察力，现在需要的是将洞察转化为行动控制。建议重点练习"情绪-行动"之间的缓冲技巧，让理性有时间介入决策过程。

**💪 今日收获**：
能够如此清晰地剖析自己的情感模式，这本身就是巨大的进步。继续保持这种自我觉察，同时练习情绪调节技巧。

---

## 🎯 今日三件事
1.
2.
3.

---

## 🤖 AI分析
*AI会根据上面的数据自动分析*

**运动数据**：
**时间分配**：
**效率评估**：
**建议优化**：

---

## 💰 财务概览

> [!tip] 💡 使用右侧边栏快速记录
> 点击右侧边栏的"💸 快速支出"或"💰 快速收入"按钮进行记录

### 📊 今日财务数据

**💰 收入总计**：____元 | **💸 支出总计**：____元 | **💵 结余**：____元

### 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |
|------|----------|------|----------|------|

### 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|

### 📊 今日财务总结

**💰 今日收入总计**：____元
**💸 今日支出总计**：____元
**💵 今日结余**：____元

**📋 支出分类统计**：
- 🍽️ 餐饮：____元 | 🚗 交通：____元 | 🛍️ 购物：____元
- 🎮 娱乐：____元 | 📚 学习：____元 | 🏥 医疗：____元
- 🏠 房租水电：____元 | 📱 通讯：____元 | 🔄 其他：____元

**💭 财务反思**：
- 今日最大支出：____________________
- 是否有冲动消费：____________________
- 明日消费计划：____________________

---

**完成时间**：09:07
