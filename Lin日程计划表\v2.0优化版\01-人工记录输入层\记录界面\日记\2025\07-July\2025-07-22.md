---
date: 2025-07-22
display_date: 2025年07月22日 星期二
created: 2025-07-22
week: 30
weekday: 2
tags:
  - 日记
  - 2025
  - 07月
sleep: 6.2
steps: 10430
---
等一下刚回家，我在处理文档，等一下看看。嗯，怎么说？怎么说怎么说怎么说。累啊，不知道啊，你等我你等我搞完这个文档我看感觉一下
# 📅 2025年07月22日 - 星期二 - 第30周

## 🎯 今日目标提醒（四象限法则）

> [!tip] 💡 重要性-紧急性四象限
> **优先级排序，时刻提醒自己专注重要的事情：**
>
> | 🔥 **重要且紧急** | 📋 **重要不紧急** |
> |----------
|--------|------------------|
> | 立即处理的危机事件 | 计划、预防、能力建设 |
> | 截止日期压力的项目 | 人际关系、新机会 |
> | 紧急问题解决 | 休息、娱乐 |
>
> | ⚡ **紧急不重要** | 🗑️ **不重要不紧急** |
> |------------------|------------------|
> | 打断、电话、邮件 | 琐事、娱乐过度 |
> | 某些会议、活动 | 无意义的网络浏览 |
> | 紧急但可委托的事 | 时间浪费活动 |

**🎯 今日重点关注（重要不紧急象限）**：
1. ____________________
2. ____________________
3. ____________________

## 📝 今日记录，我这样有点累
*想写什么就写什么*

---

## 🏃 今日运动安排

### 🚶 走路运动（周二四专属）
**今日运动**：走路1万步
**目标时长**：60分钟
**运动内容**：
- [x] 走路1万步 #exercise ✅ 2025-07-22
- [ ] 路线选择：____ #exercise
- [ ] 拉伸放松（10分钟） #exercise

**实际完成**：
- 实际用时：____分钟
- 实际步数：____步
- 运动感受：很爽😄 / 还行😊 / 累😴
- 天气影响：无影响☀️ / 有影响🌧️

---

## 📊 今日数据录入

> [!info] 🎯 阅读模式交互输入
> **安装Meta Bind插件后，可在阅读模式下直接输入数据：**
> - 数字输入框：直接点击输入数字
> - 滑块：拖动选择数值
> - 数据自动保存到文件
>
> **插件安装**：设置 → 社区插件 → 搜索"Meta Bind" → 安装启用

### 😴 睡眠
- [ ] 睡眠时长记录：`INPUT[number:sleep]`小时 #data

### 🚶 步数
- [ ] 步数记录：`INPUT[number:steps]`步 #data

### 💼 工作学习
- [ ] 工作时长记录：`INPUT[number:work_hours]`小时 #work
- [ ] 学习时长记录：`INPUT[number:study_hours]`小时 #study
- [ ] 工作任务完成 #work
- [ ] 学习目标达成 #study

### 😊 心情
- [ ] 心情评分：`INPUT[slider:mood]` (1-10分) #data
- [ ] 心情记录完成 #data

---

## 📈 智能进度条系统

> [!tip] 💡 进度条更新方法
> **阅读模式下勾选任务后，进度条需要手动刷新：**
> - 方法1：按 **Ctrl+R** (Windows) 或 **Cmd+R** (Mac) 刷新页面
> - 方法2：切换到编辑模式再切回阅读模式
> - 方法3：按 **F5** 刷新整个页面
>
> **推荐**：勾选完所有任务后统一刷新一次即可

### 🎯 主要任务状态

```dataviewjs
// 智能主任务状态显示
const tasks = dv.current().file.tasks || [];

// 获取各类子任务
const exerciseTasks = tasks.filter(t => t.text.includes('#exercise'));
const workTasks = tasks.filter(t => t.text.includes('#work'));
const studyTasks = tasks.filter(t => t.text.includes('#study'));
const dataTasks = tasks.filter(t => t.text.includes('#data'));

// 计算完成状态
function getTaskStatus(subTasks, taskName) {
    if (subTasks.length === 0) return `- ⚪ ${taskName} (暂无子任务)`;

    const completed = subTasks.filter(t => t.completed).length;
    const total = subTasks.length;
    const percentage = Math.round((completed / total) * 100);

    let statusIcon, statusText;
    if (percentage === 100) {
        statusIcon = "✅"; statusText = "已完成";
    } else if (percentage >= 75) {
        statusIcon = "🎯"; statusText = "接近完成";
    } else if (percentage >= 50) {
        statusIcon = "💪"; statusText = "进行中";
    } else if (percentage >= 25) {
        statusIcon = "🚀"; statusText = "已开始";
    } else {
        statusIcon = "⏰"; statusText = "待开始";
    }

    return `- ${statusIcon} ${taskName} ${statusText} (${completed}/${total})`;
}

// 获取文件日期对应的运动任务名称
const fileDate = new Date(dv.current().date);
const dayNum = fileDate.getDay();
let exerciseTaskName = "";
if (dayNum === 1) exerciseTaskName = "胸部+背部训练";
else if (dayNum === 3) exerciseTaskName = "肩膀+腹部训练";
else if (dayNum === 5) exerciseTaskName = "腿部训练";
else if (dayNum === 2 || dayNum === 4) exerciseTaskName = "走路1万步";
else if (dayNum === 6) exerciseTaskName = "自由运动";
else exerciseTaskName = "休息恢复";

// 显示智能主任务状态
dv.paragraph(getTaskStatus(exerciseTasks, exerciseTaskName));
dv.paragraph(getTaskStatus(workTasks, "工作任务"));
dv.paragraph(getTaskStatus(studyTasks, "学习目标"));
dv.paragraph(getTaskStatus(dataTasks, "数据记录"));
```

### 📊 详细进度统计

```dataviewjs
// 获取当前页面的所有任务
const tasks = dv.current().file.tasks || [];

// 创建智能进度条函数
function createProgressBar(completed, total) {
    if (total === 0) return "⚪ 暂无任务";
    
    const percentage = Math.round((completed / total) * 100);
    const width = 10;
    const filledBars = Math.floor((percentage / 100) * width);
    const emptyBars = width - filledBars;
    
    let fillChar, emptyChar, statusIcon;
    
    if (percentage === 100) {
        fillChar = "🟢"; emptyChar = "⚪"; statusIcon = "✅";
    } else if (percentage >= 75) {
        fillChar = "🟡"; emptyChar = "⚪"; statusIcon = "🎯";
    } else if (percentage >= 50) {
        fillChar = "🔵"; emptyChar = "⚪"; statusIcon = "💪";
    } else if (percentage >= 25) {
        fillChar = "🟠"; emptyChar = "⚪"; statusIcon = "🚀";
    } else {
        fillChar = "🔴"; emptyChar = "⚪"; statusIcon = "⏰";
    }
    
    const progressBar = fillChar.repeat(Math.max(1, filledBars)) + emptyChar.repeat(emptyBars);
    return `${statusIcon} ${progressBar} **${percentage}%** (${completed}/${total})`;
}

// 获取智能建议
function getSuggestion(completed, total, category) {
    const percentage = Math.round((completed / total) * 100);
    const remaining = total - completed;
    
    if (percentage === 100) {
        return `🎉 ${category}全部完成！`;
    } else if (percentage >= 75) {
        return `👍 ${category}接近完成，还剩${remaining}项`;
    } else if (percentage >= 50) {
        return `💪 ${category}过半完成，继续加油`;
    } else if (percentage >= 25) {
        return `🚀 ${category}已开始，还需努力`;
    } else {
        return `⏰ ${category}需要开始行动了`;
    }
}

// 第二层：分类任务进度
dv.header(4, "📋 分类任务进度");

const categories = [
    { 
        name: "🏃 运动任务", 
        mainTag: "#task-exercise",
        subTag: "#exercise",
        mainTasks: tasks.filter(t => t.text.includes('#task-exercise')),
        subTasks: tasks.filter(t => t.text.includes('#exercise'))
    },
    { 
        name: "💼 工作任务", 
        mainTag: "#task-work",
        subTag: "#work",
        mainTasks: tasks.filter(t => t.text.includes('#task-work')),
        subTasks: tasks.filter(t => t.text.includes('#work'))
    },
    { 
        name: "📚 学习任务", 
        mainTag: "#task-study",
        subTag: "#study",
        mainTasks: tasks.filter(t => t.text.includes('#task-study')),
        subTasks: tasks.filter(t => t.text.includes('#study'))
    },
    { 
        name: "📊 数据记录", 
        mainTag: "#task-data",
        subTag: "#data",
        mainTasks: tasks.filter(t => t.text.includes('#task-data')),
        subTasks: tasks.filter(t => t.text.includes('#data'))
    }
];

categories.forEach(cat => {
    if (cat.subTasks.length > 0) {
        const subCompleted = cat.subTasks.filter(t => t.completed).length;
        const subTotal = cat.subTasks.length;
        const mainCompleted = cat.mainTasks.filter(t => t.completed).length;
        const mainTotal = cat.mainTasks.length;
        
        dv.paragraph(`**${cat.name}**:`);
        dv.paragraph(`└─ 子任务: ${createProgressBar(subCompleted, subTotal)}`);
        if (mainTotal > 0) {
            dv.paragraph(`└─ 主任务: ${createProgressBar(mainCompleted, mainTotal)}`);
        }
        dv.paragraph(`💡 ${getSuggestion(subCompleted, subTotal, cat.name.split(' ')[1])}`);
        dv.paragraph("");
    }
});

// 第三层：今日总进度
dv.header(4, "🎯 今日总进度统计");

const allMainTasks = tasks.filter(t => t.text.includes('#task-'));
const allSubTasks = tasks.filter(t => 
    t.text.includes('#exercise') || 
    t.text.includes('#work') || 
    t.text.includes('#study') || 
    t.text.includes('#data')
);

if (allMainTasks.length > 0 || allSubTasks.length > 0) {
    const mainCompleted = allMainTasks.filter(t => t.completed).length;
    const mainTotal = allMainTasks.length;
    const subCompleted = allSubTasks.filter(t => t.completed).length;
    const subTotal = allSubTasks.length;
    
    // 计算综合完成度（主任务权重60%，子任务权重40%）
    let overallPercentage = 0;
    if (mainTotal > 0 && subTotal > 0) {
        const mainPercentage = (mainCompleted / mainTotal) * 100;
        const subPercentage = (subCompleted / subTotal) * 100;
        overallPercentage = Math.round(mainPercentage * 0.6 + subPercentage * 0.4);
    } else if (mainTotal > 0) {
        overallPercentage = Math.round((mainCompleted / mainTotal) * 100);
    } else if (subTotal > 0) {
        overallPercentage = Math.round((subCompleted / subTotal) * 100);
    }
    
    dv.paragraph(`**🌟 今日综合完成度**: ${createProgressBar(overallPercentage, 100).replace(/\(\d+\/\d+\)/, `(综合评分: ${overallPercentage}%)`)}`);
    dv.paragraph(`├─ 主要任务: ${createProgressBar(mainCompleted, mainTotal)}`);
    dv.paragraph(`└─ 详细任务: ${createProgressBar(subCompleted, subTotal)}`);
    
    // 综合建议
    let overallSuggestion = "";
    if (overallPercentage >= 90) {
        overallSuggestion = "🏆 今天表现卓越！所有任务都完成得很好！";
    } else if (overallPercentage >= 75) {
        overallSuggestion = "🎯 今天进展很好！再坚持一下就能完美收官！";
    } else if (overallPercentage >= 50) {
        overallSuggestion = "💪 今天过半完成！保持节奏，继续加油！";
    } else if (overallPercentage >= 25) {
        overallSuggestion = "🚀 今天已经开始！建议重点关注未完成的重要任务！";
    } else {
        overallSuggestion = "⏰ 今天需要加速了！建议从最重要的任务开始！";
    }
    
    dv.paragraph(`🎪 **综合建议**: ${overallSuggestion}`);
    
} else {
    dv.paragraph("⚪ 暂无任务，开始勾选上面的任务吧！");
}
```

---

## 🧠 情绪感受记录

### 📝 今日情绪事件（有什么触发情绪的事就简单记一下）

## **事件1**：对AI回答真实性的深度质疑与自我反思
- 🧠 脑子想的：AI的回答像镜子一样反映我的潜意识需求，而非真相；质疑自己的判断是否被感受和偏见挟持
- 💓 身体感受：内心有强烈的撕裂感和分裂感，感到冲突但又想要突破和被理解
- 🗣️ 嘴上说的："接受这种撕裂的分裂感，这就是我"；"打破自己的牢笼，过上想要的人生"
- 🏃 行动上的：深夜进行哲学思辨，记录复杂想法，试图通过文字整理内心冲突

**详细记录**：
ai的回答有一种
你想要什么样，就会是什么样
就好像一面镜子
深深的照射出你潜意识

直到你舒服为止（意思是，根据我的反馈来给出答案。）
而这个答案

本质上是 你自己想要，而非真相

我就像水？就像是我相信了命运

不也是自己看了八字最后的出来的吗？

事实上

何为自己的判断

就是感觉

就是这个了！的感受

这种感受很神奇

可能这些本质上都是偏见！

就像我的感受会挟持我的思想

我自己想要什么，就会觉得需要什么

刚刚我潜意识打了你，实际上依旧是在说我自己

接受这种情况。这种撕裂的分裂感

然后，这就是我。因为我过得不幸福，没能过上自己想要的

不断的反刍思维。我现在只有一件事情，做好当下每一件事！

让我自己的自尊心才能得到真正的保护

接受现实，然后痛快的被伤害

接受感受指引！

突破自己！

一点点一步步

完成自己的理想！
如果我是一个不受约束的水
最终又会流向哪里？谁也不知道

只有尽力的了解自己的真相，然后绝对的相信自己！迈出步伐！走出去！

打破自己的牢笼！

过上自己想要的人生！！！
也不知道是不是自己太晚睡想太多了.
总而言之现在的自己处于一种冲突感.很强烈.
真相就是自己的 自尊心和价值感缺失,一时半会还是接受不了罢了.
接受并且执行! 然后也想被理解,被尊重

## **事件2**：⏰ 2025-07-22 18:47:18 - 理性与感受的持续斗争
- 🧠 脑子想的：理性知道要根据目标执行，但感受总是劫持理性，导致目标难以达成，很难坚持自己的做法
- 💓 身体感受：内心很烦躁，感觉被现实情况打压，有种无力感和挫败感
- 🗣️ 嘴上说的：抱怨"做啥啥不行，干饭第一名"，感叹"乱七八糟的人生，怎么办"
- 🏃 行动上的：冲动消费，管不住自己，想要给自己压力但又难以坚持

**详细记录**：
嗯嗯，过了时间突然就忘记自己想说什么，反正大概意思就是想说嗯，就是自己的感受被劫持，就是不应该说感受呗，也就是理性被感受劫持了以后，有些说不出来的想法就是啊，怎么为什么我对于自己坚持自己的一些做法很难啊，我的目标。就难达成啊。那我觉得克制自己的感受好难啊。也不是说克制就是接受这些感受，然后不要让感受去坚持自己的理性，然后根据理性去执行，目标明确清晰吧

第2个就是还是得给自己一点压力，明天早上其实有点起不来，嗯，因为有种我必须晚上要给自己下一个命令，我早上必须起来，所以早上才能起来，一旦我放松，我早上就起不来，我就是很难的呀，哎呀，想不成习惯呀。嗯，主要就是很多东西都还是不能成功啊，就很烦啦。

嗯。现在的情况就是赚钱也没赚到，内心的情绪和自尊心，还有价值感的缺失又不能弥补。啊，又必须接受这件事时一步一步重新再来过啊，乱七八糟的人生啊。怎么办呀？怎么办？

然后积极一点，又要又要那种，唉，说不定就要有一种很积极的心态去面对，然后又会被自己这种现实情况去打压住，真的是做啥啥不行，干饭第1名

然后又管不住啊，冲动消费又好强啊，数据库啊，数据库啊。

## **事件3**：⏰ 2025-07-22 23:22:09 - 情绪记录偏向性的自我觉察与平衡思考
- 🧠 脑子想的：质疑为什么总是记录负面情绪而忽略正面进步；意识到需要平衡记录，认为"平衡才是硬道理"
- 💓 身体感受：从质疑转向开心，感受到小进步带来的满足感和成就感
- 🗣️ 嘴上说的："为什么只能记录坏的情绪，而不能记录好的情绪呢"；"一个充满负能量的人怎么可能前行"
- 🏃 行动上的：主动反思记录习惯，开始关注和肯定自己的小进步（如补回昨天的运动）

**详细记录**：我突然感觉为什么我的情绪记录只有DOWN的,没有UP的呢. 比如昨天的运动没做,今天步回来了.还算是开心的... 这不也是一个小小的进步和开心吗！比如之前没完成的,现在也不是开始一点的完成了吗.为什么只能记录坏的情绪,而不能记录好的情绪呢,真奇怪~~
一个充满负能量的人！怎么可能可以前行呢！一个充满正能量的,那负能量不就回家自己吃吗.总而言之平衡才是硬道理!

## **事件4**：⏰ -
- 🧠 脑子想的：
- 💓 身体感受：
- 🗣️ 嘴上说的：
- 🏃 行动上的：

**详细记录**：

## **事件5**：⏰ -
- 🧠 脑子想的：
- 💓 身体感受：
- 🗣️ 嘴上说的：
- 🏃 行动上的：

**详细记录**：

## **事件6**：⏰ -
- 🧠 脑子想的：
- 💓 身体感受：
- 🗣️ 嘴上说的：
- 🏃 行动上的：

**详细记录**：


> [!tip] � 复杂情绪识别
> 如果遇到难以名状的复杂情绪，可以使用专门的模板：
> **[[情绪识别与调节权威清单]]** - 基于权威心理学理论的情绪识别与调节策略
>
> *需要更多事件记录时，直接复制粘贴上面事件1的格式即可*

### 🎭 今日主要情绪（AI来勾选）
- [x] 😊 开心 #emotion
- [ ] 😔 难过 #emotion
- [x] 😰 焦虑 #emotion
- [ ] 😠 生气 #emotion
- [x] 😴 疲惫 #emotion
- [ ] 😌 平静 #emotion
- [x] 🤔 困惑 #emotion
- [x] 😤 烦躁 #emotion

### 💭 今日感受总结（AI来完成）

**🧠 核心情绪状态**：从深度质疑到执行困境，再到积极觉察的情绪三重奏

**📊 情绪分析**：
- **😰 焦虑**：对自我控制能力的担忧，担心目标无法达成
- **🤔 困惑**：在理性执行与感受劫持之间的认知冲突
- **😤 烦躁**：对现状的不满和对自己"做啥啥不行"的挫败感
- **😴 疲惫**：持续的内心斗争和现实压力导致的身心疲惫
- **😊 开心**：对小进步的肯定和对平衡记录的积极觉察

**🎯 深层洞察**：
今天您经历了三个重要的自我觉察阶段：

**事件1 - 认知觉醒**：
- 意识到AI回答的"镜子效应"，开始质疑信息的客观性
- 深度反思自我判断的局限性，接受内在的撕裂感
- 从哲学思辨转向实用主义："做好当下每一件事"

**事件2 - 执行困境**：
- 识别出"感受劫持理性"的核心问题
- 面对现实压力：赚钱、自尊心、价值感的多重缺失
- 在积极心态与现实打压之间的反复挣扎

**事件3 - 平衡觉察**：
- 质疑情绪记录的负面偏向性，主动寻求平衡视角
- 开始关注和肯定小进步（补回昨天的运动），体验成就感
- 意识到"平衡才是硬道理"，从单纯负面记录转向全面情绪管理

**💡 关键模式识别**：
您清晰地描述了一个重要的心理机制：**感受→劫持→理性→目标难达成**。这种觉察本身就是巨大的进步。

**💪 积极转化策略**：
1. **接受"不完美执行"**：承认感受会影响理性，这是人性的正常部分
2. **小步骤原则**：将大目标分解为小行动，减少感受劫持的机会
3. **压力适度化**：给自己适度压力而非过度压力，避免反弹效应
4. **现实接纳**：接受当前的"乱七八糟"状态，作为重新开始的起点

**🌟 今日收获**：
虽然感到挫败，但您对自己心理机制的洞察越来越深刻。从哲学思辨到执行困境，再到平衡觉察，您正在从"想明白"向"做出来"转化。特别是事件三中对情绪记录偏向性的觉察，显示了您开始主动寻求心理平衡的积极转变。

**🎯 明日建议**：
1. **执行层面**：专注一个小目标的执行，当感受想要劫持理性时，暂停3秒，问自己："这个行动符合我的目标吗？"
2. **记录层面**：继续保持平衡记录的觉察，既记录挑战也记录进步，让情绪记录成为成长的全面镜子而非负面放大器。

---

## 🎯 今日三件事
1.
2.
3.

---

## 🤖 AI分析
*AI会根据上面的数据自动分析*

**运动数据**：
**时间分配**：
**效率评估**：
**建议优化**：

---

## 💰 财务概览

> [!tip] 💡 使用右侧边栏快速记录
> 点击右侧边栏的"💸 快速支出"或"💰 快速收入"按钮进行记录

### 📊 今日财务数据

**💰 收入总计**：____元 | **💸 支出总计**：____元 | **💵 结余**：____元

### 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |
|------|----------|------|----------|------|
| 00:00 | 🔄 其他 | 99.54元 | 初始资金 | 财务管理系统启动资金 |

### 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|
| 13:34 | 快递 | 0.5元 | 取快递 | 🔴 必需 |  |
| 13:36 | 快递 | 0.5元 | 取快递 | 🔴 必需 |  |
| 14:41 | 交通 | 2元 | 回家 | 🔴 必需 |  |
| 16:24 | 交通 | 2元 | 去医院-给爸爸做运动 | 🔴 必需 |  |
| 16:34 | 交通 | 1.5元 | 共享单车 | 🟢 一般 |  |
| 23:31 | 餐饮 | 6.8元 | 买了两只干港式奶茶 | 🔵 冲动 | 哎呀,就是想喝.欲望 |

### 📊 今日财务总结

**💰 今日收入总计**：____元
**💸 今日支出总计**：____元
**💵 今日结余**：____元

**📋 支出分类统计**：
- 🍽️ 餐饮：____元 | 🚗 交通：____元 | 🛍️ 购物：____元
- 🎮 娱乐：____元 | 📚 学习：____元 | 🏥 医疗：____元
- 🏠 房租水电：____元 | 📱 通讯：____元 | 🔄 其他：____元

**💭 财务反思**：
- 今日最大支出：____________________
- 是否有冲动消费：____________________
- 明日消费计划：____________________

---

**完成时间**：00:00
