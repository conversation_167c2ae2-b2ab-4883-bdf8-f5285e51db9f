# 🎯 八阶段AI协作提示词库

> [!important] 💡 核心理念
> **基于八阶段认知进化模型设计的完整AI协作提示词系统**
> **每个阶段都有：概念→形象描述→定义→要求→输出格式的完整结构**
> **目标：让AI通过精确执行达到深度思考效果**

---

## 🌋 **第一阶段：混沌探索模式**

### 🎯 **概念**
原水源头阶段，面对新信息时的最初反应和直觉感受

### 🌊 **形象描述**
就像山泉的源头，水刚从岩石缝隙中涌出，还很混沌，方向不明确，但有一种天然的流动冲动。这时的想法和感受都是原始的、未经加工的，充满了可能性。
嗯嗯
### 📋 **定义**
混沌探索模式是指在接收到新信息时，允许自己处于困惑和不确定状态，记录最原始的直觉反应和模糊方向感，不强求立即的逻辑性和完整性。

### ✅ **执行要求**
**允许的行为：**
- 表达困惑和不确定性
- 记录模糊的方向感和直觉
- 承认矛盾和不完整的想法
- 说出内心的真实感受
- 保持开放和好奇的态度

**不允许的行为：**
- 立即给出完美的答案
- 假装完全理解
- 压制直觉和感受
- 要求逻辑的完整性
- 急于下结论

### 📤 **输出格式**
```
【混沌探索反馈】
直觉感受：我对这个信息的第一感觉是___
方向感知：虽然还不清楚具体怎么做，但直觉告诉我方向可能是___
困惑点：我现在的困惑主要在___
内心声音：我内心有个声音在说___
能量状态：这个信息让我感到[兴奋/焦虑/好奇/抗拒]___
```。

---

## 🌀 **第二阶段：自我定位模式**

### 🎯 **概念**
混沌定位阶段，诚实地看清自己在混沌中的位置和倾向

### 🌊 **形象描述**
就像在迷雾中的旅行者，虽然看不清全貌，但要诚实地确认自己现在站在哪里，面向哪个方向，有什么倾向和偏见。这是自我觉察的关键时刻。

### 📋 **定义**
自我定位模式是指在混沌状态中，诚实地识别和承认自己的倾向性判断、可能的偏见，以及当前困惑的具体位置，为后续的理性分析奠定基础。

### ✅ **执行要求**
**允许的行为：**
- 承认自己的倾向性判断
- 识别可能存在的偏见
- 分析倾向产生的原因
- 定位困惑的具体方面
- 保持自我觉察的诚实

**不允许的行为：**
- 假装完全客观
- 隐藏自己的倾向
- 回避偏见的存在
- 模糊困惑的位置
- 自我欺骗

### 📤 **输出格式**
```
【自我定位反馈】
倾向判断：我倾向于认为是___
偏见识别：这种倾向可能是因为___，让我容易忽视___
困惑定位：我当前的混沌状态主要体现在___方面
自我觉察：我意识到自己在这个问题上的盲点可能是___
定位确认：我现在站在___的位置，面向___的方向
```

---

## 📊 **第三阶段：全面收集模式**

### 🎯 **概念**
绝对理性收集阶段，系统性地收集所有相关信息

### 🌊 **形象描述**
就像科学家做实验，必须收集所有的数据，包括那些不符合假设的数据。不能因为某些信息不利于自己的判断就选择性忽视，要像海绵一样吸收所有相关信息。

### 📋 **定义**
全面收集模式是指用绝对理性的态度，系统性地收集所有相关信息，特别是那些与初始判断相冲突的信息，并用三维坐标系统对信息进行精确定位。

### ✅ **执行要求**
**允许的行为：**
- 强制收集反对初始判断的信息
- 用三维坐标标记每条信息
- 寻找被忽视的反面证据
- 收集所有相关概念和观点
- 保持绝对的理性态度

**不允许的行为：**
- 选择性收集信息
- 忽视反面证据
- 跳过不利信息
- 只收集支持性信息
- 带有情绪色彩地筛选

### 📤 **输出格式**
```
【全面收集反馈】
支持信息：支持我初始判断的信息有___
反对信息：反对我初始判断的信息有___
中性信息：中性或模糊的信息有___
三维定位：
- 时间轴：[过去/现在/未来]___
- 信息轴：[外部/内部]___
- 注意力轴：[分散/聚焦/深度]___
信息完整度：我认为信息收集的完整度是___%
```

---

## 🔗 **第四阶段：多维串联模式**

### 🎯 **概念**
多角度连接阶段，尝试不同的信息串联方式

### 🌊 **形象描述**
就像拼图游戏，同样的拼图块可以尝试不同的组合方式。不要被第一种连接方式束缚，要像魔方一样，从不同角度旋转，寻找最佳的组合。

### 📋 **定义**
多维串联模式是指对收集到的信息进行多角度的连接尝试，至少探索3种不同的连接方式，避免被初始判断劫持，寻找真相而非证实偏见。

### ✅ **执行要求**
**允许的行为：**
- 尝试至少3种不同连接方式
- 探索与初始判断相冲突的连接
- 建立完整的逻辑链条
- 考虑"如果相反会怎样"
- 寻找意外的连接可能

**不允许的行为：**
- 只尝试一种连接方式
- 被第一印象劫持
- 忽视冲突的连接
- 逻辑链条不完整
- 回避意外的可能性

### 📤 **输出格式**
```
【多维串联反馈】
连接方式1：如果___，那么___，因为___
连接方式2：如果___，那么___，因为___
连接方式3：如果___，那么___，因为___
冲突连接：与初始判断冲突的连接是___
意外发现：最意外的连接可能是___
真相推测：基于多维连接，最可能的真相是___
```

---

## 📈 **第五阶段：反馈决策模式**

### 🎯 **概念**
稳妥决策阶段，基于多维分析进行理性选择

### 🌊 **形象描述**
就像船长在暴风雨中选择航线，要综合考虑风向、海流、礁石等各种因素，选择一条相对安全但不过于保守的路线。

### 📋 **定义**
反馈决策模式是指基于前面的多维分析，评估各种可能性和风险，选择相对稳妥但不保守的方案，并设计验证方法。

### ✅ **执行要求**
**允许的行为：**
- 评估每种方案的可能性和风险
- 选择平衡稳妥与进取的方案
- 说明详细的选择理由
- 设计具体的验证方法
- 准备执行和调整计划

**不允许的行为：**
- 选择过于保守的方案
- 忽视风险评估
- 缺乏选择理由
- 没有验证方法
- 不考虑执行可行性

### 📤 **输出格式**
```
【反馈决策反馈】
方案评估：
- 方案A：可能性___%，风险级别___，优势___，劣势___
- 方案B：可能性___%，风险级别___，优势___，劣势___
- 方案C：可能性___%，风险级别___，优势___，劣势___
推荐方案：我推荐方案___
选择理由：___
验证方法：___
执行计划：___
调整预案：如果出现___情况，则调整为___
```

---

## ⚡ **第六阶段：冲突处理模式**

### 🎯 **概念**
冲突升级阶段，处理新信息与现有结论的冲突

### 🌊 **形象描述**
就像地震时建筑物的结构检测，当新的冲击力出现时，要立即检查整个结构是否还稳固，哪些部分需要重新加固或重建。

### 📋 **定义**
冲突处理模式是指当新信息与现有结论产生冲突时，立即承认冲突的存在，分析冲突的根本原因，重新启动分析流程，寻找更高层次的整合方案。

### ✅ **执行要求**
**允许的行为：**
- 立即承认冲突的存在
- 分析冲突的根本原因
- 重新启动前5阶段分析
- 寻找整合冲突的方案
- 提升到更高认知层次

**不允许的行为：**
- 回避或淡化冲突
- 强行解释冲突
- 固守原有结论
- 简单否定新信息
- 停留在原有层次

### 📤 **输出格式**
```
【冲突处理反馈】
冲突识别：新信息___与现有结论___产生冲突
冲突严重程度：[轻微/中等/严重]___
冲突根本原因：___
影响范围：这个冲突影响到___
重新分析：需要重新分析的方面包括___
整合方案：可能的整合方案是___
认知升级：这个冲突让我意识到需要在___层次重新思考
启动流程：现在重新启动第___阶段分析
```

---

---

## 🌟 **第七阶段：系统化模式**

### 🎯 **概念**
指数发现阶段，让整个认知过程系统化和可重复

### 🌊 **形象描述**
就像工程师设计自动化生产线，把之前手工完成的复杂工序变成可以自动运行的系统。发现规律，提取模式，让智慧可以复制和传承。

### 📋 **定义**
系统化模式是指识别在前6个阶段中重复出现的模式和规律，提取可复用的方法和工具，设计让整个认知循环自动化的机制，实现从个案到通用的跃升。

### ✅ **执行要求**
**允许的行为：**
- 识别重复出现的模式和规律
- 提取可复用的方法和工具
- 设计自动化的处理机制
- 总结通用的原则和框架
- 考虑系统的可扩展性

**不允许的行为：**
- 忽视重复出现的模式
- 只关注个案不考虑通用性
- 设计过于复杂的系统
- 缺乏可操作性
- 不考虑实际应用场景

### 📤 **输出格式**
```
【系统化反馈】
发现的模式：在处理过程中，我发现了___的重复模式
核心规律：背后的核心规律是___
可复用方法：可以复用的方法包括___
自动化机制：可以设计___机制来自动化这个过程
通用框架：这个经验可以抽象为___框架
适用范围：这个系统可以应用到___领域
优化建议：系统可以通过___方式进一步优化
价值评估：这个系统化的价值在于___
```

---

## 🌌 **第八阶段：重生升维模式**

### 🎯 **概念**
归零重生阶段，在更高维度重新开始认知循环

### 🌊 **形象描述**
就像蛇蜕皮或蝴蝶破茧，虽然回到了起点，但已经是完全不同层次的存在。带着新的认知能力和视野，准备开始更高层次的探索之旅。

### 📋 **定义**
重生升维模式是指在完成一个完整的认知循环后，回到最初的起点，但保持更高的认知层次，识别根本性的改变，发现新的问题和可能性，为下一个更高维度的循环做准备。

### ✅ **执行要求**
**允许的行为：**
- 回到最初起点但保持更高认知
- 识别这个循环带来的根本改变
- 发现新的问题和可能性
- 为更高层次循环做准备
- 跨入新的认知维度

**不允许的行为：**
- 简单重复之前的过程
- 忽视认知层次的提升
- 停留在原有的问题层面
- 不准备新的探索
- 拒绝认知的跃升

### 📤 **输出格式**
```
【重生升维反馈】
回到起点：我现在回到了最初的起点，但现在我理解了___
根本改变：这个循环带来的根本性改变是___
认知跃升：我的认知从___层次跃升到了___层次
新的问题：现在我发现了新的问题：___
新的可能性：我看到了新的可能性：___
下一个起点：下一个更高层次循环的起点是___
升维准备：为了进入新维度，我需要准备___
探索方向：新的探索方向是___
循环预期：我预期下一个循环将会___
```

---

## 🎯 **使用指南**

### 📱 **单阶段激活方法**
```
"激活第[1-8]阶段：[模式名称]，处理信息：[具体信息内容]"

示例：
"激活第1阶段：混沌探索模式，处理信息：我想学习RAG技术但不知道从哪开始"
```

### 🔄 **完整循环激活方法**
```
"启动八阶段完整认知循环，起始信息：[具体信息内容]"

示例：
"启动八阶段完整认知循环，起始信息：如何平衡减肥和享受美食"
```

### ⚡ **阶段跳转方法**
```
"当前第[X]阶段，遇到[具体情况]，跳转到第[Y]阶段"

示例：
"当前第5阶段，遇到新信息冲突，跳转到第6阶段"
```

### 🔄 **循环重启方法**
```
"第6阶段冲突触发，重启前5阶段循环，新的起始信息：[更新信息]"
```

---

## 🌟 **系统特色**

### 💡 **设计优势**
- **完整性**：覆盖认知过程的所有关键阶段
- **精确性**：每个阶段都有明确的执行标准
- **可操作性**：提供具体的输出格式和要求
- **可重复性**：标准化的流程可以反复使用

### 🔄 **核心价值**
- **解决AI表面理解问题**：通过精确指令达到深度效果
- **建立人机协作标准**：提供可重复的协作模式
- **实现认知系统化**：把复杂思考变成可操作的流程
- **促进智慧传承**：让优秀的思维方式可以复制

### 🎯 **应用场景**
- **复杂问题分析**：需要深度思考的问题
- **决策制定过程**：重要决策的系统化分析
- **学习新知识**：知识孤岛的连接和整合
- **创新思维训练**：突破思维局限的系统方法

---

**文档版本**：v1.0（完整版）
**创建时间**：2025-07-21
**核心价值**：首个基于认知科学的AI协作提示词系统
**使用建议**：建议从单阶段练习开始，逐步掌握完整循环
