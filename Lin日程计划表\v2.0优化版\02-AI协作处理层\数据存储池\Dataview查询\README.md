# 📊 Dataview查询脚本

## 🎯 功能说明
这里存放各种数据查询和统计脚本，用于自动生成报告和分析。

## 📁 文件结构
```
Dataview查询/
├─ 财务统计查询.md    # 收入支出统计
├─ 健康数据查询.md    # 睡眠运动统计
├─ 时间分配查询.md    # 时间使用分析
├─ 情绪趋势查询.md    # 情绪变化趋势
├─ 周度汇总查询.md    # 每周数据汇总
├─ 月度报告查询.md    # 每月综合报告
└─ README.md
```

## 🚀 使用方法
1. **复制查询代码**：从对应文件复制Dataview查询
2. **粘贴到目标位置**：粘贴到日记或周记中
3. **自动生成结果**：Dataview自动执行查询显示结果
4. **定期更新**：数据会随着新记录自动更新

## 📋 查询示例
```dataview
TABLE 
  sum(财务.支出) as "总支出",
  avg(健康.睡眠时长) as "平均睡眠"
FROM "01-人工记录输入层/记录界面/日记"
WHERE date >= date(today) - dur(7 days)
```

## 🎯 预设查询类型
- **本周数据汇总**
- **本月趋势分析**
- **年度对比统计**
- **目标完成追踪**
