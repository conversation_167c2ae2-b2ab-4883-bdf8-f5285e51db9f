# 📊 本周数据汇总查询

## 🎯 使用方法
将下面的查询代码复制到周记中，可以自动汇总本周的数据。

## 📋 本周日记列表
```dataview
TABLE 
  file.name as "日期",
  choice(contains(string(tags), "日记"), "✅", "❌") as "状态"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
SORT date ASC
```

## 💰 本周财务统计
```dataview
TABLE 
  date as "日期",
  财务.收入 as "收入",
  财务.支出 as "支出"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
SORT date ASC
```

## 😴 本周睡眠统计
```dataview
TABLE 
  date as "日期",
  睡眠.睡眠时长 as "睡眠时长",
  choice(睡眠.睡眠时长 >= 7, "✅", "❌") as "充足"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
SORT date ASC
```

## 🚶 本周步数统计
```dataview
TABLE 
  date as "日期",
  步数.今日步数 as "步数",
  choice(步数.今日步数 >= 10000, "✅", "❌") as "达标"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
SORT date ASC
```

## 💪 本周运动统计
```dataview
TABLE 
  date as "日期",
  运动.运动类型 as "运动类型",
  运动.运动时长 as "时长",
  choice(运动.完成情况 = "完成", "✅", "❌") as "完成"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
SORT date ASC
```

## 😊 本周心情统计
```dataview
TABLE 
  date as "日期",
  心情.状态 as "心情",
  choice(心情.状态 = "好", "😊", choice(心情.状态 = "一般", "😐", "😔")) as "图标"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
SORT date ASC
```

## 📈 本周汇总统计
```dataview
TABLE WITHOUT ID
  "本周汇总" as "项目",
  sum(财务.收入) as "总收入",
  sum(财务.支出) as "总支出",
  round(avg(睡眠.睡眠时长), 1) as "平均睡眠",
  round(avg(步数.今日步数), 0) as "平均步数",
  sum(运动.运动时长) as "总运动时长"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
```

## 🎯 目标完成情况
```dataview
TABLE WITHOUT ID
  "目标项目" as "项目",
  "目标值" as "目标",
  "实际值" as "实际",
  "完成率" as "完成率"
FROM "01-人工记录输入层/记录界面/日记/2025/07-July"
WHERE date >= date(today) - dur(7 days) AND date <= date(today)
LIMIT 1
FLATTEN [
  ["运动次数", "5次", length(filter(运动.完成情况, (x) => x = "完成")) + "次", round(length(filter(运动.完成情况, (x) => x = "完成")) / 5 * 100, 0) + "%"],
  ["步数达标", "7天", length(filter(步数.今日步数, (x) => x >= 10000)) + "天", round(length(filter(步数.今日步数, (x) => x >= 10000)) / 7 * 100, 0) + "%"],
  ["睡眠充足", "7天", length(filter(睡眠.睡眠时长, (x) => x >= 7)) + "天", round(length(filter(睡眠.睡眠时长, (x) => x >= 7)) / 7 * 100, 0) + "%"]
] as item
```

---

## 📋 使用说明

### 复制到周记
1. 选择需要的查询代码
2. 复制到周记文件中
3. Dataview会自动执行并显示结果

### 自定义查询
- 修改日期范围：调整 `dur(7 days)` 
- 修改数据源：调整文件夹路径
- 添加新字段：根据日记模板添加

### 注意事项
- 确保日记中的数据格式正确
- 字段名称要与模板一致
- 日期格式要标准化

---
*查询会根据实际日记数据自动更新*
