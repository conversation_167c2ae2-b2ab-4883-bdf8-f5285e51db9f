# 📊 数据存储池

## 🎯 功能说明
这里存放从日记中提取和分类的各种数据，用于统计分析和趋势追踪。

## 📂 文件夹结构
```
数据存储池/
├─ 财务数据/          # 收入、支出、投资记录
├─ 时间数据/          # 工作、学习、运动时间分配
├─ 情绪数据/          # 心情、压力、满意度记录
├─ 健康数据/          # 睡眠、运动、步数记录
├─ 工作数据/          # 任务完成、学习内容记录
├─ 人际数据/          # 社交活动、关系质量记录
├─ Dataview查询/      # 数据查询和统计脚本
└─ README.md
```

## 🔄 数据流转过程
```
日记记录 → AI识别分类 → 数据存储池 → 统计分析 → 洞察报告
```

## 🚀 使用方法
1. **自动提取**：AI从日记中自动识别和提取数据
2. **分类存储**：按照类型存储到对应文件夹
3. **统计查询**：使用Dataview查询生成统计报告
4. **趋势分析**：长期数据用于模式识别

## 📋 数据格式
所有数据都采用结构化格式存储，便于查询和分析：
- **日期标准化**：YYYY-MM-DD格式
- **数值标准化**：统一单位和精度
- **分类标准化**：统一的标签和类别
