# 💰 财务仪表板 - 模块一：数据收集与汇总（干净版）

> [!dashboard] 📊 **实时财务状况监控**
> 基于严厉教练系统的动态财务管理仪表板 - 模块一专用版

## 🔄 **模块一：数据收集与汇总**

```dataviewjs
// ===== 模块一：数据收集与汇总 =====

// 1. 万能化分层数据查询函数
async function getFinancialData(dv) {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const monthName = currentDate.toLocaleString('en-US', { month: 'long' });
    const quarter = Math.ceil(currentDate.getMonth() / 3);
    const weekNumber = getWeekNumber(currentDate);
    
    console.log(`🔄 开始分层财务数据查询 - ${year}-${month}-${currentDate.getDate()}`);
    
    // 完整的分层数据源定义（从高层到低层）
    const dataSources = [
        {
            type: 'annual',
            path: `02-AI协作处理层/数据存储池/财务数据/年度财务汇总/${year}年度财务报告.md`,
            description: `${year}年度汇总数据`,
            priority: 1
        },
        {
            type: 'quarterly', 
            path: `02-AI协作处理层/数据存储池/财务数据/季度财务汇总/${year}Q${quarter}季度财务报告.md`,
            description: `${year}年第${quarter}季度汇总数据`,
            priority: 2
        },
        {
            type: 'monthly',
            path: `02-AI协作处理层/数据存储池/财务数据/月度财务汇总/${year}-${month}月度财务报告.md`,
            description: `${year}年${month}月度汇总数据`,
            priority: 3
        },
        {
            type: 'weekly',
            path: `02-AI协作处理层/周记系统/${year}-W${weekNumber}周报.md`,
            description: `${year}年第${weekNumber}周汇总数据`,
            priority: 4
        },
        {
            type: 'daily',
            path: `01-人工记录输入层/记录界面/日记/${year}/${month}-${monthName}/`,
            description: `${year}年${month}月日记原始数据`,
            priority: 5
        }
    ];
    
    // 分层查询状态报告
    let queryReport = {
        totalLayers: dataSources.length,
        checkedLayers: 0,
        successLayer: null,
        layerResults: []
    };
    
    // 依次尝试每一层数据源
    for (const source of dataSources) {
        queryReport.checkedLayers++;
        console.log(`\n📊 [第${source.priority}层] 查询: ${source.description}`);
        console.log(`📁 路径: ${source.path}`);
        
        try {
            let data = null;
            
            if (source.type === 'daily') {
                // 日记数据特殊处理：扫描整个目录
                data = await loadDailyData(dv, source.path, year, month, monthName);
            } else {
                // 其他数据源：尝试加载单个文件
                data = await loadDataFromSource(dv, source);
            }
            
            if (data && (data.records.length > 0 || (data.income && data.income.length > 0) || (data.expense && data.expense.length > 0))) {
                console.log(`✅ [第${source.priority}层] 成功获取数据!`);
                console.log(`📈 收入记录: ${data.income ? data.income.length : 0} 条`);
                console.log(`📉 支出记录: ${data.expense ? data.expense.length : 0} 条`);
                
                queryReport.successLayer = source.priority;
                queryReport.layerResults.push({
                    layer: source.priority,
                    type: source.type,
                    description: source.description,
                    status: 'success',
                    incomeCount: data.income ? data.income.length : 0,
                    expenseCount: data.expense ? data.expense.length : 0
                });
                
                return { 
                    data: data, 
                    source: source.type, 
                    queryReport: queryReport,
                    sourceInfo: source
                };
            } else {
                console.log(`⏭️ [第${source.priority}层] 暂时没有数据，继续下一层...`);
                queryReport.layerResults.push({
                    layer: source.priority,
                    type: source.type,
                    description: source.description,
                    status: 'no_data',
                    reason: '文件存在但无财务记录'
                });
            }
            
        } catch (error) {
            console.log(`❌ [第${source.priority}层] 查询失败: ${error.message}`);
            queryReport.layerResults.push({
                layer: source.priority,
                type: source.type,
                description: source.description,
                status: 'error',
                reason: error.message
            });
            continue;
        }
    }
    
    console.log('\n⚠️ 所有数据层都无法获取有效数据');
    return { 
        data: { records: [], income: [], expense: [] }, 
        source: 'none',
        queryReport: queryReport
    };
}

// 获取周数的辅助函数
function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// 2. 日记数据加载函数
async function loadDailyData(dv, basePath, year, month, monthName) {
    try {
        // 使用Dataview查询日记文件
        const diaryFiles = dv.pages(`"01-人工记录输入层/记录界面/日记"`)
            .where(p => p.file.name.match(/^\d{4}-\d{2}-\d{2}$/))
            .where(p => p.file.name.startsWith(`${year}-${month}`))
            .sort(p => p.file.name, 'desc');
        
        console.log(`找到 ${diaryFiles.length} 个日记文件`);
        
        let allRecords = [];
        let totalIncome = [];
        let totalExpense = [];
        let processedFiles = [];
        let skippedFiles = [];
        
        // 遍历每个日记文件
        for (const file of diaryFiles) {
            try {
                const content = await dv.io.load(file.file.path);
                if (content) {
                    const records = extractFinancialRecords(content, file.file.name);
                    if (records.income.length > 0 || records.expense.length > 0) {
                        allRecords.push({
                            date: file.file.name,
                            income: records.income,
                            expense: records.expense
                        });
                        totalIncome.push(...records.income);
                        totalExpense.push(...records.expense);
                        processedFiles.push(file.file.name);
                        console.log(`✅ ${file.file.name}: 收入${records.income.length}条, 支出${records.expense.length}条`);
                    } else {
                        skippedFiles.push(file.file.name);
                        console.log(`⏭️ ${file.file.name}: 无财务记录`);
                    }
                }
            } catch (fileError) {
                console.log(`跳过文件 ${file.file.name}: ${fileError.message}`);
            }
        }
        
        return {
            records: allRecords,
            income: totalIncome,
            expense: totalExpense,
            summary: {
                totalFiles: diaryFiles.length,
                processedFiles: processedFiles.length,
                skippedFiles: skippedFiles.length,
                recordFiles: allRecords.length,
                totalIncome: totalIncome.reduce((sum, item) => sum + item.amount, 0),
                totalExpense: totalExpense.reduce((sum, item) => sum + item.amount, 0),
                processedFilesList: processedFiles,
                skippedFilesList: skippedFiles
            }
        };
        
    } catch (error) {
        console.log(`日记数据加载失败: ${error.message}`);
        return null;
    }
}

// 3. 其他数据源加载函数
async function loadDataFromSource(dv, source) {
    try {
        const file = dv.page(source.path);
        if (!file) {
            return null;
        }
        
        const content = await dv.io.load(source.path);
        if (!content) {
            return null;
        }
        
        const records = extractFinancialRecords(content, source.path);
        return {
            records: [records],
            income: records.income,
            expense: records.expense,
            source: source.type
        };
        
    } catch (error) {
        console.log(`数据源加载失败 ${source.path}: ${error.message}`);
        return null;
    }
}

// 4. 财务记录提取函数
function extractFinancialRecords(content, fileName) {
    const records = {
        income: [],
        expense: []
    };
    
    try {
        // 提取收入记录 - 适配实际格式
        const incomeSection = content.match(/### 📈 收入记录[\s\S]*?(?=###|$)/);
        if (incomeSection) {
            console.log(`找到收入记录部分，长度: ${incomeSection[0].length}`);
            const incomeMatches = incomeSection[0].match(/\|\s*\d{1,2}:\d{2}\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]*)\s*\|/g);
            if (incomeMatches) {
                console.log(`找到 ${incomeMatches.length} 条收入记录`);
                incomeMatches.forEach((match, index) => {
                    const parts = match.split('|').map(p => p.trim());
                    console.log(`收入记录 ${index + 1}: ${JSON.stringify(parts)}`);
                    
                    const timeStr = parts[1] || '';
                    const typeStr = parts[2] || '';
                    const amountStr = parts[3] || '';
                    const sourceStr = parts[4] || '';
                    const noteStr = parts[5] || '';
                    
                    const amountMatch = amountStr.match(/(\d+(?:\.\d+)?)/);
                    const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
                    
                    if (amount > 0) {
                        records.income.push({
                            date: fileName,
                            time: timeStr,
                            type: typeStr,
                            amount: amount,
                            source: sourceStr,
                            note: noteStr
                        });
                    }
                });
            } else {
                console.log('未找到匹配的收入记录格式');
            }
        } else {
            console.log('未找到收入记录部分');
        }
        
        // 提取支出记录 - 适配实际格式
        const expenseSection = content.match(/### 📉 支出记录[\s\S]*?(?=###|$)/);
        if (expenseSection) {
            console.log(`找到支出记录部分，长度: ${expenseSection[0].length}`);
            const expenseMatches = expenseSection[0].match(/\|\s*\d{1,2}:\d{2}\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]+)\s*\|\s*([^|]*)\s*\|/g);
            if (expenseMatches) {
                console.log(`找到 ${expenseMatches.length} 条支出记录`);
                expenseMatches.forEach((match, index) => {
                    const parts = match.split('|').map(p => p.trim());
                    console.log(`支出记录 ${index + 1}: ${JSON.stringify(parts)}`);
                    
                    const timeStr = parts[1] || '';
                    const typeStr = parts[2] || '';
                    const amountStr = parts[3] || '';
                    const itemStr = parts[4] || '';
                    const necessityStr = parts[5] || '';
                    const noteStr = parts[6] || '';
                    
                    const amountMatch = amountStr.match(/(\d+(?:\.\d+)?)/);
                    const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
                    
                    if (amount > 0) {
                        records.expense.push({
                            date: fileName,
                            time: timeStr,
                            type: typeStr,
                            amount: amount,
                            item: itemStr,
                            necessity: necessityStr,
                            note: noteStr
                        });
                    }
                });
            } else {
                console.log('未找到匹配的支出记录格式');
            }
        } else {
            console.log('未找到支出记录部分');
        }
        
    } catch (error) {
        console.log(`记录提取失败 ${fileName}: ${error.message}`);
    }
    
    return records;
}

// ===== 执行数据收集 =====
console.log('🔄 开始执行模块一：数据收集与汇总');

// 执行数据查询
const financialData = await getFinancialData(dv);

// 显示分层查询报告
dv.header(2, "🔍 分层数据查询报告");

if (financialData.queryReport) {
    const report = financialData.queryReport;
    
    // 查询概况表格
    const queryOverviewTable = [
        ["📊 查询层级", `${report.checkedLayers}/${report.totalLayers} 层`],
        ["🎯 成功层级", report.successLayer ? `第${report.successLayer}层` : '无']
    ];
    dv.table(["项目", "结果"], queryOverviewTable);
    
    // 各层查询详情表格
    const layerTable = report.layerResults.map(layer => [
        `第${layer.layer}层`,
        layer.type,
        layer.description,
        layer.status === 'success' ? '✅ 成功' : 
        layer.status === 'no_data' ? '⏭️ 无数据' : '❌ 失败',
        layer.status === 'success' ? `收入${layer.incomeCount}条, 支出${layer.expenseCount}条` : 
        layer.reason || ''
    ]);
    dv.table(["层级", "类型", "描述", "状态", "详情"], layerTable);
}

// 显示最终数据结果
dv.header(2, "📊 最终数据结果");

if (financialData.data.records.length > 0 || (financialData.data.income && financialData.data.income.length > 0)) {
    if (financialData.data.summary) {
        const summary = financialData.data.summary;
        
        // 基础信息表格
        const basicInfoTable = [
            ["✅ 成功数据源", financialData.sourceInfo ? financialData.sourceInfo.description : financialData.source],
            ["📈 收入记录", `${financialData.data.income ? financialData.data.income.length : 0} 条`],
            ["📉 支出记录", `${financialData.data.expense ? financialData.data.expense.length : 0} 条`]
        ];
        dv.table(["项目", "数值"], basicInfoTable);
        
        // 文件统计表格
        const fileStatsTable = [
            ["📁 扫描文件", `${summary.totalFiles} 个`],
            ["✅ 处理文件", `${summary.processedFiles} 个`],
            ["⏭️ 跳过文件", `${summary.skippedFiles} 个`],
            ["📝 有效记录", `${summary.recordFiles} 个`]
        ];
        dv.table(["文件统计", "数量"], fileStatsTable);
        
        // ===== 先计算每日余额数据 =====
        // 计算每日余额变化
        function calculateDailyBalance() {
            const dailyTransactions = {};

            // 收集所有收入记录
            if (financialData.data.income) {
                financialData.data.income.forEach(income => {
                    const date = income.date || '未知日期';
                    if (!dailyTransactions[date]) {
                        dailyTransactions[date] = { income: 0, expense: 0 };
                    }
                    dailyTransactions[date].income += parseFloat(income.amount) || 0;
                });
            }

            // 收集所有支出记录
            if (financialData.data.expense) {
                financialData.data.expense.forEach(expense => {
                    const date = expense.date || '未知日期';
                    if (!dailyTransactions[date]) {
                        dailyTransactions[date] = { income: 0, expense: 0 };
                    }
                    dailyTransactions[date].expense += parseFloat(expense.amount) || 0;
                });
            }

            // 按日期排序并计算累计余额
            const sortedDates = Object.keys(dailyTransactions).sort();
            let cumulativeBalance = 0;
            const dailyBalanceData = [];

            sortedDates.forEach(date => {
                const dayData = dailyTransactions[date];
                const dayIncome = dayData.income;
                const dayExpense = dayData.expense;
                const dayNet = dayIncome - dayExpense;
                cumulativeBalance += dayNet;

                dailyBalanceData.push({
                    date: date,
                    income: dayIncome,
                    expense: dayExpense,
                    dayNet: dayNet,
                    cumulativeBalance: cumulativeBalance
                });
            });

            return dailyBalanceData;
        }

        const dailyBalance = calculateDailyBalance();

        // ===== 1. 一目了然的总览表 =====
        dv.header(3, "📊 财务总览（一眼看懂）");

        const totalDays = dailyBalance.length || 1;
        const avgDailyExpense = summary.totalExpense / totalDays;
        const netIncome = summary.totalIncome - summary.totalExpense;

        // 状态判断
        let statusText = "够用";
        if (netIncome < 0) statusText = "超支⚠️";
        else if (netIncome < summary.totalExpense * 0.1) statusText = "紧张";
        else if (netIncome > summary.totalExpense * 0.3) statusText = "宽裕👍";

        const overviewTable = [
            ["💰 总收入", `${summary.totalIncome.toFixed(2)}元`, `${financialData.data.income.length}笔收入`],
            ["💸 总支出", `${summary.totalExpense.toFixed(2)}元`, `${financialData.data.expense.length}笔支出`],
            ["💵 净剩余", `${netIncome.toFixed(2)}元`, statusText],
            ["📅 统计天数", `${totalDays}天`, totalDays >= 7 ? "数据充足" : "数据较少"],
            ["💳 日均支出", `${avgDailyExpense.toFixed(0)}元`, avgDailyExpense > 100 ? "偏高" : "还行"]
        ];
        dv.table(["项目", "金额", "说明"], overviewTable);

        // ===== 2. 每日余额追踪表格 =====
        dv.header(3, "📅 每日余额追踪");

        if (dailyBalance.length > 0) {
            // 显示每日余额表格
            const balanceTable = dailyBalance.map(day => [
                day.date,
                `+${day.income.toFixed(2)}`,
                `-${day.expense.toFixed(2)}`,
                `${day.dayNet >= 0 ? '+' : ''}${day.dayNet.toFixed(2)}`,
                `${day.cumulativeBalance >= 0 ? '+' : ''}${day.cumulativeBalance.toFixed(2)}`
            ]);

            dv.table(["📅 日期", "💰 收入", "💸 支出", "📊 当日净额", "💵 累计余额"], balanceTable);

            // 显示余额变化摘要
            const firstBalance = dailyBalance[0].cumulativeBalance - dailyBalance[0].dayNet;
            const lastBalance = dailyBalance[dailyBalance.length - 1].cumulativeBalance;
            const totalChange = lastBalance - firstBalance;

            dv.header(4, "📈 余额变化摘要");
            const balanceSummaryTable = [
                ["📅 统计天数", `${dailyBalance.length} 天`],
                ["🏁 期初余额", `${firstBalance.toFixed(2)} 元`],
                ["🏆 期末余额", `${lastBalance.toFixed(2)} 元`],
                ["📊 总变化", `${totalChange >= 0 ? '+' : ''}${totalChange.toFixed(2)} 元`],
                ["📉 最低余额", `${Math.min(...dailyBalance.map(d => d.cumulativeBalance)).toFixed(2)} 元`],
                ["📈 最高余额", `${Math.max(...dailyBalance.map(d => d.cumulativeBalance)).toFixed(2)} 元`]
            ];
            dv.table(["指标", "数值"], balanceSummaryTable);

            // 检查余额异常
            const negativeBalanceDays = dailyBalance.filter(day => day.cumulativeBalance < 0);
            if (negativeBalanceDays.length > 0) {
                dv.header(4, "⚠️ 负余额提醒");
                dv.paragraph(`**发现 ${negativeBalanceDays.length} 天出现负余额，请检查账目：**`);
                const negativeTable = negativeBalanceDays.map(day => [
                    day.date,
                    `${day.cumulativeBalance.toFixed(2)} 元`
                ]);
                dv.table(["日期", "负余额"], negativeTable);
            }
        } else {
            dv.paragraph("📝 暂无日期数据，无法计算每日余额");
        }

        // ===== 3. 花钱排行榜（知道钱都花哪了） =====
        dv.header(3, "🏆 支出排行榜");

        const categoryStats = {};
        financialData.data.expense.forEach(expense => {
            const category = expense.type || '其他';
            if (!categoryStats[category]) {
                categoryStats[category] = { total: 0, count: 0 };
            }
            categoryStats[category].total += parseFloat(expense.amount) || 0;
            categoryStats[category].count += 1;
        });

        if (Object.keys(categoryStats).length > 0) {
            const sortedCategories = Object.entries(categoryStats)
                .sort(([,a], [,b]) => b.total - a.total)
                .slice(0, 5); // 只显示前5名

            const rankingTable = sortedCategories.map(([category, stats], index) => {
                const percentage = ((stats.total / summary.totalExpense) * 100).toFixed(1);
                let rankIcon = "";
                if (index === 0) rankIcon = "🥇";
                else if (index === 1) rankIcon = "🥈";
                else if (index === 2) rankIcon = "🥉";
                else rankIcon = ` ${index + 1}`;

                return [
                    rankIcon,
                    category,
                    `${stats.total.toFixed(2)}元`,
                    `${percentage}%`
                ];
            });

            dv.table(["排名", "类别", "金额", "占比"], rankingTable);

            // 简单提醒
            if (sortedCategories.length > 0) {
                const topCategory = sortedCategories[0];
                const topPercentage = ((topCategory[1].total / summary.totalExpense) * 100).toFixed(1);
                if (topPercentage > 40) {
                    dv.paragraph(`💡 **提醒**：${topCategory[0]}占比${topPercentage}%，支出较集中`);
                }
            }
        } else {
            dv.paragraph("📝 暂无支出分类数据");
        }

        // ===== 4. 简单的数据质量检查 =====
        dv.header(3, "✅ 数据检查");

        // 检查数据质量
        const totalFiles = summary.totalFiles || 0;
        const processedFiles = summary.processedFiles || 0;
        const incomeRecords = financialData.data.income.length;
        const expenseRecords = financialData.data.expense.length;

        // 检查缺失数据
        const missingDates = financialData.data.expense.filter(exp => !exp.date || exp.date === '未知日期').length;
        const missingAmounts = financialData.data.expense.filter(exp => !exp.amount || parseFloat(exp.amount) === 0).length;

        // 检查异常金额（超过1000元的支出）
        const largeExpenses = financialData.data.expense.filter(exp => parseFloat(exp.amount) > 1000).length;

        const qualityTable = [
            ["处理文件", `${processedFiles}个`, processedFiles > 0 ? "✅ 正常" : "⚠️ 无数据"],
            ["收入记录", `${incomeRecords}笔`, incomeRecords > 0 ? "✅ 正常" : "⚠️ 无收入"],
            ["支出记录", `${expenseRecords}笔`, expenseRecords > 0 ? "✅ 正常" : "⚠️ 无支出"],
            ["缺失日期", `${missingDates}个`, missingDates === 0 ? "✅ 完整" : "⚠️ 注意"],
            ["异常金额", `${largeExpenses}个`, largeExpenses === 0 ? "✅ 正常" : "⚠️ 检查"]
        ];

        dv.table(["检查项目", "结果", "状态"], qualityTable);

        // 数据质量评级
        let qualityScore = 100;
        if (processedFiles === 0) qualityScore -= 30;
        if (incomeRecords === 0) qualityScore -= 20;
        if (expenseRecords === 0) qualityScore -= 30;
        if (missingDates > 0) qualityScore -= 10;
        if (largeExpenses > 0) qualityScore -= 10;

        let qualityGrade = "优秀";
        if (qualityScore < 60) qualityGrade = "需改进";
        else if (qualityScore < 80) qualityGrade = "良好";

        dv.paragraph(`📊 **数据质量评级**：${qualityGrade} (${qualityScore}分)`);

        // 文件详情表格
        if (summary.processedFilesList.length > 0 || (summary.skippedFilesList && summary.skippedFilesList.length > 0)) {
            const fileDetailsTable = [];
            
            // 处理文件行
            if (summary.processedFilesList.length > 0) {
                const processedLinks = summary.processedFilesList.map(fileName => 
                    `[[01-人工记录输入层/记录界面/日记/2025/07-July/${fileName}|${fileName}]]`
                ).join(', ');
                fileDetailsTable.push(["📋 处理文件", processedLinks]);
            }
            
            // 跳过文件行 - 显示完整列表
            if (summary.skippedFilesList && summary.skippedFilesList.length > 0) {
                const skippedLinks = summary.skippedFilesList.map(fileName => 
                    `[[01-人工记录输入层/记录界面/日记/2025/07-July/${fileName}|${fileName}]]`
                ).join(', ');
                fileDetailsTable.push(["⏭️ 跳过文件", skippedLinks]);
            }
            
            if (fileDetailsTable.length > 0) {
                dv.table(["文件类型", "文件列表（双链）"], fileDetailsTable);
            }
        }
    }
    
    // 显示最近的几条记录作为验证（带双链）- 修复排序
    if (financialData.data.expense && financialData.data.expense.length > 0) {
        dv.header(3, "📉 最近支出记录（验证 + 双链）");
        
        // 按日期和时间排序，确保最新的在最后
        const sortedExpenses = financialData.data.expense.sort((a, b) => {
            const dateCompare = a.date.localeCompare(b.date);
            if (dateCompare !== 0) return dateCompare;
            return a.time.localeCompare(b.time);
        });
        
        const recentExpenses = sortedExpenses.slice(-5);
        const expenseTable = recentExpenses.map(exp => {
            const dateLink = `[[01-人工记录输入层/记录界面/日记/2025/07-July/${exp.date}|${exp.date}]]`;
            return [
                dateLink, 
                exp.time, 
                exp.type, 
                `${exp.amount}元`, 
                exp.item,
                exp.necessity || ''
            ];
        });
        dv.table(["📉 最近支出", "时间", "类型", "金额", "项目"], expenseTable.map(row => [row[0], row[1], row[2], row[3], row[4]]));
    }
    
    // 显示收入记录（如果有）- 修复排序
    if (financialData.data.income && financialData.data.income.length > 0) {
        dv.header(3, "📈 收入记录（验证 + 双链）");
        
        // 按日期和时间排序，确保最新的在最后
        const sortedIncome = financialData.data.income.sort((a, b) => {
            const dateCompare = a.date.localeCompare(b.date);
            if (dateCompare !== 0) return dateCompare;
            return a.time.localeCompare(b.time);
        });
        
        const recentIncome = sortedIncome.slice(-5);
        const incomeTable = recentIncome.map(inc => {
            const dateLink = `[[01-人工记录输入层/记录界面/日记/2025/07-July/${inc.date}|${inc.date}]]`;
            return [
                dateLink,
                inc.time,
                inc.type,
                `${inc.amount}元`,
                inc.source || '',
                inc.note || ''
            ];
        });
        dv.table(["📈 最近收入", "类型", "金额", "来源"], incomeTable.map(row => [row[0], row[2], row[3], row[4]]));
    }
    
} else {
    dv.paragraph("⚠️ **未找到财务数据**");
    dv.paragraph("请检查日记文件中是否包含收入和支出记录表格");
}

console.log('✅ 模块一：数据收集与汇总 - 执行完成');

// ===== 数据输出：将financialData保存到全局对象 =====
if (financialData && (financialData.data.expense.length > 0 || financialData.data.income.length > 0)) {
    try {
        // 计算每日余额数据（用于全局变量）
        function calculateDailyBalanceForGlobal() {
            const dailyTransactions = {};

            // 收集所有收入记录
            if (financialData.data.income) {
                financialData.data.income.forEach(income => {
                    const date = income.date || '未知日期';
                    if (!dailyTransactions[date]) {
                        dailyTransactions[date] = { income: 0, expense: 0 };
                    }
                    dailyTransactions[date].income += parseFloat(income.amount) || 0;
                });
            }

            // 收集所有支出记录
            if (financialData.data.expense) {
                financialData.data.expense.forEach(expense => {
                    const date = expense.date || '未知日期';
                    if (!dailyTransactions[date]) {
                        dailyTransactions[date] = { income: 0, expense: 0 };
                    }
                    dailyTransactions[date].expense += parseFloat(expense.amount) || 0;
                });
            }

            // 按日期排序并计算累计余额
            const sortedDates = Object.keys(dailyTransactions).sort();
            let cumulativeBalance = 0;
            const dailyBalanceData = [];

            sortedDates.forEach(date => {
                const dayData = dailyTransactions[date];
                const dayIncome = dayData.income;
                const dayExpense = dayData.expense;
                const dayNet = dayIncome - dayExpense;
                cumulativeBalance += dayNet;

                dailyBalanceData.push({
                    date: date,
                    income: Math.round(dayIncome * 100) / 100,
                    expense: Math.round(dayExpense * 100) / 100,
                    dayNet: Math.round(dayNet * 100) / 100,
                    cumulativeBalance: Math.round(cumulativeBalance * 100) / 100
                });
            });

            return dailyBalanceData;
        }

        const dailyBalanceData = calculateDailyBalanceForGlobal();

        // 创建数据输出对象
        const dataOutput = {
            timestamp: new Date().toISOString(),
            source: financialData.source,
            summary: financialData.data.summary,
            income: financialData.data.income,
            expense: financialData.data.expense,
            queryReport: financialData.queryReport,
            dailyBalance: dailyBalanceData  // 新增：每日余额数据
        };
        
        // 保存到全局window对象，供模块二使用
        // 使用中文命名，明确数据内容和结构
        window.财务数据全局对象 = dataOutput;

        // 同时保持英文命名兼容性
        window.financialDataGlobal = dataOutput;

        // 【第一阶段】创建标准化的多系统数据管理器
        window.多系统数据管理器 = {
            // 数据存储中心
            数据存储: {},

            // 保存系统数据的标准接口
            保存数据(系统名称, 数据, 元数据 = {}) {
                this.数据存储[系统名称] = {
                    数据内容: 数据,
                    保存时间: new Date().toISOString(),
                    数据数量: this.计算数据量(数据),
                    数据来源: 元数据.来源 || '未知',
                    数据类型: 元数据.类型 || '混合',
                    ...元数据
                };
                console.log(`✅ ${系统名称}数据已保存 - ${this.数据存储[系统名称].数据数量}条记录`);
                return this.数据存储[系统名称];
            },

            // 获取系统数据的标准接口
            获取数据(系统名称) {
                const 存储数据 = this.数据存储[系统名称];
                if (!存储数据) {
                    console.warn(`⚠️ ${系统名称}数据不存在，请先运行该系统的模块一`);
                    return null;
                }
                return 存储数据.数据内容;
            },

            // 获取多系统数据（为综合分析准备）
            获取多系统数据(系统名称列表) {
                const 合并数据 = {};
                const 合并元数据 = {};

                系统名称列表.forEach(系统名称 => {
                    const 存储项 = this.数据存储[系统名称];
                    if (存储项) {
                        合并数据[系统名称] = 存储项.数据内容;
                        合并元数据[系统名称] = {
                            保存时间: 存储项.保存时间,
                            数据数量: 存储项.数据数量,
                            数据来源: 存储项.数据来源
                        };
                    }
                });

                return {
                    数据: 合并数据,
                    元数据: 合并元数据,
                    合并时间: new Date().toISOString(),
                    涉及系统: 系统名称列表
                };
            },

            // 系统状态检查
            检查系统状态() {
                const 状态报告 = {
                    已激活系统: Object.keys(this.数据存储),
                    系统总数: Object.keys(this.数据存储).length,
                    总记录数: 0,
                    系统详情: {}
                };

                Object.keys(this.数据存储).forEach(系统名称 => {
                    const 存储项 = this.数据存储[系统名称];
                    状态报告.系统详情[系统名称] = {
                        保存时间: 存储项.保存时间,
                        数据数量: 存储项.数据数量,
                        数据来源: 存储项.数据来源,
                        数据类型: 存储项.数据类型
                    };
                    状态报告.总记录数 += 存储项.数据数量;
                });

                return 状态报告;
            },

            // 计算数据量的通用方法
            计算数据量(数据) {
                if (!数据) return 0;
                if (Array.isArray(数据)) return 数据.length;
                if (typeof 数据 === 'object') {
                    let 总数 = 0;
                    Object.values(数据).forEach(值 => {
                        if (Array.isArray(值)) 总数 += 值.length;
                    });
                    return 总数;
                }
                return 1;
            }
        };

        // 📋 【重要】按照新架构设计：模块一不再直接输出到生态系统
        // 生态系统输出将在模块四完成后统一进行
        // 这里只保存到财务系统内部的全局变量

        console.log('✅ 财务数据已收集并存储到内部全局变量');
        console.log('📋 注意：生态系统输出将在模块四完成后统一进行');

        // 【兼容性】保持原有的财务数据管理器
        window.财务数据管理器 = {
            // 快速访问方法
            获取收入数据: () => window.财务数据全局对象.income,
            获取支出数据: () => window.财务数据全局对象.expense,
            获取汇总数据: () => window.财务数据全局对象.summary,
            获取查询报告: () => window.财务数据全局对象.queryReport,
            获取每日余额: () => window.财务数据全局对象.dailyBalance,  // 新增

            // 数据统计方法
            收入总金额: () => window.财务数据全局对象.summary.totalIncome,
            支出总金额: () => window.财务数据全局对象.summary.totalExpense,
            净收支: () => window.财务数据全局对象.summary.totalIncome - window.财务数据全局对象.summary.totalExpense,

            // 数据筛选方法
            按类型筛选支出: (类型) => window.财务数据全局对象.expense.filter(item => item.type === 类型),
            按日期筛选支出: (日期) => window.财务数据全局对象.expense.filter(item => item.date === 日期),
            按必要性筛选支出: (必要性) => window.财务数据全局对象.expense.filter(item => item.necessity === 必要性),

            // 新增：余额相关方法
            获取指定日期余额: (日期) => {
                const dailyBalance = window.财务数据全局对象.dailyBalance;
                if (!dailyBalance) return null;
                const dayData = dailyBalance.find(day => day.date === 日期);
                return dayData ? dayData.cumulativeBalance : null;
            },

            获取最终余额: () => {
                const dailyBalance = window.财务数据全局对象.dailyBalance;
                if (!dailyBalance || dailyBalance.length === 0) return 0;
                return dailyBalance[dailyBalance.length - 1].cumulativeBalance;
            },

            获取负余额日期: () => {
                const dailyBalance = window.财务数据全局对象.dailyBalance;
                if (!dailyBalance) return [];
                return dailyBalance.filter(day => day.cumulativeBalance < 0);
            },

            // 数据验证方法
            数据是否有效: () => window.财务数据全局对象 &&
                           (window.财务数据全局对象.income.length > 0 ||
                            window.财务数据全局对象.expense.length > 0),

            // 数据摘要方法
            获取数据摘要: () => ({
                数据源: window.财务数据全局对象.source,
                时间戳: window.财务数据全局对象.timestamp,
                收入记录数: window.财务数据全局对象.income.length,
                支出记录数: window.财务数据全局对象.expense.length,
                总收入: window.财务数据全局对象.summary.totalIncome,
                总支出: window.财务数据全局对象.summary.totalExpense,
                净收支: window.财务数据全局对象.summary.totalIncome - window.财务数据全局对象.summary.totalExpense,
                每日余额天数: window.财务数据全局对象.dailyBalance ? window.财务数据全局对象.dailyBalance.length : 0,
                最终余额: window.财务数据全局对象.dailyBalance && window.财务数据全局对象.dailyBalance.length > 0 ?
                         window.财务数据全局对象.dailyBalance[window.财务数据全局对象.dailyBalance.length - 1].cumulativeBalance : 0
            })
        };

        // 创建数组索引说明对象
        window.财务数据数组说明 = {
            收入记录数组: {
                变量名: 'window.财务数据全局对象.income',
                数组长度: dataOutput.income.length,
                数据说明: '每个元素包含：日期、时间、类型、金额、来源、备注',
                索引示例: dataOutput.income.length > 0 ? {
                    '第0个元素': dataOutput.income[0],
                    '数组总长度': dataOutput.income.length
                } : '暂无数据'
            },
            支出记录数组: {
                变量名: 'window.财务数据全局对象.expense',
                数组长度: dataOutput.expense.length,
                数据说明: '每个元素包含：日期、时间、类型、金额、项目、必要性、备注',
                索引示例: dataOutput.expense.length > 0 ? {
                    '第0个元素': dataOutput.expense[0],
                    '数组总长度': dataOutput.expense.length
                } : '暂无数据'
            },
            每日余额数组: {
                变量名: 'window.财务数据全局对象.dailyBalance',
                数组长度: dataOutput.dailyBalance.length,
                数据说明: '每个元素包含：日期、当日收入、当日支出、当日净额、累计余额',
                索引示例: dataOutput.dailyBalance.length > 0 ? {
                    '第0个元素': dataOutput.dailyBalance[0],
                    '数组总长度': dataOutput.dailyBalance.length,
                    '最终余额': dataOutput.dailyBalance[dataOutput.dailyBalance.length - 1].cumulativeBalance
                } : '暂无数据'
            },
            汇总统计对象: {
                变量名: 'window.财务数据全局对象.summary',
                数据说明: '包含文件统计、金额汇总、文件列表等信息'
            },
            查询报告对象: {
                变量名: 'window.财务数据全局对象.queryReport',
                数据说明: '包含分层查询的详细报告和状态信息'
            }
        };
        
        console.log('✅ 财务数据已输出到全局对象');
        dv.paragraph(`📤 **数据输出完成** - 数据已保存到全局对象，可供模块二使用`);

        // 显示数据摘要
        const summaryTable = [
            ["📊 数据源", dataOutput.source],
            ["📈 收入记录", `${dataOutput.income.length} 条`],
            ["📉 支出记录", `${dataOutput.expense.length} 条`],
            ["💰 总收入", `${dataOutput.summary.totalIncome.toFixed(2)} 元`],
            ["💸 总支出", `${dataOutput.summary.totalExpense.toFixed(2)} 元`],
            ["💵 净收支", `${(dataOutput.summary.totalIncome - dataOutput.summary.totalExpense).toFixed(2)} 元`]
        ];
        dv.table(["数据项目", "数值"], summaryTable);

        // 显示全局变量数组结构说明
        dv.header(3, "🌐 全局变量数组结构说明");

        const arrayStructureTable = [
            ["🔗 中文变量名", "window.财务数据全局对象"],
            ["🔗 英文变量名", "window.financialDataGlobal"],
            ["📋 数组说明变量", "window.财务数据数组说明"],
            ["🛠️ 数据管理器", "window.财务数据管理器"]
        ];
        dv.table(["变量类型", "变量名称"], arrayStructureTable);

        // 显示数据管理器功能说明
        dv.header(4, "🛠️ 数据管理器功能");
        const managerFunctionsTable = [
            ["快速访问", "window.财务数据管理器.获取支出数据()", "直接获取支出数组"],
            ["数据统计", "window.财务数据管理器.净收支()", "计算净收支金额"],
            ["数据筛选", "window.财务数据管理器.按类型筛选支出('餐饮')", "筛选特定类型支出"],
            ["数据验证", "window.财务数据管理器.数据是否有效()", "检查数据有效性"],
            ["数据摘要", "window.财务数据管理器.获取数据摘要()", "获取完整数据摘要"]
        ];
        dv.table(["功能类型", "调用方法", "功能说明"], managerFunctionsTable);

        // 显示数组详细结构
        const arrayDetailsTable = [
            ["📈 收入记录数组", `income[${dataOutput.income.length}]`, "日期、时间、类型、金额、来源、备注"],
            ["📉 支出记录数组", `expense[${dataOutput.expense.length}]`, "日期、时间、类型、金额、项目、必要性、备注"],
            ["📊 汇总统计对象", "summary{}", "文件统计、金额汇总、文件列表"],
            ["🔍 查询报告对象", "queryReport{}", "分层查询状态、结果详情"]
        ];
        dv.table(["数据类型", "数组/对象结构", "包含字段"], arrayDetailsTable);

        // 显示数组索引示例（如果有数据）
        if (dataOutput.expense.length > 0) {
            dv.header(4, "📉 支出数组索引示例");
            const expenseExampleTable = [
                ["数组索引", "访问方式", "示例数据"],
                ["第0个元素", "window.财务数据全局对象.expense[0]", JSON.stringify(dataOutput.expense[0])],
                ["数组长度", "window.财务数据全局对象.expense.length", dataOutput.expense.length.toString()]
            ];
            dv.table(["索引说明", "JavaScript访问", "实际内容"], expenseExampleTable);
        }

        if (dataOutput.income.length > 0) {
            dv.header(4, "📈 收入数组索引示例");
            const incomeExampleTable = [
                ["数组索引", "访问方式", "示例数据"],
                ["第0个元素", "window.财务数据全局对象.income[0]", JSON.stringify(dataOutput.income[0])],
                ["数组长度", "window.财务数据全局对象.income.length", dataOutput.income.length.toString()]
            ];
            dv.table(["索引说明", "JavaScript访问", "实际内容"], incomeExampleTable);
        }
        
    } catch (error) {
        console.log('❌ 数据输出失败:', error.message);
        dv.paragraph(`❌ **数据输出失败** - ${error.message}`);
    }
} else {
    dv.paragraph(`⚠️ **无数据输出** - 未获取到有效的财务数据`);
}

console.log('✅ 财务仪表板模块一 - 完整执行完成');
```

---

## 🔗 **模块二链接**

模块二已独立为单独文件，请点击下方链接访问：

**📊 [[财务仪表板-模块二-图表版|🔢 模块二：数据分析与计算（图表版）]]**

### **使用说明**
1. **先运行模块一** - 在当前页面执行数据收集
2. **确认数据输出** - 看到"📤 数据输出完成"提示
3. **点击上方链接** - 跳转到模块二查看图表分析

---

## ✅ **模块一：数据收集与汇总 - 完成！**

### **🎯 实现的核心功能**

#### **✅ 万能化分层查询系统**
1. **5层数据源查询** - 年度→季度→月度→周→日记的智能降级
2. **详细状态报告** - 每层显示"成功"、"无数据"或"失败"
3. **智能降级机制** - 高层无数据时自动降级到下层
4. **通用性处理** - 无论哪层有数据都能正确处理

#### **✅ 全局数据输出系统**
1. **window对象存储** - 使用`window.financialDataGlobal`实现模块间数据传递
2. **数据完整性** - 包含时间戳、数据源、汇总统计、原始数据
3. **状态反馈** - 详细的数据输出状态和摘要信息
4. **错误处理** - 完善的异常处理和友好提示

#### **✅ 实用汇总展示系统**
1. **📊 财务总览** - 一目了然的总收入、总支出、净剩余、日均支出
2. **📅 每日余额追踪** - 每天的资金变化，方便找到账目问题
3. **🏆 支出排行榜** - 前5名支出类别，知道钱都花哪了
4. **✅ 数据质量检查** - 自动检查数据完整性和异常情况
5. **📋 最近记录验证** - 简化显示最新记录，快速核对

### **🔧 技术特色**

#### **全局变量管理**
```javascript
// 财务系统专用全局变量
window.financialDataGlobal = {
    timestamp: "2025-07-24T12:30:00.000Z",
    source: "daily",
    summary: { totalIncome: 138.04, totalExpense: 25.70, ... },
    income: [...],
    expense: [...],
    queryReport: {...}
};
```

#### **模块化架构**
- **模块一** - 数据收集与汇总，输出到全局变量
- **模块二** - 从全局变量读取，进行图表化分析
- **清晰分离** - 每个模块职责明确，便于维护

---

## 🎯 **财务系统全局变量说明**

### **变量名称**：`window.financialDataGlobal`

### **数据结构**：
```javascript
{
    timestamp: "ISO时间戳",
    source: "数据源类型(daily/monthly/quarterly/annual)",
    summary: {
        totalFiles: "扫描文件总数",
        processedFiles: "处理文件数量",
        totalIncome: "总收入金额",
        totalExpense: "总支出金额",
        processedFilesList: ["处理的文件列表"],
        skippedFilesList: ["跳过的文件列表"]
    },
    income: [收入记录数组],
    expense: [支出记录数组],
    dailyBalance: [  // 新增：每日余额数组
        {
            date: "日期",
            income: "当日收入",
            expense: "当日支出",
            dayNet: "当日净额",
            cumulativeBalance: "累计余额"
        }
    ],
    queryReport: {查询报告详情}
}
```

### **使用流程**：
1. **模块一执行** → 数据收集 → 保存到`window.financialDataGlobal`
2. **模块二执行** → 从`window.financialDataGlobal`读取 → 纯粹计算 → 保存到`window.financialAnalysisGlobal`
3. **模块三执行** → 从`window.financialAnalysisGlobal`读取 → 图表生成 → 保存到`window.financialChartsGlobal`
4. **模块四执行** → 汇总所有结果 → 统一输出到生态系统

**🚀 下一步：运行模块二进行纯粹计算分析**
