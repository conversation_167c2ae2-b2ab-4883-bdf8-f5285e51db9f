# 💰 财务仪表板 - 模块二：纯粹计算分析版

> [!analysis] 🔢 **纯粹计算分析引擎**
> 基于模块一数据的多维度分析计算，专注数据处理，不负责图表生成

## 🎯 **模块二职责定位**

### **核心功能**
- **数据读取**：从 `window.financialDataGlobal` 读取模块一的数据
- **纯粹计算**：多维度分析计算，不涉及图表生成
- **结果输出**：将分析结果保存到 `window.financialAnalysisGlobal`
- **为模块三准备**：提供标准化的计算结果供图表生成使用

### **不负责的功能**
- ❌ 图表生成（交给模块三处理）
- ❌ 数据收集（模块一已完成）
- ❌ 生态系统输出（模块四统一处理）

---

## 🔢 **模块二：纯粹计算分析引擎**

```dataviewjs
// ===== 模块二：纯粹计算分析引擎 =====

// 主执行函数，避免顶层return语句
function executeModule2() {
    console.log('🔢 开始执行模块二：纯粹计算分析');

    // 1. 详细的数据存在性检查
    console.log('🔍 检查全局变量状态...');
    console.log('window.financialDataGlobal 存在:', !!window.financialDataGlobal);
    console.log('window.财务数据全局对象 存在:', !!window.财务数据全局对象);

    // 尝试从多个可能的全局变量获取数据
    let financialData = null;

    if (window.financialDataGlobal) {
        financialData = window.financialDataGlobal;
        console.log('✅ 从 window.financialDataGlobal 获取数据');
    } else if (window.财务数据全局对象) {
        financialData = window.财务数据全局对象;
        console.log('✅ 从 window.财务数据全局对象 获取数据');
    } else {
        // 显示详细的错误信息和解决方案
        dv.header(2, "❌ 模块二执行失败");
        dv.paragraph('**错误原因**：找不到模块一的数据');
        dv.paragraph('**解决方案**：');
        dv.paragraph('1. 请先运行 [[财务仪表板-模块一-干净版]] ');
        dv.paragraph('2. 确保看到 "📤 数据输出完成" 提示');
        dv.paragraph('3. 然后再运行本模块二');

        dv.header(3, "🔍 调试信息");
        dv.paragraph(`当前时间：${new Date().toLocaleString()}`);
        dv.paragraph(`window.financialDataGlobal：${!!window.financialDataGlobal}`);
        dv.paragraph(`window.财务数据全局对象：${!!window.财务数据全局对象}`);

        console.error('❌ 模块二错误：未找到任何财务数据全局变量');
        return false; // 函数内部可以使用return
    }

    console.log('✅ 成功读取模块一数据:', financialData.source, financialData.timestamp);

    // 2. 数据结构验证
    if (!financialData || typeof financialData !== 'object') {
        dv.paragraph('❌ **数据格式错误**：数据不是有效对象');
        console.error('❌ 数据格式错误:', financialData);
        return false;
    }

    if (!financialData.expense || !financialData.income) {
        dv.paragraph('❌ **数据格式错误**：缺少收入或支出数据');
        console.error('❌ 数据格式错误 - expense存在:', !!financialData.expense, 'income存在:', !!financialData.income);
        return false;
    }

    // 确保数据是数组格式
    if (!Array.isArray(financialData.expense)) {
        dv.paragraph('❌ **数据格式错误**：支出数据不是数组格式');
        console.error('❌ expense不是数组:', typeof financialData.expense);
        return false;
    }

    if (!Array.isArray(financialData.income)) {
        dv.paragraph('❌ **数据格式错误**：收入数据不是数组格式');
        console.error('❌ income不是数组:', typeof financialData.income);
        return false;
    }

    dv.header(2, "📊 模块二执行状态");
    dv.paragraph(`✅ **数据源**：${financialData.source || '未知'}`);
    dv.paragraph(`✅ **数据时间**：${financialData.timestamp ? new Date(financialData.timestamp).toLocaleString() : '未知'}`);
    dv.paragraph(`✅ **收入记录**：${financialData.income.length} 条`);
    dv.paragraph(`✅ **支出记录**：${financialData.expense.length} 条`);
    dv.paragraph(`🔢 **开始计算**：${new Date().toLocaleString()}`);

    // ===== 核心计算函数定义 =====

// 基础统计计算
function calculateBasicStats(data) {
    const totalIncome = data.income.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    const totalExpense = data.expense.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    const netIncome = totalIncome - totalExpense;
    const savingsRate = totalIncome > 0 ? (netIncome / totalIncome) * 100 : 0;
    
    return {
        totalIncome: Math.round(totalIncome * 100) / 100,
        totalExpense: Math.round(totalExpense * 100) / 100,
        netIncome: Math.round(netIncome * 100) / 100,
        savingsRate: Math.round(savingsRate * 100) / 100,
        incomeCount: data.income.length,
        expenseCount: data.expense.length
    };
}

// 支出分类分析
function analyzeExpenseCategories(expenses) {
    const categoryStats = {};
    
    expenses.forEach(expense => {
        const category = expense.type || '其他';
        const amount = parseFloat(expense.amount) || 0;
        
        if (!categoryStats[category]) {
            categoryStats[category] = {
                total: 0,
                count: 0,
                items: []
            };
        }
        
        categoryStats[category].total += amount;
        categoryStats[category].count += 1;
        categoryStats[category].items.push(expense);
    });
    
    // 计算每个类别的平均值和占比
    const totalExpense = expenses.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    
    Object.keys(categoryStats).forEach(category => {
        const stats = categoryStats[category];
        stats.average = Math.round((stats.total / stats.count) * 100) / 100;
        stats.percentage = totalExpense > 0 ? Math.round((stats.total / totalExpense) * 10000) / 100 : 0;
        stats.total = Math.round(stats.total * 100) / 100;
    });
    
    return categoryStats;
}

// 必要性分析
function analyzeNecessity(expenses) {
    const necessityStats = {};
    
    expenses.forEach(expense => {
        const necessity = expense.necessity || '未分类';
        const amount = parseFloat(expense.amount) || 0;
        
        if (!necessityStats[necessity]) {
            necessityStats[necessity] = {
                total: 0,
                count: 0
            };
        }
        
        necessityStats[necessity].total += amount;
        necessityStats[necessity].count += 1;
    });
    
    // 计算占比
    const totalExpense = expenses.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
    
    Object.keys(necessityStats).forEach(necessity => {
        const stats = necessityStats[necessity];
        stats.percentage = totalExpense > 0 ? Math.round((stats.total / totalExpense) * 10000) / 100 : 0;
        stats.total = Math.round(stats.total * 100) / 100;
    });
    
    return necessityStats;
}

// 异常支出检测
function detectAnomalies(expenses) {
    if (expenses.length === 0) return [];
    
    const amounts = expenses.map(e => parseFloat(e.amount) || 0);
    const mean = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
    const variance = amounts.reduce((sum, amount) => sum + Math.pow(amount - mean, 2), 0) / amounts.length;
    const stdDev = Math.sqrt(variance);
    
    // 定义异常阈值（超过平均值 + 2倍标准差）
    const anomalyThreshold = mean + (2 * stdDev);
    
    const anomalies = expenses.filter(expense => {
        const amount = parseFloat(expense.amount) || 0;
        return amount > anomalyThreshold && amount > 50; // 至少50元才考虑为异常
    });
    
    return anomalies.map(expense => ({
        ...expense,
        anomalyScore: Math.round(((parseFloat(expense.amount) || 0) / mean) * 100) / 100
    }));
}

// 时间趋势分析（如果有多天数据）
function analyzeTrends(data) {
    const dailyStats = {};
    
    // 分析支出趋势
    data.expense.forEach(expense => {
        const date = expense.date || 'unknown';
        const amount = parseFloat(expense.amount) || 0;
        
        if (!dailyStats[date]) {
            dailyStats[date] = {
                expense: 0,
                income: 0,
                expenseCount: 0,
                incomeCount: 0
            };
        }
        
        dailyStats[date].expense += amount;
        dailyStats[date].expenseCount += 1;
    });
    
    // 分析收入趋势
    data.income.forEach(income => {
        const date = income.date || 'unknown';
        const amount = parseFloat(income.amount) || 0;
        
        if (!dailyStats[date]) {
            dailyStats[date] = {
                expense: 0,
                income: 0,
                expenseCount: 0,
                incomeCount: 0
            };
        }
        
        dailyStats[date].income += amount;
        dailyStats[date].incomeCount += 1;
    });
    
    // 计算每日净收支
    Object.keys(dailyStats).forEach(date => {
        const stats = dailyStats[date];
        stats.netIncome = Math.round((stats.income - stats.expense) * 100) / 100;
        stats.expense = Math.round(stats.expense * 100) / 100;
        stats.income = Math.round(stats.income * 100) / 100;
    });
    
    return dailyStats;
}

// 三层资金状态计算（基于严厉教练系统）
function calculateThreeTierStatus(basicStats) {
    // 这里需要根据实际的资金状况来计算
    // 暂时使用示例逻辑，实际应该基于用户的资金配置
    const monthlyIncome = basicStats.totalIncome;
    const monthlyExpense = basicStats.totalExpense;
    
    return {
        基础生存层: {
            预算金额: Math.round(monthlyExpense * 1.2), // 120%的基础支出
            当前状态: monthlyIncome > monthlyExpense * 1.2 ? '安全' : '紧张',
            建议: monthlyIncome > monthlyExpense * 1.2 ? '保持当前水平' : '需要增加收入或减少支出'
        },
        安全缓冲层: {
            预算金额: Math.round(monthlyExpense * 0.5), // 50%的缓冲
            当前状态: basicStats.savingsRate > 20 ? '充足' : '不足',
            建议: basicStats.savingsRate > 20 ? '继续积累' : '提高储蓄率'
        },
        自由支配层: {
            预算金额: Math.round(monthlyIncome * 0.2), // 20%的自由支配
            当前状态: basicStats.netIncome > monthlyIncome * 0.2 ? '宽裕' : '紧张',
            建议: basicStats.netIncome > monthlyIncome * 0.2 ? '可以适度消费' : '控制非必要支出'
        }
    };
}

// 风险评估
function assessRisk(basicStats, categoryStats, anomalies) {
    let riskScore = 0;
    const riskFactors = [];
    
    // 储蓄率风险
    if (basicStats.savingsRate < 10) {
        riskScore += 30;
        riskFactors.push('储蓄率过低');
    } else if (basicStats.savingsRate < 20) {
        riskScore += 15;
        riskFactors.push('储蓄率偏低');
    }
    
    // 支出集中度风险
    const categoryValues = Object.values(categoryStats);
    const maxCategoryPercentage = Math.max(...categoryValues.map(c => c.percentage));
    if (maxCategoryPercentage > 60) {
        riskScore += 20;
        riskFactors.push('支出过度集中');
    }
    
    // 异常支出风险
    if (anomalies.length > 0) {
        riskScore += anomalies.length * 10;
        riskFactors.push(`存在${anomalies.length}项异常支出`);
    }
    
    // 负现金流风险
    if (basicStats.netIncome < 0) {
        riskScore += 40;
        riskFactors.push('负现金流');
    }
    
    let riskLevel = '低';
    if (riskScore > 60) riskLevel = '高';
    else if (riskScore > 30) riskLevel = '中';
    
    return {
        riskScore: Math.min(riskScore, 100),
        riskLevel,
        riskFactors,
        recommendations: generateRiskRecommendations(riskFactors)
    };
}

// 风险建议生成
function generateRiskRecommendations(riskFactors) {
    const recommendations = [];
    
    if (riskFactors.includes('储蓄率过低') || riskFactors.includes('储蓄率偏低')) {
        recommendations.push('建议制定储蓄计划，目标储蓄率20%以上');
    }
    
    if (riskFactors.includes('支出过度集中')) {
        recommendations.push('建议分散支出类别，避免单一类别占比过高');
    }
    
    if (riskFactors.some(f => f.includes('异常支出'))) {
        recommendations.push('建议审查大额支出的必要性，建立支出审批机制');
    }
    
    if (riskFactors.includes('负现金流')) {
        recommendations.push('紧急建议：立即减少非必要支出，寻找增收途径');
    }
    
    return recommendations;
}

// ===== 执行所有计算 =====

console.log('🔢 开始执行多维度分析计算...');

const analysisResults = {
    // 基础统计
    basicStats: calculateBasicStats(financialData),
    
    // 分类分析
    categoryAnalysis: analyzeExpenseCategories(financialData.expense),
    
    // 必要性分析
    necessityAnalysis: analyzeNecessity(financialData.expense),
    
    // 异常检测
    anomalies: detectAnomalies(financialData.expense),
    
    // 趋势分析
    trendAnalysis: analyzeTrends(financialData),
    
    // 三层资金状态
    threeTierStatus: calculateThreeTierStatus(calculateBasicStats(financialData)),
    
    // 风险评估
    riskAssessment: assessRisk(
        calculateBasicStats(financialData),
        analyzeExpenseCategories(financialData.expense),
        detectAnomalies(financialData.expense)
    ),
    
    // 元数据
    metadata: {
        calculationTime: new Date().toISOString(),
        sourceData: {
            timestamp: financialData.timestamp,
            source: financialData.source
        },
        version: '2.0'
    }
};

// ===== 保存分析结果到全局变量 =====

window.financialAnalysisGlobal = analysisResults;
console.log('✅ 分析结果已保存到 window.financialAnalysisGlobal');

// ===== 显示计算结果摘要 =====

dv.header(2, "📊 计算结果摘要");

// 基础统计摘要
dv.header(3, "💰 基础统计");
const basicStatsTable = [
    ["总收入", `${analysisResults.basicStats.totalIncome} 元`],
    ["总支出", `${analysisResults.basicStats.totalExpense} 元`],
    ["净收支", `${analysisResults.basicStats.netIncome} 元`],
    ["储蓄率", `${analysisResults.basicStats.savingsRate}%`]
];
dv.table(["指标", "数值"], basicStatsTable);

// 分类分析摘要
dv.header(3, "📊 支出分类TOP3");
const sortedCategories = Object.entries(analysisResults.categoryAnalysis)
    .sort(([,a], [,b]) => b.total - a.total)
    .slice(0, 3);

const categoryTable = sortedCategories.map(([category, stats]) => [
    category,
    `${stats.total} 元`,
    `${stats.percentage}%`,
    `${stats.count} 笔`
]);
dv.table(["类别", "金额", "占比", "笔数"], categoryTable);

// 风险评估摘要
dv.header(3, "⚠️ 风险评估");
dv.paragraph(`**风险等级**：${analysisResults.riskAssessment.riskLevel} (${analysisResults.riskAssessment.riskScore}分)`);
if (analysisResults.riskAssessment.riskFactors.length > 0) {
    dv.paragraph(`**风险因素**：${analysisResults.riskAssessment.riskFactors.join('、')}`);
}

// 异常支出提醒
if (analysisResults.anomalies.length > 0) {
    dv.header(3, "🚨 异常支出提醒");
    const anomalyTable = analysisResults.anomalies.map(anomaly => [
        anomaly.date || '未知',
        anomaly.type || '未分类',
        `${anomaly.amount} 元`,
        `${anomaly.anomalyScore}x`
    ]);
    dv.table(["日期", "类别", "金额", "异常倍数"], anomalyTable);
}

dv.header(2, "✅ 模块二执行完成");
dv.paragraph(`🔢 **计算完成时间**：${new Date().toLocaleString()}`);
dv.paragraph(`📊 **分析维度**：${Object.keys(analysisResults).length - 1} 个`);
dv.paragraph(`💾 **数据已保存**：window.financialAnalysisGlobal`);
dv.paragraph(`🚀 **下一步**：运行模块三进行图表生成`);

    console.log('✅ 模块二：纯粹计算分析 - 执行完成');
    return true; // 成功执行
}

// 执行主函数
executeModule2();
```

---

## 🎯 **模块二输出说明**

### **全局变量**：`window.financialAnalysisGlobal`

### **数据结构**：
```javascript
{
    basicStats: {          // 基础统计
        totalIncome: 数值,
        totalExpense: 数值,
        netIncome: 数值,
        savingsRate: 数值
    },
    categoryAnalysis: {    // 分类分析
        "类别名": {
            total: 数值,
            count: 数值,
            percentage: 数值
        }
    },
    necessityAnalysis: {   // 必要性分析
        "必要性": {
            total: 数值,
            count: 数值,
            percentage: 数值
        }
    },
    anomalies: [],         // 异常支出数组
    trendAnalysis: {},     // 趋势分析
    threeTierStatus: {},   // 三层资金状态
    riskAssessment: {},    // 风险评估
    metadata: {}           // 元数据
}
```

### **使用流程**：
1. **模块一执行** → 数据收集 → 保存到`window.financialDataGlobal`
2. **模块二执行** → 从`window.financialDataGlobal`读取 → 纯粹计算 → 保存到`window.financialAnalysisGlobal`
3. **模块三执行** → 从`window.financialAnalysisGlobal`读取 → 图表生成 → 保存到`window.financialChartsGlobal`
4. **模块四执行** → 汇总所有结果 → 统一输出到生态系统

**🚀 下一步：开发模块三进行图表化展示**
