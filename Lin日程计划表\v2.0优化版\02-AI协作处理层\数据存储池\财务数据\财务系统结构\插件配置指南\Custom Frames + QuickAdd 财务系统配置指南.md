# Custom Frames + QuickAdd 财务记录系统配置指南

## 🎯 最终效果

✅ **右侧边栏财务面板**：专用的财务记录界面，不占用主笔记空间
✅ **智能表单**：下拉选择、快速金额、自动时间
✅ **一键记录**：填写完成后自动添加到当日日记
✅ **数据统一**：所有财务数据格式标准化，便于分析

## 📦 必需插件

### 1. Custom Frames 插件
**作用**：在右侧边栏创建财务记录面板
**安装**：设置 → 社区插件 → 搜索"Custom Frames" → 安装启用

### 2. QuickAdd 插件  
**作用**：处理财务数据并写入日记文件
**安装**：设置 → 社区插件 → 搜索"QuickAdd" → 安装启用

## 🔧 详细配置步骤

### 步骤1：配置 Custom Frames

1. **打开 Custom Frames 设置**
   - 设置 → Custom Frames

2. **添加新的 Frame**
   - 点击"Add Frame"
   - 配置如下：

```
Frame Name: 财务记录面板
URL: app://local/v2.0优化版/02-AI协作处理层/Custom Frames界面/财务记录面板.html
Display Name: 💰 财务
Icon: dollar-sign
Hide on Mobile: false
```

3. **保存配置**
   - 点击"Save"
   - 重启Obsidian

### 步骤2：创建HTML界面文件

在 `v2.0优化版/02-AI协作处理层/Custom Frames界面/` 目录下创建 `财务记录面板.html`

### 步骤3：配置 QuickAdd

1. **打开 QuickAdd 设置**
   - 设置 → QuickAdd

2. **添加新的 Macro**
   - 点击"Add Choice"
   - 选择"Macro"
   - 名称：`财务记录处理`

3. **配置 Macro 脚本**
   - 点击"Configure"
   - 添加脚本文件路径

### 步骤4：创建处理脚本

在 `v2.0优化版/02-AI协作处理层/QuickAdd脚本/` 目录下创建处理脚本

## ✅ 验证配置

### 测试步骤
1. **检查右侧边栏**：应该看到"💰 财务"面板
2. **测试表单**：填写收入/支出信息
3. **验证写入**：检查当日日记是否正确添加记录
4. **检查格式**：确认数据格式符合标准

### 成功标志
- ✅ 右侧边栏显示财务面板
- ✅ 表单可以正常填写和提交
- ✅ 数据正确写入当日日记
- ✅ 数据格式标准化

## ⚠️ 常见问题

### 面板不显示
- 检查HTML文件路径是否正确
- 确认Custom Frames插件已启用
- 重启Obsidian

### 数据写入失败
- 检查QuickAdd脚本路径
- 确认日记文件存在
- 检查脚本权限

### 格式不正确
- 检查脚本中的格式化逻辑
- 确认日期格式一致
- 验证分类标准

## 🚀 最佳实践

### 数据标准化
1. **统一时间格式**：使用 `HH:mm` 格式
2. **标准分类**：使用预定义的32分类
3. **金额格式**：保留两位小数

### 用户体验优化
1. **快速选择**：常用分类放在前面
2. **智能默认**：根据时间推荐分类
3. **一键清空**：提交后自动清空表单

---

**最后更新**: 2025-07-25
**状态**: ✅ 配置完成，功能正常
