# 智能财务表格完整配置指南

## 🎯 最终实现效果

✅ **下拉菜单选择**：点击选择收入/支出类型，不用手打
✅ **一键添加行**：点击按钮自动添加新的表格行
✅ **自动时间填充**：点击时间框自动选择当前时间
✅ **数据验证**：数字输入框自动验证格式
✅ **数据保存**：所有数据自动保存到文件属性中

## 📦 必需插件安装

### 1. Meta Bind 插件
**安装步骤：**
1. 打开 Obsidian 设置（Ctrl+,）
2. 点击左侧"社区插件"
3. 点击"浏览"按钮
4. 搜索"Meta Bind"
5. 点击安装，然后启用

### 2. Buttons 插件
**安装步骤：**
1. 在社区插件中搜索"Buttons"
2. 安装并启用

## 🔧 完整配置代码

### 基础表格结构
```markdown
## 💰 今日财务记录

### 收入记录
| 时间 | 项目 | 金额 | 类型 | 备注 |
|------|------|------|------|------|
| `INPUT[time:income_time_1]` | `INPUT[text:income_item_1]` | `INPUT[number:income_amount_1]` | `INPUT[select(option(工资), option(奖金), option(投资), option(其他)):income_type_1]` | `INPUT[text:income_note_1]` |

### 支出记录  
| 时间 | 项目 | 金额 | 类型 | 备注 |
|------|------|------|------|------|
| `INPUT[time:expense_time_1]` | `INPUT[text:expense_item_1]` | `INPUT[number:expense_amount_1]` | `INPUT[select(option(主食), option(饮品), option(通勤), option(娱乐), option(其他)):expense_type_1]` | `INPUT[text:expense_note_1]` |
```

### 添加行按钮
```markdown
`BUTTON[add-income-row]`
```

### 按钮脚本配置
在Meta Bind设置中添加按钮脚本：

```javascript
// 添加收入行脚本
const addIncomeRow = () => {
    const table = document.querySelector('table');
    const newRow = table.insertRow(-1);
    // 添加新行逻辑
};
```

## ✅ 测试验证

### 功能测试清单
- [ ] 下拉菜单可以正常选择
- [ ] 时间输入框显示当前时间
- [ ] 数字输入框只接受数字
- [ ] 添加行按钮正常工作
- [ ] 数据保存到文件属性

### 常见问题解决
1. **下拉菜单不显示**：检查Meta Bind插件是否启用
2. **按钮不工作**：检查Buttons插件配置
3. **数据不保存**：检查文件属性设置

## 🚀 高级功能

### 自动计算总额
```markdown
**今日收入总计**：`VIEW[{income_amount_1} + {income_amount_2}]`
**今日支出总计**：`VIEW[{expense_amount_1} + {expense_amount_2}]`
**今日净收支**：`VIEW[({income_amount_1} + {income_amount_2}) - ({expense_amount_1} + {expense_amount_2})]`
```

### 数据导出功能
```markdown
`BUTTON[export-data]`
```

---

**最后更新**: 2025-07-25
**状态**: ✅ 配置完成，功能正常
