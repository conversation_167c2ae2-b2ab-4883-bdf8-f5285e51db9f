# 财务表格动态按钮配置

## 需要安装的插件

### 1. Meta Bind
- **功能**：提供交互式输入组件（下拉菜单、数字输入、时间选择等）
- **安装**：设置 → 社区插件 → 搜索"Meta Bind" → 安装启用

### 2. Buttons
- **功能**：创建可点击的按钮，执行特定操作
- **安装**：设置 → 社区插件 → 搜索"Buttons" → 安装启用

### 3. Templater
- **功能**：高级模板功能，支持JavaScript脚本
- **安装**：设置 → 社区插件 → 搜索"Templater" → 安装启用

## 按钮脚本配置

### 添加收入行按钮脚本
```javascript
// 在Templater设置中添加此脚本
function addIncomeRow() {
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) return;
    
    app.vault.read(activeFile).then(content => {
        // 找到收入表格位置
        const incomeTableRegex = /(\| `INPUT\[time:income_time_\d+\]`.*\n)/g;
        const matches = [...content.matchAll(incomeTableRegex)];
        
        if (matches.length > 0) {
            const lastMatch = matches[matches.length - 1];
            const nextIndex = matches.length + 1;
            
            const newRow = `| \`INPUT[time:income_time_${nextIndex}]\` | \`INPUT[suggester(option(工资), option(奖金), option(兼职), option(投资收益), option(其他收入)):income_type_${nextIndex}]\` | \`INPUT[number:income_amount_${nextIndex}]\` | \`INPUT[text:income_source_${nextIndex}]\` | \`INPUT[text:income_note_${nextIndex}]\` |\n`;
            
            const newContent = content.replace(lastMatch[0], lastMatch[0] + newRow);
            app.vault.modify(activeFile, newContent);
        }
    });
}
```

### 添加支出行按钮脚本
```javascript
// 在Templater设置中添加此脚本
function addExpenseRow() {
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) return;
    
    app.vault.read(activeFile).then(content => {
        // 找到支出表格位置
        const expenseTableRegex = /(\| `INPUT\[time:expense_time_\d+\]`.*\n)/g;
        const matches = [...content.matchAll(expenseTableRegex)];
        
        if (matches.length > 0) {
            const lastMatch = matches[matches.length - 1];
            const nextIndex = matches.length + 1;
            
            const newRow = `| \`INPUT[time:expense_time_${nextIndex}]\` | \`INPUT[suggester(option(餐饮), option(交通), option(购物), option(娱乐), option(学习), option(医疗), option(房租), option(水电), option(通讯), option(其他)):expense_type_${nextIndex}]\` | \`INPUT[number:expense_amount_${nextIndex}]\` | \`INPUT[text:expense_item_${nextIndex}]\` | \`INPUT[suggester(option(必需), option(重要), option(一般), option(冲动)):necessity_${nextIndex}]\` | \`INPUT[text:expense_note_${nextIndex}]\` |\n`;
            
            const newContent = content.replace(lastMatch[0], lastMatch[0] + newRow);
            app.vault.modify(activeFile, newContent);
        }
    });
}
```

## 使用说明

1. **安装插件**：按照上面的列表安装三个插件
2. **配置Templater**：将按钮脚本添加到Templater的用户脚本中
3. **使用方法**：
   - 点击"添加收入行"按钮自动增加收入记录行
   - 点击"添加支出行"按钮自动增加支出记录行
   - 使用下拉菜单选择类型，避免手动输入错误
   - 数字输入框自动验证格式
   - 时间选择器方便选择时间

## 替代方案

如果上述插件配置复杂，也可以使用：

### 方案2：Database Folder插件
- 将财务记录作为数据库管理
- 支持表格视图和表单视图
- 可以轻松添加、编辑、删除记录

### 方案3：Dataview + 简单表格
- 使用Dataview查询和显示数据
- 手动复制粘贴表格行（最简单但不够智能）

## 推荐配置顺序

1. 先安装Meta Bind插件，测试下拉选择功能
2. 再安装Buttons插件，配置添加行按钮
3. 最后配置Templater脚本，实现动态功能

这样您就能实现真正智能的财务记录表格了！
