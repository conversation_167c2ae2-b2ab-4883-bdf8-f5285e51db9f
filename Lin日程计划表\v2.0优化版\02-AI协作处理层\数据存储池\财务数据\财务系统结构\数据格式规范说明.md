# 📋 数据格式规范说明

> [!data] 📊 **数据标准**
> 财务数据的标准格式规范，确保AI能够准确提取和处理所有财务信息

## 💰 收入记录格式

### **标准表格结构**

```markdown
### 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源 | 性质 | 备注 |
|------|----------|------|------|------|------|
| 09:00 | 工资 | 5000元 | 公司发薪 | 🟢 固定 | 月薪 |
| 14:30 | 兼职 | 500元 | 设计项目 | 🟡 临时 | 外包收入 |
| 16:00 | 投资 | 120元 | 股票分红 | 🔵 被动 | 年度分红 |
```

### **字段说明**

- **时间**：HH:MM 格式，24小时制
- **收入类型**：工资、兼职、投资、奖金、其他
- **金额**：数字+元，如 "5000元"
- **来源**：具体的收入来源描述
- **性质**：🟢固定 / 🟡临时 / 🔵被动 / 🔴一次性
- **备注**：可选，记录特殊情况

### **收入分类标准**

**主要分类及图标**：
```text
💼 工资    - 固定月薪、年薪、基本工资
💻 兼职    - 自由职业、外包项目、临时工作
📈 投资    - 股票、基金、理财产品收益
🎁 奖金    - 年终奖、绩效奖、项目奖励
🔄 其他    - 无法归类的收入
```

**扩展收入分类**：
```text
🏠 租金收入  💰 利息收入  🎯 佣金收入  📚 版权收入
🛍️ 二手出售  🎮 游戏收入  💡 专利收入  🎨 创作收入
```

## 📉 支出记录格式

### **标准表格结构**

```markdown
### 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|
| 13:34 | 快递 | 0.5元 | 取快递 | 🔴 必需 |  |
| 14:41 | 交通 | 2元 | 回家 | 🔴 必需 |  |
| 23:31 | 餐饮 | 6.8元 | 港式奶茶 | 🔵 冲动 | 就是想喝 |
```

### **字段说明**

- **时间**：HH:MM 格式，24小时制
- **支出类型**：预定义16分类（参考[[16分类架构设计]]）
- **金额**：数字+元，如 "6.8元"
- **具体项目**：简短描述购买的具体物品或服务
- **必要性**：🔴必需 / 🟡重要 / 🟢一般 / 🔵冲动
- **备注**：可选，记录特殊情况或感受

### **支出分类标准**

**16分类系统**（详见[[16分类架构设计]]）：

**🔴 必需支出**：
```text
🍽️ 餐饮    - 日常用餐、饮品、零食
🚗 交通    - 公交、地铁、打车、共享单车
🏠 住房    - 房租、水电、物业费
🏥 医疗    - 看病、买药、体检
```

**🟡 生活支出**：
```text
🛍️ 购物    - 日用品、服装、电子产品
🎮 娱乐    - 电影、游戏、娱乐活动
👥 社交    - 聚餐、礼品、社交活动
📦 快递    - 邮费、快递费、配送费
```

**🟢 投资支出**：
```text
📚 学习    - 书籍、课程、培训
💪 技能    - 技能培训、证书考试
🔧 工具    - 软件、设备、工具
🤝 人脉    - 商务社交、人脉维护
```

**🔵 储备支出**：
```text
🚨 应急    - 紧急情况储备
🎯 机会    - 机会投资资金
💰 理财    - 财务增值投资
💼 其他    - 灵活机动资金
```

## 🔍 数据识别规则

### **收入 vs 支出的识别逻辑**

1. **表格标题区分**：
   - `### 📈 收入记录` - 收入表格
   - `### 📉 支出记录` - 支出表格

2. **金额符号区分**：
   - 收入：正数，如 "500元"
   - 支出：正数，如 "6.8元"（系统自动识别为负数）

3. **类型字段区分**：
   - 收入类型：工资、兼职、投资、奖金、其他
   - 支出类型：餐饮、交通、购物、娱乐、学习、医疗、快递、其他等16分类

4. **数据提取规则**：
   - 在"收入记录"表格下的数据 → 识别为收入
   - 在"支出记录"表格下的数据 → 识别为支出

## 🔧 正则表达式模式

### **表格行匹配模式**

```javascript
// 收入记录表格行匹配（在"收入记录"标题下）
const incomeSection = content.match(/### 📈 收入记录[\s\S]*?(?=###|$)/);
if (incomeSection) {
    const incomeMatches = incomeSection[0].match(/\| \d{2}:\d{2} \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]*) \|/g);
}

// 支出记录表格行匹配（在"支出记录"标题下）
const expenseSection = content.match(/### 📉 支出记录[\s\S]*?(?=###|$)/);
if (expenseSection) {
    const expenseMatches = expenseSection[0].match(/\| \d{2}:\d{2} \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]*) \|/g);
}

// 金额提取
const amountMatch = amountStr.match(/(\d+(?:\.\d+)?)/);
const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
```

### **时间格式匹配**

```javascript
// 时间格式验证：HH:MM
const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

// 日期格式验证：YYYY-MM-DD
const datePattern = /^\d{4}-\d{2}-\d{2}$/;
```

### **金额格式匹配**

```javascript
// 金额提取：支持小数点
const amountPattern = /(\d+(?:\.\d+)?)元?/;

// 金额验证：确保为正数
function validateAmount(amountStr) {
    const match = amountStr.match(amountPattern);
    if (match) {
        const amount = parseFloat(match[1]);
        return amount > 0 ? amount : null;
    }
    return null;
}
```

## 📁 文件组织结构

### **目录结构标准**

```text
财务数据/
├─ 财务系统结构/              # 模块化文档
│  ├─ README.md
│  ├─ 01-系统目标和运作方式.md
│  ├─ 02-严厉教练形象描述.md
│  ├─ 03-系统实现架构.md
│  ├─ 04-用户界面展示规范.md
│  ├─ 16分类架构设计.md
│  ├─ 动态预算配置方案.md
│  ├─ 反向思维财务哲学.md
│  ├─ 技术实现详细指南.md
│  └─ 数据格式规范说明.md
├─ 年度财务汇总/              # 年度汇总数据
├─ 季度财务汇总/              # 季度汇总数据
├─ 月度财务汇总/              # 月度汇总数据
└─ 财务数据流转模式说明.md    # 主导航文档
```

### **日记文件结构标准**

```text
日记系统/
├─ 2025/
│  ├─ 07-July/
│  │  ├─ 2025-07-22.md
│  │  ├─ 2025-07-23.md
│  │  ├─ 2025-07-24.md
│  │  └─ ...
│  ├─ 08-August/
│  │  ├─ 2025-08-01.md
│  │  └─ ...
│  └─ ...
```

## 📝 完整示例

### **标准日记文件示例**

**2025-07-24.md 文件内容**：

```markdown
# 2025-07-24 星期三

## 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源 | 性质 | 备注 |
|------|----------|------|------|------|------|
| 09:00 | 工资 | 5000元 | 公司发薪 | 🟢 固定 | 月薪 |
| 15:30 | 兼职 | 39元 | 设计项目 | 🟡 临时 | 小项目收入 |

## 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|
| 13:34 | 快递 | 0.5元 | 取快递 | 🔴 必需 |  |
| 14:41 | 交通 | 2元 | 回家 | 🔴 必需 |  |
| 23:31 | 餐饮 | 6.8元 | 港式奶茶 | 🔵 冲动 | 就是想喝 |
```

### **数据提取结果示例**

```javascript
// 提取结果
const extractedData = {
    income: [
        { time: "09:00", type: "工资", amount: 5000, source: "公司发薪", nature: "🟢 固定", note: "月薪" },
        { time: "15:30", type: "兼职", amount: 39, source: "设计项目", nature: "🟡 临时", note: "小项目收入" }
    ],
    expense: [
        { time: "13:34", type: "快递", amount: 0.5, item: "取快递", necessity: "🔴 必需", note: "" },
        { time: "14:41", type: "交通", amount: 2, item: "回家", necessity: "🔴 必需", note: "" },
        { time: "23:31", type: "餐饮", amount: 6.8, item: "港式奶茶", necessity: "🔵 冲动", note: "就是想喝" }
    ]
};
```

## 🔗 **相关文档**

- **分类标准**：[[16分类架构设计]] - 查看完整的16分类系统
- **技术实现**：[[技术实现详细指南]] - 了解数据提取的具体代码
- **系统架构**：[[03-系统实现架构]] - 查看数据处理流程
- **配置参数**：[[动态预算配置方案]] - 了解预算配置格式

---

**📅 文档信息**
- **创建时间**：2024-07-24
- **文档类型**：数据格式标准与规范
- **维护状态**：核心标准，相对稳定
- **目标用户**：AI开发者、数据处理系统
