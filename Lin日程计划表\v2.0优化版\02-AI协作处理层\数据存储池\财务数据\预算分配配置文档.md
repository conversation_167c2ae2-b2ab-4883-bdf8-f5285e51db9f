# 💰 预算分配配置

> [!info] 📋 **配置说明**
> 此文档用于配置32分类的预算分配比例，模块二会自动读取这些配置并根据实际收入计算具体预算金额。
> 
> **使用方法**：
> 1. 修改下方的配比数值
> 2. 确保各大类配比总和为100%
> 3. 确保各小类配比总和为100%
> 4. 保存文件后，模块二会自动应用新配置

## 📊 四大类配比设置（总计100%）

```javascript
const 四大类配比 = {
    "必需支出": 50,  // 50% - 生存必需，不可压缩
    "生活支出": 25,  // 25% - 生活质量，可调整
    "投资支出": 15,  // 15% - 未来增值，重点关注
    "储备支出": 10   // 10% - 风险对冲，灵活运用
};
```

**当前配比验证**：50% + 25% + 15% + 10% = 100% ✅

## 🔧 32小类内部配比设置

### 🔴 必需支出内部分配（100%）

```javascript
const 必需支出配比 = {
    "主食": 40,    // 40% - 一日三餐主要饮食
    "饮品": 8,     // 8%  - 水、咖啡、茶、饮料
    "通勤": 25,    // 25% - 上班地铁、公交
    "打车": 7,     // 7%  - 出租车、网约车
    "房租": 0,     // 0%  - 房租、房贷（如适用）
    "水电": 10,    // 10% - 水电气费、物业费
    "医疗": 5,     // 5%  - 看病、体检
    "药品": 5      // 5%  - 处方药、常用药
};
```

**配比验证**：40% + 8% + 25% + 7% + 0% + 10% + 5% + 5% = 100% ✅

### 🟡 生活支出内部分配（100%）

```javascript
const 生活支出配比 = {
    "服装": 15,    // 15% - 衣服、鞋子、配饰
    "日用品": 20,  // 20% - 洗护用品、生活工具
    "娱乐": 25,    // 25% - 电影、游戏、KTV
    "嗜好": 15,    // 15% - 烟草、酒类、收藏品
    "社交": 10,    // 10% - 聚餐、聚会
    "礼品": 5,     // 5%  - 礼物、红包、份子钱
    "快递": 5,     // 5%  - 邮费、快递费
    "通讯": 5      // 5%  - 话费、流量费
};
```

**配比验证**：15% + 20% + 25% + 15% + 10% + 5% + 5% + 5% = 100% ✅

### 🟢 投资支出内部分配（100%）

```javascript
const 投资支出配比 = {
    "学习": 25,      // 25% - 书籍、课程、培训
    "数字服务": 15,  // 15% - 软件订阅、会员服务
    "技能": 20,      // 20% - 技能培训、证书考试
    "健康": 15,      // 15% - 健身、运动、保健
    "工具": 10,      // 10% - 硬件设备、工作工具
    "维修": 5,       // 5%  - 设备维修、保养
    "人脉": 5,       // 5%  - 商务社交、人脉维护
    "投资": 5        // 5%  - 股票、基金、理财
};
```

**配比验证**：25% + 15% + 20% + 15% + 10% + 5% + 5% + 5% = 100% ✅

### 🔵 储备支出内部分配（100%）

```javascript
const 储备支出配比 = {
    "应急": 30,    // 30% - 紧急情况、意外支出
    "保险": 20,    // 20% - 各类保险费用
    "机会": 20,    // 20% - 投资机会、创业资金
    "试错": 10,    // 10% - 实验性支出、试错成本
    "储蓄": 10,    // 10% - 银行储蓄、定期存款
    "理财": 5,     // 5%  - 理财产品、投资组合
    "其他": 3,     // 3%  - 真正无法归类的支出
    "缓冲": 2      // 2%  - 临时性支出、过渡资金
};
```

**配比验证**：30% + 20% + 20% + 10% + 10% + 5% + 3% + 2% = 100% ✅

## 🔧 技术配置对象（供模块二读取）

```javascript
// 模块二读取的完整配置对象
const 预算分配配置 = {
    四大类配比: {
        "必需支出": 50,
        "生活支出": 25,
        "投资支出": 15,
        "储备支出": 10
    },
    三十二分类配比: {
        "必需支出": {
            "主食": 40, "饮品": 8, "通勤": 25, "打车": 7,
            "房租": 0, "水电": 10, "医疗": 5, "药品": 5
        },
        "生活支出": {
            "服装": 15, "日用品": 20, "娱乐": 25, "嗜好": 15,
            "社交": 10, "礼品": 5, "快递": 5, "通讯": 5
        },
        "投资支出": {
            "学习": 25, "数字服务": 15, "技能": 20, "健康": 15,
            "工具": 10, "维修": 5, "人脉": 5, "投资": 5
        },
        "储备支出": {
            "应急": 30, "保险": 20, "机会": 20, "试错": 10,
            "储蓄": 10, "理财": 5, "其他": 3, "缓冲": 2
        }
    }
};
```

## 📋 配置修改指南

### 🎯 **调整原则**

1. **四大类调整**：
   - 根据个人财务状况调整大类比例
   - 收入较低时可增加必需支出比例
   - 收入较高时可增加投资支出比例

2. **32小类调整**：
   - 根据实际消费习惯调整小类比例
   - 某些分类不适用时可设为0%
   - 其他分类相应增加比例

3. **验证要求**：
   - 四大类配比总和必须等于100%
   - 各大类内部小类配比总和必须等于100%

### 🔄 **常见调整场景**

**场景1：学生/低收入**
```javascript
四大类配比: {
    "必需支出": 60,  // 增加必需支出
    "生活支出": 20,  // 减少生活支出
    "投资支出": 15,  // 保持投资
    "储备支出": 5    // 减少储备
}
```

**场景2：高收入/投资导向**
```javascript
四大类配比: {
    "必需支出": 40,  // 减少必需支出比例
    "生活支出": 20,  // 减少生活支出
    "投资支出": 25,  // 大幅增加投资
    "储备支出": 15   // 增加储备
}
```

---

**📅 配置信息**
- **创建时间**：2025-07-25
- **文档类型**：预算配置文件
- **使用系统**：财务系统模块二
- **自动应用**：保存后立即生效
