# 📋 Obsidian标注块(Callout)使用指南

> [!info] 📖 文档说明
> 本文档详细介绍Obsidian中Callout标注块的使用方法，包括语法、类型、颜色和实际应用场景

## 🎯 什么是Callout标注块

**Callout**（标注块/提示框）是Obsidian中用于突出显示特定内容的格式化工具，通过不同颜色的边框和图标来区分不同类型的信息。

### 基本语法
```markdown
> [!类型] 标题
> 内容文本
> 可以多行
```

## 🎨 Callout类型和颜色对照表

| 类型 | 颜色 | 图标 | 用途 | 适用场景 |
|------|------|------|------|----------|
| `[!note]` | 🔵 蓝色 | 📝 | 一般信息 | 普通说明、备注 |
| `[!abstract]` | 🟦 青色 | 📄 | 摘要总结 | AI生成内容、分析总结 |
| `[!info]` | 🔵 蓝色 | ℹ️ | 信息提示 | 重要信息、说明 |
| `[!quote]` | 🟫 灰色 | 💬 | 引用内容 | 用户原创、引用文字 |
| `[!tip]` | 🟢 绿色 | 💡 | 提示建议 | 使用技巧、建议 |
| `[!success]` | 🟢 绿色 | ✅ | 成功完成 | 完成状态、成功案例 |
| `[!warning]` | 🟡 黄色 | ⚠️ | 警告注意 | 注意事项、重要提醒 |
| `[!danger]` | 🔴 红色 | ⛔ | 危险错误 | 严重问题、错误警告 |
| `[!failure]` | 🔴 红色 | ❌ | 失败错误 | 失败案例、错误记录 |
| `[!bug]` | 🔴 红色 | 🐛 | 程序错误 | 代码bug、系统问题 |
| `[!example]` | 🟣 紫色 | 📋 | 示例演示 | 代码示例、操作演示 |
| `[!question]` | 🟡 黄色 | ❓ | 疑问思考 | 问题记录、思考点 |

## 🎯 在日记系统中的应用

### 📝 情绪事件记录标注
```markdown
## **事件1**：标题
> [!quote] 👤 用户原创记录
> **四个维度**：用户Lin原创填写
> - 🧠 脑子想的：
> - 💓 身体感受：
> - 🗣️ 嘴上说的：
> - 🏃 行动上的：

> [!abstract] 🤖 AI生成分析
> **详细记录**：AI根据用户要求生成的分析内容
```

### 🔍 内容来源标注系统
- **🤖 AI生成内容** → `[!abstract]` 青色边框
- **👤 用户原创内容** → `[!quote]` 灰色边框
- **💡 使用提示** → `[!tip]` 绿色边框
- **⚠️ 重要注意** → `[!warning]` 黄色边框

## 📚 高级用法

### 可折叠标注块
```markdown
> [!note]- 可折叠的标题
> 这个内容默认是折叠的
> 点击标题可以展开/收起
```

### 嵌套标注块
```markdown
> [!info] 外层信息
> 这是外层内容
> 
> > [!tip] 内层提示
> > 这是嵌套的提示内容
```

### 自定义标题
```markdown
> [!warning] 🚨 自定义警告标题
> 可以在类型后添加自定义标题
```

## 🎨 视觉效果预览

> [!note] 📝 蓝色信息框
> 这是一个标准的信息提示框，用于一般说明

> [!abstract] 🤖 青色摘要框
> 这是AI生成内容的标注框，用于区分AI分析

> [!quote] 👤 灰色引用框
> 这是用户原创内容的标注框，用于标识个人记录

> [!tip] 💡 绿色提示框
> 这是使用技巧和建议的提示框

> [!warning] ⚠️ 黄色警告框
> 这是重要注意事项的警告框

> [!danger] ⛔ 红色危险框
> 这是严重问题的危险警告框

## 🔧 实用技巧

### 1. 快速输入
- 使用Obsidian的模板功能预设常用Callout
- 设置快捷键快速插入常用类型

### 2. 一致性原则
- 同类内容使用相同的Callout类型
- 建立个人的颜色编码系统

### 3. 可读性优化
- 避免在同一页面使用过多不同颜色
- 重要内容使用醒目颜色（红色、黄色）

## 📋 日记模板中的标准用法

在日记的情绪事件记录中：
- **用户填写的四个维度** → `[!quote]` 灰色
- **AI生成的分析内容** → `[!abstract]` 青色
- **使用说明和提示** → `[!tip]` 绿色
- **重要注意事项** → `[!warning]` 黄色

---

**📅 创建时间**：2025-07-23
**🎯 创建目的**：规范Callout使用，提高日记系统的可读性和信息区分度
**🔗 相关文档**：[[智能动态日记模板]]、[[AI深度理解与问题解决方法论]]
