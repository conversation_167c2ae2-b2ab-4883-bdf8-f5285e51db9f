# Calendar插件配置经验

## 🎯 配置目标
配置Calendar插件，实现日记和周记的自动创建

## ⚙️ 基础配置

### **插件安装**
- 设置 → 社区插件 → 搜索"Calendar" → 安装启用

## 📅 Daily Notes配置

### **Daily note format**
```
YYYY-MM-DD
```

### **Daily note template**
```
智能动态日记模板
```

### **Daily note folder**
```
v2.0优化版/01-人工记录输入层/记录界面/日记/YYYY
```

## 📊 Weekly Notes配置

### **Weekly note format**
```
YYYY年第ww周周记
```

### **Weekly note template**
```
智能周记模板
```

### **Weekly note folder**
```
v2.0优化版/01-人工记录输入层/记录界面/周记/YYYY
```

## ✅ 配置验证

### **测试步骤**
1. **测试日记创建**：点击Calendar中的任意日期
2. **测试周记创建**：点击Calendar中的任意一周
3. **验证模板应用**：检查生成的文件是否正确应用模板
4. **验证文件路径**：确认文件保存在正确的文件夹中

### **成功标志**
- ✅ 点击日期自动创建日记文件
- ✅ 点击周数自动创建周记文件
- ✅ 模板内容正确生成
- ✅ 文件保存在指定文件夹

## ⚠️ 常见问题

### **模板不生效**
- **检查模板路径**: 确保模板文件存在
- **检查模板名称**: 不要包含文件扩展名
- **重启Obsidian**: 配置更改后重启

### **文件夹不存在**
- **手动创建文件夹**: 或让Calendar自动创建
- **检查路径格式**: 使用正斜杠 `/`

### **日期格式问题**
- **使用标准格式**: `YYYY-MM-DD` 用于日记
- **使用周数格式**: `YYYY年第ww周周记` 用于周记

## 🚀 最佳实践

### **文件夹组织**
```
记录界面/
├── 日记/
│   ├── 2025/
│   │   ├── 2025-07-14.md
│   │   ├── 2025-07-15.md
│   │   └── 2025-07-16.md
│   └── 2024/
└── 周记/
    ├── 2025/
    │   ├── 2025年第28周周记.md
    │   └── 2025年第29周周记.md
    └── 2024/
```

### **配置建议**
1. **统一命名规范**: 日记和周记使用一致的命名格式
2. **按年份分文件夹**: 便于长期管理
3. **模板路径简化**: 使用相对路径，避免过长路径

---

**最后更新**: 2025-07-16
**状态**: ✅ 配置成功，功能正常
