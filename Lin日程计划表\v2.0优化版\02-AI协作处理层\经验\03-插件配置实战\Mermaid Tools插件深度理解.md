# 📈 Mermaid Tools插件深度理解

## 1️⃣ 插件目的与定位

### 🎯 设计目的
Mermaid Tools是Obsidian生态中的**图表创建增强器**，专门为提升Mermaid.js图表创建体验而设计。它的核心使命是通过可视化工具栏和元素管理系统，将复杂的Mermaid语法转化为直观的点击操作，让用户无需记忆繁琐的语法规则就能快速创建专业的流程图、时序图、甘特图等各类图表，从而大幅提升文档的可视化表达能力。

### 🏗️ 生态定位
- **图表创建加速器**：为Obsidian的原生Mermaid支持提供用户友好的创建界面
- **语法记忆减负器**：通过可视化工具栏减少用户对复杂Mermaid语法的记忆负担
- **图表元素管理器**：提供自定义图表元素的创建、编辑和组织功能
- **可视化文档增强器**：提升文档的图表表达能力和专业视觉效果

## 2️⃣ 实际问题解决场景

### 📝 核心解决的问题

**传统痛点**：
- Mermaid语法复杂，用户需要记忆大量的语法规则和元素格式
- 创建图表时需要频繁查阅文档，影响创作流程的连贯性
- 复杂图表的语法编写容易出错，调试困难
- 缺乏可视化的图表元素管理和复用机制

**Mermaid Tools的系统性解决方案**：

#### 场景1：财务流程图的快速创建（您的核心用例）
```mermaid
flowchart TD
    A[收入来源分析] --> B{收入类型判断}
    B -->|工资收入| C[工资收入记录]
    B -->|投资收益| D[投资收益记录]
    B -->|其他收入| E[其他收入记录]
    
    C --> F[月度收入汇总]
    D --> F
    E --> F
    
    F --> G[预算分配决策]
    G --> H[必要支出]
    G --> I[储蓄投资]
    G --> J[可选支出]
    
    H --> K[支出执行]
    I --> L[投资执行]
    J --> M[消费执行]
    
    K --> N[月度财务回顾]
    L --> N
    M --> N
    
    N --> O{预算执行情况}
    O -->|超支| P[调整下月预算]
    O -->|结余| Q[增加储蓄投资]
    O -->|平衡| R[维持当前策略]
    
    P --> S[财务策略优化]
    Q --> S
    R --> S
    
    S --> T[下月财务计划]
    T --> A
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style N fill:#e8f5e8
    style S fill:#fff3e0
```

**使用Mermaid Tools创建流程**：
1. 点击工具栏中的"流程图开始"元素 → 自动插入 `flowchart TD`
2. 点击"矩形节点"元素 → 插入 `A[收入来源分析]`
3. 点击"菱形决策"元素 → 插入 `B{收入类型判断}`
4. 点击"箭头连接"元素 → 插入 `A --> B`
5. 继续点击相应元素快速构建完整流程图

**实际效果**：
- 将原本需要记忆的复杂语法转化为直观的点击操作
- 大幅减少语法错误和调试时间
- 提高财务流程图创建效率
- 确保图表格式的一致性和专业性

#### 场景2：投资决策的时序图分析
```mermaid
sequenceDiagram
    participant 投资者
    participant 市场分析
    participant 风险评估
    participant 投资决策
    participant 资金管理
    participant 执行交易
    
    投资者->>市场分析: 请求市场数据
    市场分析->>投资者: 返回市场趋势报告
    
    投资者->>风险评估: 提交投资方案
    风险评估->>风险评估: 计算风险指标
    风险评估->>投资者: 返回风险评估结果
    
    alt 风险可接受
        投资者->>投资决策: 制定投资策略
        投资决策->>资金管理: 分配投资资金
        资金管理->>执行交易: 执行买入指令
        执行交易->>投资者: 确认交易完成
    else 风险过高
        投资者->>投资决策: 调整投资方案
        投资决策->>风险评估: 重新评估风险
    end
    
    loop 持续监控
        投资者->>市场分析: 监控投资表现
        市场分析->>投资者: 提供表现报告
        
        alt 需要调整
            投资者->>投资决策: 调整投资组合
            投资决策->>执行交易: 执行调整指令
        end
    end
```

**工具栏快速创建步骤**：
1. 点击"时序图开始" → `sequenceDiagram`
2. 点击"参与者" → `participant 投资者`
3. 点击"消息箭头" → `投资者->>市场分析: 请求市场数据`
4. 点击"条件分支" → `alt 风险可接受`
5. 点击"循环结构" → `loop 持续监控`

**实际效果**：
- 清晰展示投资决策的时间序列和参与方交互
- 通过可视化工具快速构建复杂的时序逻辑
- 便于团队理解投资决策流程
- 支持决策流程的持续优化和调整

#### 场景3：项目管理的甘特图规划
```mermaid
gantt
    title 财务管理系统开发计划
    dateFormat YYYY-MM-DD
    section 需求分析
    需求调研           :done,    des1, 2025-01-01, 2025-01-07
    需求文档编写       :done,    des2, after des1, 5d
    需求评审           :active,  des3, after des2, 2d
    
    section 系统设计
    架构设计           :         des4, after des3, 7d
    数据库设计         :         des5, after des4, 5d
    接口设计           :         des6, after des5, 4d
    
    section 开发实现
    后端开发           :         dev1, after des6, 14d
    前端开发           :         dev2, after des6, 12d
    数据集成           :         dev3, after dev1, 5d
    
    section 测试部署
    单元测试           :         test1, after dev2, 3d
    集成测试           :         test2, after dev3, 4d
    用户测试           :         test3, after test2, 5d
    生产部署           :         deploy1, after test3, 2d
    
    section 运维支持
    系统监控           :         ops1, after deploy1, 30d
    用户培训           :         ops2, after deploy1, 7d
    文档完善           :         ops3, after deploy1, 10d
```

**工具栏辅助创建**：
1. 点击"甘特图开始" → `gantt`
2. 点击"标题设置" → `title 财务管理系统开发计划`
3. 点击"日期格式" → `dateFormat YYYY-MM-DD`
4. 点击"章节标题" → `section 需求分析`
5. 点击"任务项目" → `需求调研 :done, des1, 2025-01-01, 2025-01-07`

**实际效果**：
- 项目时间线的可视化管理和进度追踪
- 任务依赖关系的清晰展示
- 资源分配和时间规划的优化
- 项目风险和瓶颈的提前识别

#### 场景4：数据关系的实体图建模
```mermaid
erDiagram
    USER {
        int user_id PK
        string username
        string email
        string password_hash
        datetime created_at
        datetime updated_at
    }
    
    ACCOUNT {
        int account_id PK
        int user_id FK
        string account_name
        string account_type
        decimal balance
        string currency
        datetime created_at
    }
    
    TRANSACTION {
        int transaction_id PK
        int account_id FK
        int category_id FK
        decimal amount
        string transaction_type
        string description
        datetime transaction_date
        datetime created_at
    }
    
    CATEGORY {
        int category_id PK
        string category_name
        string category_type
        string description
        boolean is_active
    }
    
    BUDGET {
        int budget_id PK
        int user_id FK
        int category_id FK
        decimal budget_amount
        string period_type
        date start_date
        date end_date
        datetime created_at
    }
    
    INVESTMENT {
        int investment_id PK
        int user_id FK
        string investment_type
        string symbol
        decimal quantity
        decimal purchase_price
        decimal current_price
        datetime purchase_date
    }
    
    USER ||--o{ ACCOUNT : "拥有"
    USER ||--o{ BUDGET : "制定"
    USER ||--o{ INVESTMENT : "持有"
    ACCOUNT ||--o{ TRANSACTION : "产生"
    CATEGORY ||--o{ TRANSACTION : "分类"
    CATEGORY ||--o{ BUDGET : "预算"
```

**工具栏快速建模**：
1. 点击"实体图开始" → `erDiagram`
2. 点击"实体定义" → `USER { }`
3. 点击"属性字段" → `int user_id PK`
4. 点击"关系连接" → `USER ||--o{ ACCOUNT : "拥有"`

**实际效果**：
- 财务数据库结构的清晰可视化
- 实体关系和数据流向的直观展示
- 数据库设计的标准化和规范化
- 系统架构理解和维护的便利性

## 3️⃣ 技术实现原理

### 🔧 核心技术架构

**三层服务架构**：
```
用户界面层 (UI Layer)
├── 工具栏视图渲染器 (Toolbar View Renderer)
├── 元素预览组件 (Element Preview Component)
├── 设置面板管理器 (Settings Panel Manager)
└── 快捷操作处理器 (Quick Action Handler)

元素管理层 (Element Management Layer)
├── 元素定义存储器 (Element Definition Storage)
├── 元素分类管理器 (Element Category Manager)
├── 自定义元素编辑器 (Custom Element Editor)
└── 元素导入导出器 (Element Import/Export)

编辑器集成层 (Editor Integration Layer)
├── 光标位置管理器 (Cursor Position Manager)
├── 文本插入处理器 (Text Insertion Handler)
├── 语法验证器 (Syntax Validator)
└── Obsidian API适配器 (Obsidian API Adapter)
```

### 📊 元素定义系统

**元素数据结构**：
```typescript
interface MermaidElement {
    // 基础属性
    id: string;
    name: string;
    category: ElementCategory;
    description: string;
    
    // 显示属性
    icon: string;
    preview: string;        // 在工具栏中的预览内容
    
    // 插入属性
    template: string;       // 插入到编辑器的模板
    cursorOffset: number;   // 插入后光标位置偏移
    
    // 元数据
    isBuiltIn: boolean;
    isCustom: boolean;
    createdAt: Date;
    updatedAt: Date;
    
    // 分组和排序
    order: number;
    tags: string[];
}

enum ElementCategory {
    FLOWCHART = 'flowchart',
    SEQUENCE = 'sequence',
    GANTT = 'gantt',
    CLASS = 'class',
    STATE = 'state',
    ER = 'er',
    JOURNEY = 'journey',
    PIE = 'pie',
    CUSTOM = 'custom'
}

// 内置元素定义示例
const BUILTIN_ELEMENTS: MermaidElement[] = [
    {
        id: 'flowchart-start',
        name: '流程图开始',
        category: ElementCategory.FLOWCHART,
        description: '创建新的流程图',
        icon: '🔄',
        preview: 'flowchart TD\n    A --> B',
        template: 'flowchart TD\n    ',
        cursorOffset: 0,
        isBuiltIn: true,
        isCustom: false,
        order: 1,
        tags: ['flowchart', 'start']
    },
    {
        id: 'flowchart-node-rect',
        name: '矩形节点',
        category: ElementCategory.FLOWCHART,
        description: '添加矩形节点',
        icon: '⬜',
        preview: 'A[节点文本]',
        template: 'A[节点文本]',
        cursorOffset: 2,
        isBuiltIn: true,
        isCustom: false,
        order: 2,
        tags: ['flowchart', 'node', 'rectangle']
    }
];
```

### ⚙️ 工具栏渲染系统

**动态工具栏生成**：
```typescript
class MermaidToolbarRenderer {
    private elements: MermaidElement[] = [];
    private activeCategory: ElementCategory = ElementCategory.FLOWCHART;
    
    // 渲染工具栏视图
    renderToolbar(containerEl: HTMLElement): void {
        containerEl.empty();
        
        // 创建分类选择器
        const categorySelector = this.createCategorySelector(containerEl);
        
        // 创建元素网格
        const elementsGrid = this.createElementsGrid(containerEl);
        
        // 渲染当前分类的元素
        this.renderCategoryElements(elementsGrid);
        
        // 添加自定义元素管理按钮
        this.addCustomElementsButton(containerEl);
    }
    
    // 创建分类选择器
    private createCategorySelector(containerEl: HTMLElement): HTMLElement {
        const selectorEl = containerEl.createDiv('mermaid-category-selector');
        
        Object.values(ElementCategory).forEach(category => {
            const buttonEl = selectorEl.createEl('button', {
                text: this.getCategoryDisplayName(category),
                cls: category === this.activeCategory ? 'active' : ''
            });
            
            buttonEl.addEventListener('click', () => {
                this.switchCategory(category);
            });
        });
        
        return selectorEl;
    }
    
    // 创建元素网格
    private createElementsGrid(containerEl: HTMLElement): HTMLElement {
        return containerEl.createDiv('mermaid-elements-grid');
    }
    
    // 渲染分类元素
    private renderCategoryElements(gridEl: HTMLElement): void {
        gridEl.empty();
        
        const categoryElements = this.elements
            .filter(el => el.category === this.activeCategory)
            .sort((a, b) => a.order - b.order);
        
        categoryElements.forEach(element => {
            this.renderElementCard(gridEl, element);
        });
    }
    
    // 渲染元素卡片
    private renderElementCard(containerEl: HTMLElement, element: MermaidElement): void {
        const cardEl = containerEl.createDiv('mermaid-element-card');
        
        // 元素图标
        const iconEl = cardEl.createDiv('element-icon');
        iconEl.textContent = element.icon;
        
        // 元素名称
        const nameEl = cardEl.createDiv('element-name');
        nameEl.textContent = element.name;
        
        // 元素预览
        const previewEl = cardEl.createDiv('element-preview');
        this.renderMermaidPreview(previewEl, element.preview);
        
        // 点击事件
        cardEl.addEventListener('click', () => {
            this.insertElement(element);
        });
        
        // 右键菜单（自定义元素）
        if (element.isCustom) {
            cardEl.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showElementContextMenu(e, element);
            });
        }
    }
    
    // 渲染Mermaid预览
    private renderMermaidPreview(containerEl: HTMLElement, mermaidCode: string): void {
        const preEl = containerEl.createEl('pre');
        preEl.createEl('code', {
            text: mermaidCode,
            cls: 'language-mermaid'
        });
        
        // 使用Obsidian的Mermaid渲染器
        // 注意：这里简化了实际的渲染逻辑
        if (window.mermaid) {
            window.mermaid.render(`preview-${Date.now()}`, mermaidCode)
                .then(({ svg }) => {
                    containerEl.innerHTML = svg;
                })
                .catch(err => {
                    console.warn('Mermaid preview render failed:', err);
                });
        }
    }
}
```

### 🔄 编辑器集成机制

**智能文本插入**：
```typescript
class EditorIntegration {
    private app: App;
    private plugin: MermaidToolsPlugin;
    
    // 插入元素到编辑器
    async insertElement(element: MermaidElement): Promise<void> {
        const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
        if (!activeView) {
            new Notice('请先打开一个Markdown文档');
            return;
        }
        
        const editor = activeView.editor;
        const cursor = editor.getCursor();
        
        // 检查当前上下文
        const context = this.analyzeInsertionContext(editor, cursor);
        
        // 根据上下文调整插入内容
        const insertText = this.prepareInsertText(element, context);
        
        // 执行插入
        editor.replaceRange(insertText, cursor);
        
        // 调整光标位置
        this.adjustCursorPosition(editor, cursor, element, insertText);
        
        // 触发语法高亮更新
        this.triggerSyntaxHighlight(activeView);
    }
    
    // 分析插入上下文
    private analyzeInsertionContext(editor: Editor, cursor: EditorPosition): InsertionContext {
        const currentLine = editor.getLine(cursor.line);
        const previousLine = cursor.line > 0 ? editor.getLine(cursor.line - 1) : '';
        
        return {
            isInMermaidBlock: this.isInMermaidCodeBlock(editor, cursor),
            isEmptyLine: currentLine.trim() === '',
            isStartOfLine: cursor.ch === 0,
            previousLineContent: previousLine,
            currentLineContent: currentLine,
            indentLevel: this.getIndentLevel(currentLine)
        };
    }
    
    // 检查是否在Mermaid代码块中
    private isInMermaidCodeBlock(editor: Editor, cursor: EditorPosition): boolean {
        let inMermaidBlock = false;
        
        for (let i = 0; i <= cursor.line; i++) {
            const line = editor.getLine(i);
            if (line.startsWith('```mermaid')) {
                inMermaidBlock = true;
            } else if (line.startsWith('```') && inMermaidBlock) {
                inMermaidBlock = false;
            }
        }
        
        return inMermaidBlock;
    }
    
    // 准备插入文本
    private prepareInsertText(element: MermaidElement, context: InsertionContext): string {
        let insertText = element.template;
        
        // 如果不在Mermaid块中，需要创建代码块
        if (!context.isInMermaidBlock) {
            insertText = '```mermaid\n' + insertText + '\n```';
        }
        
        // 处理缩进
        if (!context.isStartOfLine && !context.isEmptyLine) {
            insertText = '\n' + insertText;
        }
        
        // 添加适当的换行
        if (context.isInMermaidBlock && !context.isEmptyLine) {
            insertText = '\n' + insertText;
        }
        
        return insertText;
    }
    
    // 调整光标位置
    private adjustCursorPosition(
        editor: Editor, 
        originalCursor: EditorPosition, 
        element: MermaidElement, 
        insertedText: string
    ): void {
        const lines = insertedText.split('\n');
        const lastLineIndex = originalCursor.line + lines.length - 1;
        const lastLineLength = lines[lines.length - 1].length;
        
        // 计算新的光标位置
        let newCursor: EditorPosition;
        
        if (element.cursorOffset !== undefined) {
            // 使用元素定义的光标偏移
            const offset = element.cursorOffset;
            newCursor = {
                line: lastLineIndex,
                ch: lastLineLength + offset
            };
        } else {
            // 默认放在插入文本的末尾
            newCursor = {
                line: lastLineIndex,
                ch: lastLineLength
            };
        }
        
        editor.setCursor(newCursor);
    }
}
```

### 🎨 自定义元素管理

**元素编辑器界面**：
```typescript
class CustomElementEditor extends Modal {
    private element: MermaidElement;
    private isEditing: boolean;
    private onSave: (element: MermaidElement) => void;
    
    constructor(app: App, element?: MermaidElement, onSave?: (element: MermaidElement) => void) {
        super(app);
        this.element = element || this.createNewElement();
        this.isEditing = !!element;
        this.onSave = onSave || (() => {});
    }
    
    onOpen(): void {
        const { contentEl } = this;
        contentEl.empty();
        
        contentEl.createEl('h2', { text: this.isEditing ? '编辑元素' : '创建新元素' });
        
        // 基础信息表单
        this.createBasicInfoForm(contentEl);
        
        // 模板编辑器
        this.createTemplateEditor(contentEl);
        
        // 预览区域
        this.createPreviewArea(contentEl);
        
        // 操作按钮
        this.createActionButtons(contentEl);
    }
    
    private createBasicInfoForm(containerEl: HTMLElement): void {
        const formEl = containerEl.createDiv('element-form');
        
        // 元素名称
        const nameGroup = formEl.createDiv('form-group');
        nameGroup.createEl('label', { text: '元素名称' });
        const nameInput = nameGroup.createEl('input', {
            type: 'text',
            value: this.element.name
        });
        nameInput.addEventListener('input', (e) => {
            this.element.name = (e.target as HTMLInputElement).value;
        });
        
        // 元素分类
        const categoryGroup = formEl.createDiv('form-group');
        categoryGroup.createEl('label', { text: '元素分类' });
        const categorySelect = categoryGroup.createEl('select');
        Object.values(ElementCategory).forEach(category => {
            const option = categorySelect.createEl('option', {
                value: category,
                text: this.getCategoryDisplayName(category)
            });
            if (category === this.element.category) {
                option.selected = true;
            }
        });
        categorySelect.addEventListener('change', (e) => {
            this.element.category = (e.target as HTMLSelectElement).value as ElementCategory;
        });
        
        // 元素图标
        const iconGroup = formEl.createDiv('form-group');
        iconGroup.createEl('label', { text: '元素图标' });
        const iconInput = iconGroup.createEl('input', {
            type: 'text',
            value: this.element.icon,
            placeholder: '输入emoji或字符'
        });
        iconInput.addEventListener('input', (e) => {
            this.element.icon = (e.target as HTMLInputElement).value;
        });
        
        // 描述
        const descGroup = formEl.createDiv('form-group');
        descGroup.createEl('label', { text: '描述' });
        const descTextarea = descGroup.createEl('textarea', {
            value: this.element.description,
            placeholder: '描述这个元素的用途...'
        });
        descTextarea.addEventListener('input', (e) => {
            this.element.description = (e.target as HTMLTextAreaElement).value;
        });
    }
    
    private createTemplateEditor(containerEl: HTMLElement): void {
        const editorGroup = containerEl.createDiv('template-editor-group');
        editorGroup.createEl('label', { text: 'Mermaid模板' });
        
        const editorEl = editorGroup.createEl('textarea', {
            cls: 'template-editor',
            value: this.element.template,
            placeholder: '输入Mermaid语法模板...'
        });
        
        editorEl.addEventListener('input', (e) => {
            this.element.template = (e.target as HTMLTextAreaElement).value;
            this.updatePreview();
        });
        
        // 添加语法提示
        const hintEl = editorGroup.createDiv('syntax-hint');
        hintEl.innerHTML = `
            <strong>提示：</strong>
            <ul>
                <li>使用标准的Mermaid语法</li>
                <li>可以使用占位符，如 <code>A[节点名称]</code></li>
                <li>光标位置可通过cursorOffset设置</li>
            </ul>
        `;
    }
    
    private createPreviewArea(containerEl: HTMLElement): void {
        const previewGroup = containerEl.createDiv('preview-group');
        previewGroup.createEl('label', { text: '预览效果' });
        
        this.previewEl = previewGroup.createDiv('mermaid-preview');
        this.updatePreview();
    }
    
    private updatePreview(): void {
        if (!this.previewEl) return;
        
        this.previewEl.empty();
        
        if (this.element.template.trim()) {
            // 创建完整的Mermaid图表用于预览
            const fullMermaid = this.createPreviewMermaid(this.element.template);
            
            const preEl = this.previewEl.createEl('pre');
            preEl.createEl('code', {
                text: fullMermaid,
                cls: 'language-mermaid'
            });
            
            // 渲染预览
            if (window.mermaid) {
                window.mermaid.render(`preview-${Date.now()}`, fullMermaid)
                    .then(({ svg }) => {
                        this.previewEl.innerHTML = svg;
                    })
                    .catch(err => {
                        this.previewEl.textContent = '预览渲染失败: ' + err.message;
                    });
            }
        }
    }
}
```

## 4️⃣ 实战效果与社区案例

### 🌟 成功案例概述

**商业流程文档化**：
- **企业流程图**：使用工具栏快速创建业务流程、决策流程和操作规程图表
- **系统架构图**：通过可视化工具构建技术架构、数据流和系统交互图
- **项目管理图表**：创建甘特图、里程碑图和资源分配图表

**教育和培训应用**：
- **课程设计**：教师使用工具创建教学流程图、知识结构图和学习路径图
- **学生笔记**：学生通过简单点击创建概念图、思维导图和学习计划图
- **培训材料**：企业培训师制作流程说明、操作指南和评估流程图

**个人知识管理**：
- **思维整理**：个人用户创建思维导图、决策树和问题分析图
- **项目规划**：使用甘特图和流程图规划个人项目和目标管理
- **学习笔记**：通过图表化方式整理学习内容和知识结构

### 📈 技术影响力

**社区数据**：
- **GitHub Stars**: 254+ (图表工具类插件的重要代表)
- **下载量**: 167k+ 总下载量，广泛使用
- **版本迭代**: 14个版本，持续功能完善
- **社区贡献**: 活跃的用户反馈和功能建议

**生态集成**：
- 完全基于Obsidian原生Mermaid支持，无渲染冲突
- 与各种主题完美兼容，保持视觉一致性
- 支持自定义元素的导入导出，便于团队协作
- 与其他图表插件形成互补，提供完整的可视化解决方案

### 🔗 相关资源链接

**官方资源**：
- [GitHub仓库](https://github.com/dartungar/obsidian-mermaid)
- [Obsidian插件市场](https://obsidian.md/plugins?id=mermaid-tools)
- [版本发布记录](https://github.com/dartungar/obsidian-mermaid/releases)

**作者信息**：
- [dartungar](https://github.com/dartungar) - 插件开发者和维护者

**Mermaid.js资源**：
- [Mermaid.js官方网站](https://mermaid.js.org/)
- [Mermaid语法文档](https://mermaid.js.org/intro/syntax-reference.html)
- [Mermaid在线编辑器](https://mermaid.live/)
- [Mermaid图表类型大全](https://mermaid.js.org/intro/#diagram-types)

**学习资源**：
- [Mermaid图表设计最佳实践](https://mermaid.js.org/config/theming.html)
- [商业流程图设计指南](https://www.lucidchart.com/pages/flowchart)
- [数据可视化原则](https://www.tableau.com/learn/articles/data-visualization)

**技术文档**：
- [Mermaid.js API文档](https://mermaid.js.org/config/setup/README.html)
- [图表主题定制](https://mermaid.js.org/config/theming.html)
- [Obsidian插件开发指南](https://docs.obsidian.md/Plugins/Getting+started/Build+a+plugin)

---

## 📝 维护说明

**版本信息**：当前版本 1.2.2 (稳定版本)
**维护状态**：活跃维护，定期发布功能更新和用户体验改进
**兼容性**：支持Obsidian最新版本，与原生Mermaid功能完全兼容
**扩展性**：支持自定义元素创建、编辑和管理，高度可配置
