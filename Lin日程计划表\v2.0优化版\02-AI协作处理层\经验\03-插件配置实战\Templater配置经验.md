# Templater配置经验

## 🎯 配置目标
配置Templater插件，实现日记和周记的动态模板生成

## ⚙️ 基础配置

### **插件安装**
- 设置 → 社区插件 → 搜索"Templater" → 安装启用

### **模板文件夹设置**
- **Template folder location**: `v2.0优化版/01-人工记录输入层/Obsidian模板库`
- **Automatic jump to cursor**: 启用
- **Enable Templater**: 启用

## 🔧 关键语法经验

### **日期相关语法**
```javascript
// 标准日期格式（用于数据查询）
{{date:YYYY-MM-DD}}

// 中文显示格式
{{date:YYYY年MM月DD日 dddd}}

// 周数和星期
{{date:w}}    // 周数
{{date:d}}    // 星期数字
```

### **tp.date.weekday语法**
```javascript
// 正确语法：format在前，weekday_number在后
<% tp.date.weekday("YYYY-MM-DD", 0) %>        // 本周一
<% tp.date.weekday("YYYY-MM-DD", 6) %>        // 本周日

// 错误语法（避免）
<% tp.date.weekday(tp.date.now(), 1, "YYYY-MM-DD") %>
```

### **weekday参数说明**
- `0` = 本周一
- `1` = 本周二
- `2` = 本周三
- `3` = 本周四
- `4` = 本周五
- `5` = 本周六
- `6` = 本周日
- `7` = 下周一
- `-7` = 上周一

## ⚠️ 常见错误

### **语法错误**
1. **错误**: `<%* tR += ... %>`
   **正确**: `<% ... %>`

2. **错误**: 参数顺序错误
   **正确**: format参数在前，数字参数在后

### **配置错误**
1. **模板路径错误**: 确保路径正确且文件存在
2. **权限问题**: 确保Templater有文件访问权限

## ✅ 成功案例

### **周记自动链接生成**
```markdown
**本周日记链接**：
- [[<% tp.date.weekday("YYYY-MM-DD", 0) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 0) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 1) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 1) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 2) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 2) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 3) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 3) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 4) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 4) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 5) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 5) %>]]
- [[<% tp.date.weekday("YYYY-MM-DD", 6) %>|<% tp.date.weekday("YYYY年MM月DD日 dddd", 6) %>]]
```

### **智能运动安排**
```javascript
<%*
const dayNum = parseInt(tp.date.now("d"));
if (dayNum === 1) {
    tR += `**周一训练**:
- [ ] 热身 #exercise
- [ ] 胸部训练 #exercise
- [ ] 背部训练 #exercise
- [ ] 拉伸 #exercise`;
} else if (dayNum === 3) {
    tR += `**周三训练**:
- [ ] 热身 #exercise
- [ ] 肩膀训练 #exercise
- [ ] 腹部训练 #exercise
- [ ] 拉伸 #exercise`;
}
%>
```

## 🚀 最佳实践

### **模板设计原则**
1. **标准化日期格式**: 统一使用 `YYYY-MM-DD`
2. **分离显示和数据**: `date` 用于查询，`display_date` 用于显示
3. **简化逻辑**: 避免复杂的计算，优先使用简单可靠的方案

### **调试技巧**
1. **逐步测试**: 先测试简单语法，再组合复杂功能
2. **查看官方文档**: 遇到问题先查官方文档
3. **保留工作版本**: 修改前备份可用的模板

---

**最后更新**: 2025-07-16
**状态**: ✅ 核心功能稳定
