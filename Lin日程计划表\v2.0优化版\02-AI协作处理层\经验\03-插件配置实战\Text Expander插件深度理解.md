# 🔄 Text Expander插件深度理解

## 1️⃣ 插件目的与定位

### 🎯 设计目的
Text Expander是Obsidian生态中的**智能文本扩展和内容聚合引擎**，专门为基于搜索的动态内容生成和文本快速扩展而设计。它的核心使命是将Obsidian的强大搜索功能与模板引擎相结合，让用户能够通过简单的搜索查询自动生成复杂的内容列表、索引和报告，实现从静态笔记到动态内容聚合的转换，大幅提升内容组织和信息整合的效率。

### 🏗️ 生态定位
- **动态内容聚合核心**：为Obsidian提供基于搜索的智能内容聚合和生成功能
- **模板驱动引擎**：通过强大的模板系统实现内容的自定义格式化和展示
- **搜索结果处理器**：将搜索结果转化为结构化的文档内容和索引
- **知识库自动化工具**：实现知识库内容的自动更新和维护

## 2️⃣ 实际问题解决场景

### 📝 核心解决的问题

**传统痛点**：
- 手动创建和维护内容索引工作量大，容易遗漏和过时
- 搜索结果无法直接转化为结构化的文档内容
- 重复性的内容聚合任务缺乏自动化手段
- 动态内容更新需要大量手工操作，效率低下

**Text Expander的系统性解决方案**：

#### 场景1：学术研究的文献索引自动生成
```markdown
# 机器学习研究文献索引

## 监督学习相关文献

```expander
tag:#机器学习 AND tag:#监督学习
^## 监督学习文献列表
- **[[$filename]]** - $frontmatter:author ($frontmatter:year)
  - 摘要：$frontmatter:abstract
  - 关键词：$frontmatter:keywords
  - 引用次数：$frontmatter:citations
>共找到 $count 篇相关文献
```

## 深度学习算法研究

```expander
path:"深度学习/" AND (CNN OR RNN OR Transformer)
^## 深度学习算法文献
|标题|作者|发表年份|核心算法|影响因子|
|---|---|---|---|---|
|[[$filename]]|$frontmatter:author|$frontmatter:year|$frontmatter:algorithm|$frontmatter:impact_factor|
>总计：$count 篇深度学习算法研究
```

## 最新研究动态

```expander
created:7d AND tag:#机器学习
^## 本周新增研究文献
<% it.files.forEach(file => { %>
### <%= file.frontmatter.title || file.basename %>
- **作者**：<%= file.frontmatter.author %>
- **发表时间**：<%= file.frontmatter.date %>
- **研究方向**：<%= file.frontmatter.field %>
- **核心贡献**：<%= file.frontmatter.contribution %>
- **链接**：<%= file.link %>

**摘要**：
<%= file.frontmatter.abstract %>

**关键发现**：
<% if (file.frontmatter.findings) { %>
<% file.frontmatter.findings.forEach(finding => { %>
- <%= finding %>
<% }) %>
<% } %>

---
<% }) %>
>本周共新增 <%= it.files.length %> 篇研究文献
```

## 研究领域分布统计

```expander
tag:#机器学习
^## 研究领域分布
<% 
const fieldCount = {};
it.files.forEach(file => {
  const field = file.frontmatter.field || '未分类';
  fieldCount[field] = (fieldCount[field] || 0) + 1;
});
%>
| 研究领域 | 文献数量 | 占比 |
|---------|---------|------|
<% Object.entries(fieldCount).sort((a, b) => b[1] - a[1]).forEach(([field, count]) => { %>
| <%= field %> | <%= count %> | <%= (count / it.files.length * 100).toFixed(1) %>% |
<% }) %>
>总计：<%= it.files.length %> 篇文献，涵盖 <%= Object.keys(fieldCount).length %> 个研究领域
```
```

**实际效果**：
- 自动生成和维护文献索引，无需手动更新
- 多维度的文献分类和统计分析
- 动态追踪最新研究动态和趋势
- 结构化展示研究成果和核心信息

#### 场景2：项目管理的状态报告自动生成
```markdown
# 项目管理状态报告

## 进行中的项目

```expander
tag:#项目 AND status:进行中
^## 🚀 当前活跃项目
<% it.files.forEach(file => { %>
### <%= file.frontmatter.project_name || file.basename %>
- **项目经理**：<%= file.frontmatter.manager %>
- **开始时间**：<%= file.frontmatter.start_date %>
- **预计完成**：<%= file.frontmatter.due_date %>
- **当前进度**：<%= file.frontmatter.progress %>%
- **项目状态**：<%= file.frontmatter.status %>
- **团队规模**：<%= file.frontmatter.team_size %> 人

**项目描述**：
<%= file.frontmatter.description %>

**当前里程碑**：
<% if (file.frontmatter.current_milestone) { %>
- <%= file.frontmatter.current_milestone %>
<% } %>

**风险提醒**：
<% if (file.frontmatter.risks && file.frontmatter.risks.length > 0) { %>
<% file.frontmatter.risks.forEach(risk => { %>
- ⚠️ <%= risk %>
<% }) %>
<% } else { %>
- ✅ 暂无重大风险
<% } %>

**下周计划**：
<% if (file.frontmatter.next_week_plan) { %>
<% file.frontmatter.next_week_plan.forEach(plan => { %>
- [ ] <%= plan %>
<% }) %>
<% } %>

---
<% }) %>
>当前共有 <%= it.files.length %> 个活跃项目
```

## 项目风险监控

```expander
tag:#项目 AND (risk:高 OR status:延期)
^## ⚠️ 高风险项目监控
| 项目名称 | 项目经理 | 风险等级 | 主要风险 | 应对措施 |
|---------|---------|---------|---------|---------|
<% it.files.forEach(file => { %>
| [[$<%= file.basename %>]] | <%= file.frontmatter.manager %> | <%= file.frontmatter.risk_level %> | <%= file.frontmatter.main_risk %> | <%= file.frontmatter.mitigation %> |
<% }) %>
>需要重点关注的项目：<%= it.files.length %> 个
```

## 团队工作负载分析

```expander
tag:#项目 AND status:进行中
^## 👥 团队工作负载分析
<% 
const teamLoad = {};
it.files.forEach(file => {
  if (file.frontmatter.team_members) {
    file.frontmatter.team_members.forEach(member => {
      if (!teamLoad[member]) {
        teamLoad[member] = { projects: 0, totalHours: 0 };
      }
      teamLoad[member].projects += 1;
      teamLoad[member].totalHours += file.frontmatter.estimated_hours || 0;
    });
  }
});
%>
| 团队成员 | 参与项目数 | 预计工时 | 负载状态 |
|---------|-----------|---------|---------|
<% Object.entries(teamLoad).sort((a, b) => b[1].totalHours - a[1].totalHours).forEach(([member, load]) => { %>
<% const status = load.totalHours > 160 ? '🔴 超负荷' : load.totalHours > 120 ? '🟡 高负载' : '🟢 正常'; %>
| <%= member %> | <%= load.projects %> | <%= load.totalHours %>h | <%= status %> |
<% }) %>
>团队总人数：<%= Object.keys(teamLoad).length %> 人
```

## 项目完成情况统计

```expander
tag:#项目
^## 📊 项目完成情况统计
<% 
const statusCount = {};
it.files.forEach(file => {
  const status = file.frontmatter.status || '未知';
  statusCount[status] = (statusCount[status] || 0) + 1;
});
const total = it.files.length;
%>
| 项目状态 | 数量 | 占比 | 进度条 |
|---------|------|------|-------|
<% Object.entries(statusCount).forEach(([status, count]) => { %>
<% const percentage = (count / total * 100).toFixed(1); %>
<% const barLength = Math.round(percentage / 5); %>
<% const bar = '█'.repeat(barLength) + '░'.repeat(20 - barLength); %>
| <%= status %> | <%= count %> | <%= percentage %>% | <%= bar %> |
<% }) %>
>项目总数：<%= total %> 个
```
```

**实际效果**：
- 实时生成项目状态报告，无需手动统计
- 自动识别高风险项目和资源瓶颈
- 团队工作负载的可视化分析
- 项目进度的动态追踪和预警

#### 场景3：知识库内容的主题索引生成
```markdown
# 知识库主题索引

## 技术文档索引

```expander
path:"技术文档/" AND -tag:#草稿
^## 💻 技术文档目录
<% 
const categories = {};
it.files.forEach(file => {
  const category = file.frontmatter.category || '其他';
  if (!categories[category]) {
    categories[category] = [];
  }
  categories[category].push(file);
});
%>
<% Object.entries(categories).sort().forEach(([category, files]) => { %>
### <%= category %>
<% files.sort((a, b) => a.basename.localeCompare(b.basename)).forEach(file => { %>
- [[$<%= file.basename %>]] 
  - 📅 更新时间：<%= new Date(file.stat.mtime).toLocaleDateString() %>
  - 🏷️ 标签：<%= file.frontmatter.tags ? file.frontmatter.tags.join(', ') : '无' %>
  - 📝 描述：<%= file.frontmatter.description || '暂无描述' %>
<% }) %>

<% }) %>
>技术文档总数：<%= it.files.length %> 篇，分为 <%= Object.keys(categories).length %> 个类别
```

## 学习笔记汇总

```expander
tag:#学习笔记 AND created:30d
^## 📚 最近学习笔记汇总
<% 
const subjects = {};
it.files.forEach(file => {
  const subject = file.frontmatter.subject || '通用';
  if (!subjects[subject]) {
    subjects[subject] = [];
  }
  subjects[subject].push(file);
});
%>
<% Object.entries(subjects).forEach(([subject, files]) => { %>
### <%= subject %> (<%= files.length %> 篇)
<% files.sort((a, b) => b.stat.ctime - a.stat.ctime).forEach(file => { %>
- **[[$<%= file.basename %>]]** 
  - 学习日期：<%= new Date(file.stat.ctime).toLocaleDateString() %>
  - 难度等级：<%= file.frontmatter.difficulty || '未评级' %>
  - 掌握程度：<%= file.frontmatter.mastery || '未评估' %>
  - 学习时长：<%= file.frontmatter.study_time || '未记录' %>
  <% if (file.frontmatter.key_points) { %>
  - 关键要点：
    <% file.frontmatter.key_points.forEach(point => { %>
    - <%= point %>
    <% }) %>
  <% } %>
<% }) %>

<% }) %>
>本月学习笔记：<%= it.files.length %> 篇，涉及 <%= Object.keys(subjects).length %> 个学科
```

## 待办事项汇总

```expander
tag:#待办 OR contains:"- [ ]"
^## ✅ 待办事项汇总
<% 
let todoCount = 0;
let completedCount = 0;
%>
<% it.files.forEach(file => { %>
### <%= file.basename %>
<% if (file.content) { %>
<% const todos = file.content.match(/- \[ \] .+/g) || []; %>
<% const completed = file.content.match(/- \[x\] .+/g) || []; %>
<% todoCount += todos.length; %>
<% completedCount += completed.length; %>
<% if (todos.length > 0) { %>
**待完成任务** (<%= todos.length %> 项)：
<% todos.forEach(todo => { %>
<%= todo %>
<% }) %>
<% } %>
<% if (completed.length > 0) { %>
**已完成任务** (<%= completed.length %> 项)：
<% completed.slice(0, 3).forEach(todo => { %>
<%= todo %>
<% }) %>
<% if (completed.length > 3) { %>
... 还有 <%= completed.length - 3 %> 项已完成
<% } %>
<% } %>
<% } %>
---
<% }) %>
>待办统计：未完成 <%= todoCount %> 项，已完成 <%= completedCount %> 项，完成率 <%= todoCount + completedCount > 0 ? (completedCount / (todoCount + completedCount) * 100).toFixed(1) : 0 %>%
```

## 标签使用统计

```expander
tag:#
^## 🏷️ 标签使用统计
<% 
const tagCount = {};
it.files.forEach(file => {
  if (file.frontmatter.tags) {
    file.frontmatter.tags.forEach(tag => {
      tagCount[tag] = (tagCount[tag] || 0) + 1;
    });
  }
});
%>
| 标签 | 使用次数 | 相关文档 |
|------|---------|---------|
<% Object.entries(tagCount).sort((a, b) => b[1] - a[1]).slice(0, 20).forEach(([tag, count]) => { %>
| #<%= tag %> | <%= count %> | [查看相关文档](obsidian://search?query=tag%3A%23<%= tag %>) |
<% }) %>
>标签总数：<%= Object.keys(tagCount).length %> 个，显示使用频率前20个
```
```

**实际效果**：
- 自动生成和维护知识库的主题索引
- 多维度的内容分类和统计分析
- 学习进度和待办事项的动态追踪
- 标签使用情况的可视化统计

#### 场景4：内容创作的素材库管理
```markdown
# 内容创作素材库

## 创作灵感收集

```expander
tag:#灵感 AND created:7d
^## 💡 本周新增创作灵感
<% it.files.forEach(file => { %>
### <%= file.frontmatter.title || file.basename %>
- **记录时间**：<%= new Date(file.stat.ctime).toLocaleString() %>
- **灵感来源**：<%= file.frontmatter.source || '未记录' %>
- **创作方向**：<%= file.frontmatter.direction || '待确定' %>
- **优先级**：<%= file.frontmatter.priority || '中等' %>

**核心想法**：
<%= file.frontmatter.core_idea || '待完善' %>

**可能的角度**：
<% if (file.frontmatter.angles) { %>
<% file.frontmatter.angles.forEach(angle => { %>
- <%= angle %>
<% }) %>
<% } %>

**相关资源**：
<% if (file.frontmatter.resources) { %>
<% file.frontmatter.resources.forEach(resource => { %>
- <%= resource %>
<% }) %>
<% } %>

---
<% }) %>
>本周新增灵感：<%= it.files.length %> 个
```

## 写作素材分类

```expander
tag:#素材
^## 📝 写作素材分类整理
<% 
const materialTypes = {};
it.files.forEach(file => {
  const type = file.frontmatter.material_type || '其他';
  if (!materialTypes[type]) {
    materialTypes[type] = [];
  }
  materialTypes[type].push(file);
});
%>
<% Object.entries(materialTypes).forEach(([type, materials]) => { %>
### <%= type %> (<%= materials.length %> 项)
<% materials.sort((a, b) => b.stat.mtime - a.stat.mtime).forEach(material => { %>
- **[[$<%= material.basename %>]]**
  - 素材类型：<%= material.frontmatter.material_type %>
  - 适用主题：<%= material.frontmatter.topics ? material.frontmatter.topics.join(', ') : '通用' %>
  - 质量评级：<%= material.frontmatter.quality || '未评级' %>
  - 使用状态：<%= material.frontmatter.used ? '已使用' : '待使用' %>
  <% if (material.frontmatter.description) { %>
  - 描述：<%= material.frontmatter.description %>
  <% } %>
<% }) %>

<% }) %>
>素材总数：<%= it.files.length %> 项，分为 <%= Object.keys(materialTypes).length %> 个类型
```

## 文章发布统计

```expander
tag:#已发布 AND created:30d
^## 📊 本月发布统计
<% 
const platformStats = {};
let totalViews = 0;
let totalLikes = 0;
%>
<% it.files.forEach(file => { %>
<% if (file.frontmatter.platforms) { %>
<% file.frontmatter.platforms.forEach(platform => { %>
<% if (!platformStats[platform]) platformStats[platform] = { count: 0, views: 0, likes: 0 }; %>
<% platformStats[platform].count += 1; %>
<% platformStats[platform].views += file.frontmatter.views || 0; %>
<% platformStats[platform].likes += file.frontmatter.likes || 0; %>
<% }) %>
<% } %>
<% totalViews += file.frontmatter.views || 0; %>
<% totalLikes += file.frontmatter.likes || 0; %>
<% }) %>

### 发布平台统计
| 平台 | 文章数 | 总阅读量 | 总点赞数 | 平均阅读量 |
|------|-------|---------|---------|-----------|
<% Object.entries(platformStats).forEach(([platform, stats]) => { %>
| <%= platform %> | <%= stats.count %> | <%= stats.views %> | <%= stats.likes %> | <%= stats.count > 0 ? Math.round(stats.views / stats.count) : 0 %> |
<% }) %>

### 热门文章排行
<% it.files.sort((a, b) => (b.frontmatter.views || 0) - (a.frontmatter.views || 0)).slice(0, 5).forEach((file, index) => { %>
<%= index + 1 %>. **[[$<%= file.basename %>]]** - <%= file.frontmatter.views || 0 %> 阅读，<%= file.frontmatter.likes || 0 %> 点赞
<% }) %>

>本月发布：<%= it.files.length %> 篇文章，总阅读量：<%= totalViews %>，总点赞数：<%= totalLikes %>
```

## 创作计划跟踪

```expander
tag:#创作计划
^## 📅 创作计划执行情况
<% 
let plannedCount = 0;
let completedCount = 0;
let inProgressCount = 0;
%>
<% it.files.forEach(file => { %>
### <%= file.frontmatter.plan_name || file.basename %>
- **计划周期**：<%= file.frontmatter.period %>
- **目标数量**：<%= file.frontmatter.target_count %> 篇
- **已完成**：<%= file.frontmatter.completed_count || 0 %> 篇
- **进行中**：<%= file.frontmatter.in_progress_count || 0 %> 篇
- **完成率**：<%= file.frontmatter.target_count > 0 ? ((file.frontmatter.completed_count || 0) / file.frontmatter.target_count * 100).toFixed(1) : 0 %>%

<% plannedCount += file.frontmatter.target_count || 0; %>
<% completedCount += file.frontmatter.completed_count || 0; %>
<% inProgressCount += file.frontmatter.in_progress_count || 0; %>

**计划详情**：
<% if (file.frontmatter.plan_details) { %>
<% file.frontmatter.plan_details.forEach(detail => { %>
- <%= detail %>
<% }) %>
<% } %>

---
<% }) %>
>总体进度：计划 <%= plannedCount %> 篇，已完成 <%= completedCount %> 篇，进行中 <%= inProgressCount %> 篇，整体完成率 <%= plannedCount > 0 ? (completedCount / plannedCount * 100).toFixed(1) : 0 %>%
```
```

**实际效果**：
- 创作灵感的系统化收集和管理
- 写作素材的分类整理和质量评估
- 发布数据的统计分析和效果追踪
- 创作计划的执行监控和进度管理

## 3️⃣ 技术实现原理

### 🔧 核心技术架构

**四层处理架构**：
```
搜索集成层 (Search Integration Layer)
├── 搜索查询解析器 (Search Query Parser)
├── 搜索结果提取器 (Search Result Extractor)
├── 搜索上下文管理器 (Search Context Manager)
└── 搜索延迟控制器 (Search Delay Controller)

模板引擎层 (Template Engine Layer)
├── ETA模板处理器 (ETA Template Processor)
├── 序列模板处理器 (Sequence Template Processor)
├── 模板语法解析器 (Template Syntax Parser)
└── 模板变量注入器 (Template Variable Injector)

内容生成层 (Content Generation Layer)
├── 文件信息提取器 (File Info Extractor)
├── 元数据处理器 (Metadata Processor)
├── 内容格式化器 (Content Formatter)
└── 结果聚合器 (Result Aggregator)

用户界面层 (User Interface Layer)
├── 命令处理器 (Command Handler)
├── 代码块识别器 (Code Block Recognizer)
├── 进度指示器 (Progress Indicator)
└── 错误处理器 (Error Handler)
```

### 📊 搜索集成系统

**搜索查询处理**：
```typescript
class SearchQueryProcessor {
    private app: App;
    private searchDelay: number;
    
    constructor(app: App, settings: TextExpanderSettings) {
        this.app = app;
        this.searchDelay = settings.delay || 100;
    }
    
    async executeSearch(query: string): Promise<SearchResult[]> {
        if (!query.trim()) {
            // 如果查询为空，使用当前搜索面板的结果
            return this.getCurrentSearchResults();
        }
        
        // 执行搜索查询
        await this.performSearch(query);
        
        // 等待搜索完成
        await this.waitForSearchCompletion();
        
        // 提取搜索结果
        return this.extractSearchResults();
    }
    
    private async performSearch(query: string): Promise<void> {
        // 获取搜索插件实例
        const searchPlugin = this.app.internalPlugins.plugins.search;
        if (!searchPlugin) {
            throw new Error('Search plugin is not available');
        }
        
        // 执行搜索
        const searchInstance = searchPlugin.instance;
        searchInstance.openGlobalSearch(query);
    }
    
    private async waitForSearchCompletion(): Promise<void> {
        // 等待指定的延迟时间
        await new Promise(resolve => setTimeout(resolve, this.searchDelay));
    }
    
    private async getCurrentSearchResults(): Promise<SearchResult[]> {
        const searchPlugin = this.app.internalPlugins.plugins.search;
        if (!searchPlugin) {
            return [];
        }
        
        const searchInstance = searchPlugin.instance;
        const searchResults = searchInstance.dom?.resultDomLookup;
        
        if (!searchResults) {
            return [];
        }
        
        const results: SearchResult[] = [];
        
        for (const [file, resultInfo] of searchResults) {
            if (file instanceof TFile) {
                const searchResult = await this.createSearchResult(file, resultInfo);
                results.push(searchResult);
            }
        }
        
        return results;
    }
    
    private async extractSearchResults(): Promise<SearchResult[]> {
        // 从搜索面板DOM中提取结果
        const searchLeaf = this.app.workspace.getLeavesOfType('search')[0];
        if (!searchLeaf) {
            return [];
        }
        
        const searchView = searchLeaf.view as any;
        const resultContainer = searchView.dom?.resultContainer;
        
        if (!resultContainer) {
            return [];
        }
        
        const results: SearchResult[] = [];
        const resultElements = resultContainer.querySelectorAll('.search-result-file-title');
        
        for (const element of resultElements) {
            const filePath = element.getAttribute('data-path');
            if (filePath) {
                const file = this.app.vault.getAbstractFileByPath(filePath);
                if (file instanceof TFile) {
                    const searchResult = await this.createSearchResultFromElement(file, element);
                    results.push(searchResult);
                }
            }
        }
        
        return results;
    }
    
    private async createSearchResult(file: TFile, resultInfo: any): Promise<SearchResult> {
        const content = await this.app.vault.read(file);
        const frontmatter = this.app.metadataCache.getFileCache(file)?.frontmatter || {};
        const links = this.extractLinks(content);
        const headings = this.extractHeadings(content);
        const sections = this.extractSections(content);
        const listItems = this.extractListItems(content);
        
        return {
            basename: file.basename,
            name: file.name,
            content: content,
            extension: file.extension,
            link: this.generateLink(file),
            path: file.path,
            frontmatter: frontmatter,
            stat: file.stat,
            links: links,
            headings: headings,
            sections: sections,
            listItems: listItems,
            searchContext: this.extractSearchContext(content, resultInfo)
        };
    }
    
    private async createSearchResultFromElement(file: TFile, element: Element): Promise<SearchResult> {
        const content = await this.app.vault.read(file);
        const frontmatter = this.app.metadataCache.getFileCache(file)?.frontmatter || {};
        
        // 从DOM元素中提取搜索上下文
        const contextElement = element.parentElement?.querySelector('.search-result-file-matches');
        const searchContext = contextElement ? this.extractContextFromDOM(contextElement) : '';
        
        return {
            basename: file.basename,
            name: file.name,
            content: content,
            extension: file.extension,
            link: this.generateLink(file),
            path: file.path,
            frontmatter: frontmatter,
            stat: file.stat,
            links: this.extractLinks(content),
            headings: this.extractHeadings(content),
            sections: this.extractSections(content),
            listItems: this.extractListItems(content),
            searchContext: searchContext
        };
    }
    
    private generateLink(file: TFile): string {
        // 根据Obsidian设置生成Wiki链接或Markdown链接
        const useWikiLinks = this.app.vault.getConfig('useMarkdownLinks') === false;
        
        if (useWikiLinks) {
            return `[[${file.basename}]]`;
        } else {
            return `[${file.basename}](${file.path})`;
        }
    }
    
    private extractLinks(content: string): string[] {
        const linkPattern = /\[\[([^\]]+)\]\]/g;
        const links: string[] = [];
        let match;
        
        while ((match = linkPattern.exec(content)) !== null) {
            links.push(match[1]);
        }
        
        return links;
    }
    
    private extractHeadings(content: string): Heading[] {
        const lines = content.split('\n');
        const headings: Heading[] = [];
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const match = line.match(/^(#{1,6})\s+(.+)$/);
            
            if (match) {
                headings.push({
                    level: match[1].length,
                    text: match[2],
                    line: i,
                    link: `[[${this.sanitizeHeadingForLink(match[2])}]]`
                });
            }
        }
        
        return headings;
    }
    
    private extractSections(content: string): Section[] {
        const lines = content.split('\n');
        const sections: Section[] = [];
        let currentSection: Section | null = null;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
            
            if (headingMatch) {
                // 结束当前section
                if (currentSection) {
                    currentSection.endLine = i - 1;
                    currentSection.content = lines.slice(currentSection.startLine, i).join('\n');
                    sections.push(currentSection);
                }
                
                // 开始新的section
                currentSection = {
                    heading: headingMatch[2],
                    level: headingMatch[1].length,
                    startLine: i,
                    endLine: lines.length - 1,
                    content: ''
                };
            }
        }
        
        // 添加最后一个section
        if (currentSection) {
            currentSection.content = lines.slice(currentSection.startLine).join('\n');
            sections.push(currentSection);
        }
        
        return sections;
    }
    
    private extractListItems(content: string): ListItem[] {
        const lines = content.split('\n');
        const listItems: ListItem[] = [];
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const match = line.match(/^(\s*)([-*+]|\d+\.)\s+(.+)$/);
            
            if (match) {
                listItems.push({
                    text: match[3],
                    indent: match[1].length,
                    marker: match[2],
                    line: i,
                    isTask: /^\s*[-*+]\s+\[[ x]\]/.test(line),
                    isCompleted: /^\s*[-*+]\s+\[x\]/.test(line)
                });
            }
        }
        
        return listItems;
    }
    
    private extractSearchContext(content: string, resultInfo: any): string {
        // 从搜索结果信息中提取上下文
        if (resultInfo && resultInfo.matches) {
            return resultInfo.matches.map((match: any) => match.context || '').join('\n');
        }
        
        return '';
    }
    
    private extractContextFromDOM(contextElement: Element): string {
        const matches = contextElement.querySelectorAll('.search-result-file-match');
        return Array.from(matches).map(match => match.textContent || '').join('\n');
    }
    
    private sanitizeHeadingForLink(heading: string): string {
        // 清理标题文本以生成有效的链接
        return heading.replace(/[^\w\s-]/g, '').trim();
    }
}

// 搜索结果类型定义
interface SearchResult {
    basename: string;
    name: string;
    content: string;
    extension: string;
    link: string;
    path: string;
    frontmatter: Record<string, any>;
    stat: FileStats;
    links: string[];
    headings: Heading[];
    sections: Section[];
    listItems: ListItem[];
    searchContext: string;
}

interface Heading {
    level: number;
    text: string;
    line: number;
    link: string;
}

interface Section {
    heading: string;
    level: number;
    startLine: number;
    endLine: number;
    content: string;
}

interface ListItem {
    text: string;
    indent: number;
    marker: string;
    line: number;
    isTask: boolean;
    isCompleted: boolean;
}
```

### ⚙️ 模板引擎系统

**双模板引擎支持**：
```typescript
class TemplateEngineManager {
    private etaEngine: EtaTemplateEngine;
    private sequenceEngine: SequenceTemplateEngine;
    
    constructor() {
        this.etaEngine = new EtaTemplateEngine();
        this.sequenceEngine = new SequenceTemplateEngine();
    }
    
    async processTemplate(
        template: string, 
        searchResults: SearchResult[], 
        currentFile: TFile
    ): Promise<string> {
        // 检测模板类型
        const templateType = this.detectTemplateType(template);
        
        switch (templateType) {
            case 'eta':
                return this.etaEngine.process(template, searchResults, currentFile);
            case 'sequence':
                return this.sequenceEngine.process(template, searchResults, currentFile);
            default:
                // 默认使用序列模板引擎
                return this.sequenceEngine.process(template, searchResults, currentFile);
        }
    }
    
    private detectTemplateType(template: string): 'eta' | 'sequence' {
        // 检测ETA模板语法
        if (template.includes('<%') && template.includes('%>')) {
            return 'eta';
        }
        
        // 检测序列模板语法
        if (template.includes('$filename') || template.includes('^') || template.includes('>')) {
            return 'sequence';
        }
        
        return 'sequence'; // 默认
    }
}

// ETA模板引擎
class EtaTemplateEngine {
    async process(
        template: string, 
        searchResults: SearchResult[], 
        currentFile: TFile
    ): Promise<string> {
        try {
            // 动态导入eta库
            const { Eta } = await import('eta');
            const eta = new Eta();
            
            // 准备模板数据
            const templateData = {
                current: await this.prepareCurrentFileData(currentFile),
                files: searchResults,
                count: searchResults.length
            };
            
            // 渲染模板
            const result = eta.render(template, templateData);
            return result;
            
        } catch (error) {
            console.error('ETA template processing failed:', error);
            throw new Error(`Template processing failed: ${error.message}`);
        }
    }
    
    private async prepareCurrentFileData(file: TFile): Promise<any> {
        if (!file) {
            return {};
        }
        
        const content = await this.app.vault.read(file);
        const frontmatter = this.app.metadataCache.getFileCache(file)?.frontmatter || {};
        
        return {
            basename: file.basename,
            name: file.name,
            content: content,
            extension: file.extension,
            path: file.path,
            frontmatter: frontmatter,
            stat: file.stat
        };
    }
}

// 序列模板引擎（传统模式）
class SequenceTemplateEngine {
    private sequenceProcessors = new Map<string, SequenceProcessor>();
    
    constructor() {
        this.registerSequenceProcessors();
    }
    
    private registerSequenceProcessors(): void {
        this.sequenceProcessors.set('$filename', new FilenameProcessor());
        this.sequenceProcessors.set('$link', new LinkProcessor());
        this.sequenceProcessors.set('$searchresult', new SearchResultProcessor());
        this.sequenceProcessors.set('$matchline', new MatchLineProcessor());
        this.sequenceProcessors.set('$lines', new LinesProcessor());
        this.sequenceProcessors.set('$ext', new ExtensionProcessor());
        this.sequenceProcessors.set('$created', new CreatedProcessor());
        this.sequenceProcessors.set('$size', new SizeProcessor());
        this.sequenceProcessors.set('$parent', new ParentProcessor());
        this.sequenceProcessors.set('$path', new PathProcessor());
        this.sequenceProcessors.set('$frontmatter', new FrontmatterProcessor());
        this.sequenceProcessors.set('$header', new HeaderProcessor());
        this.sequenceProcessors.set('$blocks', new BlocksProcessor());
    }
    
    async process(
        template: string, 
        searchResults: SearchResult[], 
        currentFile: TFile
    ): Promise<string> {
        const lines = template.split('\n');
        const processedLines: string[] = [];
        
        let header = '';
        let footer = '';
        let bodyTemplate = '';
        
        // 解析模板结构
        for (const line of lines) {
            if (line.startsWith('^')) {
                header += line.substring(1) + '\n';
            } else if (line.startsWith('>')) {
                footer += line.substring(1) + '\n';
            } else {
                bodyTemplate += line + '\n';
            }
        }
        
        // 处理头部
        if (header) {
            processedLines.push(header.trim());
        }
        
        // 处理每个搜索结果
        for (const result of searchResults) {
            const processedBody = await this.processBodyTemplate(bodyTemplate, result);
            processedLines.push(processedBody);
        }
        
        // 处理尾部
        if (footer) {
            const processedFooter = footer.replace('$count', searchResults.length.toString());
            processedLines.push(processedFooter.trim());
        }
        
        return processedLines.join('\n');
    }
    
    private async processBodyTemplate(template: string, result: SearchResult): Promise<string> {
        let processed = template;
        
        // 处理所有序列
        for (const [sequence, processor] of this.sequenceProcessors) {
            const regex = new RegExp(`\\${sequence}(?::(\\w+))?(?::(\\w+))?(?::(\\w+))?`, 'g');
            
            processed = processed.replace(regex, (match, param1, param2, param3) => {
                return processor.process(result, param1, param2, param3);
            });
        }
        
        return processed.trim();
    }
}

// 序列处理器基类
abstract class SequenceProcessor {
    abstract process(result: SearchResult, ...params: string[]): string;
}

// 文件名处理器
class FilenameProcessor extends SequenceProcessor {
    process(result: SearchResult): string {
        return result.basename;
    }
}

// 链接处理器
class LinkProcessor extends SequenceProcessor {
    process(result: SearchResult): string {
        return result.link;
    }
}

// 搜索结果处理器
class SearchResultProcessor extends SequenceProcessor {
    process(result: SearchResult): string {
        return result.searchContext || '';
    }
}

// 匹配行处理器
class MatchLineProcessor extends SequenceProcessor {
    process(result: SearchResult, contextLines?: string, limitChars?: string): string {
        const lines = result.content.split('\n');
        const searchContext = result.searchContext;
        
        if (!searchContext) {
            return '';
        }
        
        // 查找包含搜索内容的行
        const matchingLines: string[] = [];
        const contextCount = contextLines ? parseInt(contextLines) : 0;
        const charLimit = limitChars ? parseInt(limitChars) : 0;
        
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].toLowerCase().includes(searchContext.toLowerCase())) {
                const startLine = Math.max(0, i - contextCount);
                const endLine = Math.min(lines.length - 1, i + contextCount);
                
                for (let j = startLine; j <= endLine; j++) {
                    let line = lines[j];
                    if (charLimit > 0 && line.length > charLimit) {
                        line = line.substring(0, charLimit) + '...';
                    }
                    matchingLines.push(line);
                }
                break;
            }
        }
        
        return matchingLines.join('\n');
    }
}

// 行内容处理器
class LinesProcessor extends SequenceProcessor {
    process(result: SearchResult, lineCount?: string): string {
        if (!lineCount) {
            return result.content;
        }
        
        const count = parseInt(lineCount);
        const lines = result.content.split('\n');
        
        return lines.slice(0, count).join('\n');
    }
}

// 前置元数据处理器
class FrontmatterProcessor extends SequenceProcessor {
    process(result: SearchResult, fieldName?: string): string {
        if (!fieldName) {
            return JSON.stringify(result.frontmatter, null, 2);
        }
        
        const value = result.frontmatter[fieldName];
        return value ? String(value) : '';
    }
}

// 标题处理器
class HeaderProcessor extends SequenceProcessor {
    process(result: SearchResult, level?: string, specificHeader?: string): string {
        const headings = result.headings;
        
        if (specificHeader) {
            // 查找特定标题
            const heading = headings.find(h => 
                h.text.toLowerCase().includes(specificHeader.toLowerCase())
            );
            return heading ? heading.link : '';
        }
        
        if (level) {
            // 过滤特定级别的标题
            const levelNum = level.length; // ## = 2, ### = 3, etc.
            const filteredHeadings = headings.filter(h => h.level === levelNum);
            return filteredHeadings.map(h => h.link).join('\n');
        }
        
        // 返回所有标题
        return headings.map(h => h.link).join('\n');
    }
}

// 块引用处理器
class BlocksProcessor extends SequenceProcessor {
    process(result: SearchResult): string {
        // 提取块引用
        const blockPattern = /\^([a-zA-Z0-9-]+)/g;
        const blocks: string[] = [];
        let match;
        
        while ((match = blockPattern.exec(result.content)) !== null) {
            blocks.push(`[[${result.basename}#^${match[1]}]]`);
        }
        
        return blocks.join('\n');
    }
}
```

## 4️⃣ 实战效果与社区案例

### 🌟 成功案例概述

**学术研究应用**：
- **研究生和学者**：自动生成文献索引和研究报告，提升研究效率
- **图书馆员和信息管理员**：创建动态的资源目录和主题索引
- **科研团队**：建立自动化的研究进展追踪和成果汇总系统

**企业知识管理**：
- **项目经理**：自动生成项目状态报告和团队工作负载分析
- **技术团队**：创建动态的技术文档索引和API文档汇总
- **内容团队**：建立自动化的内容创作素材库和发布统计系统

**个人知识工作者**：
- **知识管理爱好者**：实现知识库的自动化索引和主题分类
- **内容创作者**：建立创作素材的动态管理和发布追踪系统
- **学习者**：创建自动化的学习进度追踪和知识点汇总

### 📈 技术影响力

**社区数据**：
- **GitHub Stars**: 187+ (文本扩展类插件的代表作)
- **下载量**: 33k+ 总下载量，专业用户群体
- **版本迭代**: 38个版本，持续功能完善
- **社区贡献**: 10个贡献者，稳定的开源生态

**生态集成**：
- 与Obsidian搜索功能深度集成，提供原生级别的体验
- 支持ETA和序列两种模板引擎，满足不同用户需求
- 为动态内容生成提供标准化的解决方案
- 建立了搜索驱动的内容聚合最佳实践

### 🔗 相关资源链接

**官方资源**：
- [GitHub仓库](https://github.com/mrjackphil/obsidian-text-expand)
- [使用文档](https://github.com/mrjackphil/obsidian-text-expand#how-to-use)
- [模板引擎指南](https://github.com/mrjackphil/obsidian-text-expand#template-engines)

**作者信息**：
- [MrJackphil (mrjackphil)](https://github.com/mrjackphil) - 乌克兰软件开发者，自动化工具专家

**社区资源**：
- [GitHub Issues](https://github.com/mrjackphil/obsidian-text-expand/issues)
- [Obsidian论坛讨论](https://forum.obsidian.md/search?q=text%20expand)
- [用户案例分享](https://www.reddit.com/r/ObsidianMD/search/?q=text%20expander)

**学习资源**：
- [ETA模板引擎文档](https://eta.js.org/)
- [搜索语法指南](https://help.obsidian.md/Plugins/Search)
- [模板最佳实践](https://github.com/mrjackphil/obsidian-text-expand#special-sequences)

**技术文档**：
- [序列语法参考](https://github.com/mrjackphil/obsidian-text-expand#special-sequences)
- [配置选项](https://github.com/mrjackphil/obsidian-text-expand#settings)
- [高级用法示例](https://github.com/mrjackphil/obsidian-text-expand#template-engines)

---

## 📝 维护说明

**版本信息**：当前版本 0.11.5 (稳定版本)
**维护状态**：稳定维护，定期发布功能更新和兼容性修复
**兼容性**：支持Obsidian最新版本，与搜索功能完美集成
**扩展性**：支持自定义模板和序列处理器，高度可配置
