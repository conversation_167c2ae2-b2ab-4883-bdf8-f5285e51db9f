# QuickAdd下拉菜单实现模板

## 🎯 使用场景
当需要在Obsidian中创建带下拉菜单选择的快捷输入功能时使用此模板。

## 📋 完整实现步骤

### 步骤1：创建JavaScript脚本

**文件位置**：`QuickAdd脚本/[功能名].js`

**基础模板**：
```javascript
// [功能描述] - QuickAdd脚本
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 第一步：下拉菜单选择
        const selectedOption = await quickAddApi.suggester(
            ["🎯 选项1", "📝 选项2", "💡 选项3"], // 显示文本
            ["option1", "option2", "option3"]     // 返回值
        );
        
        if (!selectedOption) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第二步：文本输入
        const userInput = await quickAddApi.inputPrompt("💭 请输入内容:");
        if (!userInput) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第三步：获取当前时间
        const currentTime = new Date().toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // 第四步：构建记录
        const record = `| ${currentTime} | ${selectedOption} | ${userInput} |`;
        
        // 第五步：智能插入到指定位置
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            new Notice("❌ 请先打开一个文件！");
            return;
        }
        
        let content = await app.vault.read(activeFile);
        
        // 查找插入位置的模式
        const patterns = [
            // 模式1：完整表格
            /### 📊 [标题]\s*\n[\s\S]*?\| --- \| ---- \| --- \|/,
            // 模式2：只有标题
            /### 📊 [标题]/
        ];
        
        let insertSuccess = false;
        
        for (let i = 0; i < patterns.length; i++) {
            const pattern = patterns[i];
            const match = content.match(pattern);
            
            if (match) {
                if (i === 0) {
                    // 找到完整表格，在分隔线后插入
                    const insertPosition = match.index + match[0].length;
                    const newContent = content.slice(0, insertPosition) + '\n' + record + content.slice(insertPosition);
                    await app.vault.modify(activeFile, newContent);
                    insertSuccess = true;
                    break;
                } else if (i === 1) {
                    // 只找到标题，创建完整表格
                    const tableContent = `\n\n| 时间 | 类型 | 内容 |\n| --- | ---- | --- |\n${record}`;
                    const insertPosition = match.index + match[0].length;
                    const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
                    await app.vault.modify(activeFile, newContent);
                    insertSuccess = true;
                    break;
                }
            }
        }
        
        if (!insertSuccess) {
            // 如果找不到指定部分，在文件末尾添加
            const appendContent = `\n\n### 📊 [标题]\n\n| 时间 | 类型 | 内容 |\n| --- | ---- | --- |\n${record}`;
            await app.vault.modify(activeFile, content + appendContent);
            new Notice(`✅ 已在文件末尾创建新表格：${selectedOption}`);
        } else {
            new Notice(`✅ 已记录：${selectedOption} - ${userInput}`);
        }
        
    } catch (error) {
        console.error("[功能名]脚本错误:", error);
        new Notice(`❌ 错误：${error.message}`);
    }
};
```

### 步骤2：配置QuickAdd

**文件位置**：`.obsidian/plugins/quickadd/data.json`

**添加到choices数组**：
```json
{
  "id": "[功能名]-choice-001",
  "name": "🎯 [显示名称]",
  "type": "Macro",
  "command": true,
  "macroId": "[功能名]-macro-001"
}
```

**添加到macros数组**：
```json
{
  "name": "[功能名]宏",
  "id": "[功能名]-macro-001",
  "commands": [
    {
      "name": "[功能名].js",
      "type": "UserScript",
      "id": "[功能名]-script-001",
      "path": "QuickAdd脚本/[功能名].js",
      "settings": {}
    }
  ],
  "runOnStartup": false
}
```

### 步骤3：配置Commander按钮

**文件位置**：`.obsidian/plugins/cmdr/data.json`

**添加到对应位置数组**（如rightSidebar）：
```json
{
  "name": "🎯 [显示名称]",
  "type": "command",
  "id": "quickadd:choice:[功能名]-choice-001",
  "icon": "[图标名称]"
}
```

## 🔧 自定义配置要点

### 1. 下拉菜单选项
```javascript
const options = await quickAddApi.suggester(
    ["🎯 显示文本1", "📝 显示文本2"],  // 用户看到的
    ["value1", "value2"]              // 实际返回的值
);
```

### 2. 多级下拉菜单
```javascript
// 第一级选择
const category = await quickAddApi.suggester(
    ["📊 工作", "🏠 生活", "📚 学习"],
    ["work", "life", "study"]
);

// 根据第一级选择显示第二级
let subcategories;
if (category === "work") {
    subcategories = ["会议", "项目", "邮件"];
} else if (category === "life") {
    subcategories = ["购物", "运动", "娱乐"];
}

const subcategory = await quickAddApi.suggester(subcategories, subcategories);
```

### 3. 表格插入位置自定义
```javascript
// 修改查找模式
const patterns = [
    /### 📊 你的标题\s*\n[\s\S]*?\| --- \| ---- \|/,
    /### 📊 你的标题/
];

// 修改表格结构
const tableContent = `\n\n| 列1 | 列2 | 列3 |\n| --- | --- | --- |\n${record}`;
```

### 4. 记录格式自定义
```javascript
// 简单列表格式
const record = `- **${currentTime}** ${selectedOption}: ${userInput}`;

// 表格格式
const record = `| ${currentTime} | ${selectedOption} | ${userInput} | ${note} |`;

// 带标签格式
const record = `- #${selectedOption} **${currentTime}** ${userInput}`;
```

## 📝 常用API参考

### 输入方法
```javascript
// 单行输入
const input = await quickAddApi.inputPrompt("提示文本", "占位符", "默认值");

// 多行输入
const input = await quickAddApi.wideInputPrompt("提示文本", "占位符");

// 确认对话框
const confirmed = await quickAddApi.yesNoPrompt("标题", "说明文本");

// 多选框
const selected = await quickAddApi.checkboxPrompt(
    ["选项1", "选项2", "选项3"],
    ["预选项1"]  // 可选的预选项
);
```

### 时间格式
```javascript
// 当前时间
const now = quickAddApi.date.now("YYYY-MM-DD HH:mm:ss");

// 明天
const tomorrow = quickAddApi.date.tomorrow("YYYY-MM-DD");

// 自定义偏移
const nextWeek = quickAddApi.date.now("YYYY-MM-DD", 7);
```

## 🎯 使用此模板的步骤

1. **复制模板代码**
2. **替换所有`[功能名]`为你的功能名称**
3. **替换所有`[显示名称]`为按钮显示文本**
4. **修改下拉菜单选项**
5. **调整表格结构和插入位置**
6. **保存文件并重启Obsidian**
7. **测试功能**

---

**模板版本**：v1.0
**适用场景**：需要下拉菜单选择的QuickAdd功能
**最后更新**：2025-07-18
