# 📚 经验知识库总览

## 🎯 知识库目的

本经验知识库是财务管理系统开发过程中积累的核心经验和方法论，旨在为AI协作和Obsidian系统开发提供权威的技术指导和实践经验。

## 📁 知识库结构

### 01-AI协作方法论 🤖
**核心内容**：AI深度理解与协作的方法论体系
- `AI深度理解与问题解决方法论.md` - AI协作的核心原则和框架
- `AI深度网络搜索方法论.md` - 高效信息搜索的策略体系

**适用场景**：
- 需要与AI进行深度协作时
- 遇到复杂技术问题需要系统性解决时
- 建立高效的问题解决工作流时

### 02-Obsidian软件理解 📖
**核心内容**：Obsidian软件的框架式理解和使用指南
- `Obsidian软件理解说明.md` - 软件架构、插件理解、布局设计的完整框架
- `Obsidian标注块(Callout)使用指南.md` - Callout语法和应用场景详解

**适用场景**：
- 初次接触Obsidian软件时
- 需要深度理解软件机制时
- 设计基于Obsidian的系统架构时

### 03-插件配置实战 ⚙️
**核心内容**：插件配置的实战经验和通用方法
- `Obsidian插件配置通用经验.md` - 插件配置的通用原则和模式
- `QuickAdd插件深度配置经验.md` - QuickAdd插件的深度配置实战

**适用场景**：
- 配置复杂插件功能时
- 遇到插件配置问题时
- 需要自定义插件行为时

### 04-问题排查记录 🔍
**核心内容**：具体问题的排查过程和解决方案
- `Obsidian右侧边栏真相调查弹出一个呃。.md` - 右侧边栏按钮显示问题的深度调查

**适用场景**：
- 遇到类似的界面显示问题时
- 需要深度排查系统问题时
- 学习问题排查思路和方法时

### 05-使用指南模板 📋
**核心内容**：可复用的使用指南和实现模板
- `QuickAdd下拉菜单实现模板.md` - 下拉菜单功能的完整实现模板
- `如何找到右侧边栏按钮.md` - 用户友好的操作指南

**适用场景**：
- 需要快速实现类似功能时
- 为用户编写操作指南时
- 建立标准化的实现模板时

## 🎯 使用建议

### 📖 学习路径
1. **新手入门**：先阅读`02-Obsidian软件理解`建立基础认知
2. **方法论学习**：学习`01-AI协作方法论`提升协作效率
3. **实战应用**：参考`03-插件配置实战`进行具体配置
4. **问题解决**：遇到问题时查阅`04-问题排查记录`
5. **快速实现**：使用`05-使用指南模板`加速开发

### 🔄 维护原则
- **及时更新**：新的经验和发现及时记录
- **分类清晰**：按照既定结构进行分类存放
- **质量优先**：确保每个文档都有实际价值
- **关联性强**：文档间建立适当的链接关系

### 🎯 应用价值
- **提升效率**：避免重复踩坑，快速解决问题
- **知识沉淀**：将实践经验转化为可复用的知识资产
- **协作优化**：为AI协作提供清晰的指导框架
- **系统思维**：建立系统性的问题解决思路

---

## 📝 维护说明

**创建时间**：2025-07-23
**维护责任**：定期更新和完善知识库内容
**版本控制**：重要更新需要记录变更历史

此知识库是财务管理系统开发的重要资产，需要持续维护和完善。
