# 📋 生态系统数据项说明文档

> **目的**：为AI系统提供清晰的数据格式规范，确保数据提取和处理的准确性

## 🎯 **早晨习惯系统数据项**

### **📊 任务状态字段 (Boolean类型)**

| 字段名 | 数据类型 | 说明 | 示例值 |
|--------|----------|------|--------|
| `task_1_wakeup` | boolean | 起床醒神任务完成状态 | `true` / `false` |
| `task_2_hygiene` | boolean | 刷牙洗脸任务完成状态 | `true` / `false` |
| `task_3_water` | boolean | 喝水补充任务完成状态 | `true` / `false` |
| `task_4_hiit1` | boolean | HIIT动作1(开合跳)完成状态 | `true` / `false` |
| `task_5_hiit2` | boolean | HIIT动作2(高抬腿)完成状态 | `true` / `false` |
| `task_6_hiit3` | boolean | HIIT动作3(波比跳)完成状态 | `true` / `false` |
| `task_7_hiit4` | boolean | HIIT动作4(平板支撑)完成状态 | `true` / `false` |
| `task_8_hiit5` | boolean | HIIT动作5(深蹲)完成状态 | `true` / `false` |
| `task_9_hiit6` | boolean | HIIT动作6(俯卧撑)完成状态 | `true` / `false` |
| `task_10_hiit7` | boolean | HIIT动作7(登山者)完成状态 | `true` / `false` |
| `task_11_hiit8` | boolean | HIIT动作8(卷腹)完成状态 | `true` / `false` |
| `task_12_bike` | boolean | 单车运动任务完成状态 | `true` / `false` |
| `task_13_health` | boolean | 健康记录任务完成状态 | `true` / `false` |
| `task_14_prepare` | boolean | 装水整理任务完成状态 | `true` / `false` |
| `task_15_breakfast` | boolean | 早餐安排任务完成状态 | `true` / `false` |
| `task_16_final` | boolean | 最终准备任务完成状态 | `true` / `false` |

### **⏰ 时间记录字段 (String类型)**

| 字段名 | 数据类型 | 说明 | 示例值 |
|--------|----------|------|--------|
| `actual_time_1` | string | 起床醒神实际用时(分钟) | `"5"` / `"3"` |
| `actual_time_2` | string | 刷牙洗脸实际用时(分钟) | `"4"` / `"6"` |
| `actual_time_3` | string | 喝水补充实际用时(分钟) | `"3"` / `"2"` |
| `actual_time_4` | string | HIIT动作1实际用时(分钟) | `"2"` / `"3"` |
| `actual_time_5` | string | HIIT动作2实际用时(分钟) | `"2"` / `"3"` |
| `actual_time_6` | string | HIIT动作3实际用时(分钟) | `"2"` / `"4"` |
| `actual_time_7` | string | HIIT动作4实际用时(分钟) | `"2"` / `"3"` |
| `actual_time_8` | string | HIIT动作5实际用时(分钟) | `"2"` / `"2"` |
| `actual_time_9` | string | HIIT动作6实际用时(分钟) | `"2"` / `"4"` |
| `actual_time_10` | string | HIIT动作7实际用时(分钟) | `"2"` / `"3"` |
| `actual_time_11` | string | HIIT动作8实际用时(分钟) | `"2"` / `"2"` |
| `actual_time_12` | string | 单车运动实际用时(分钟) | `"15"` / `"18"` |
| `actual_time_13` | string | 健康记录实际用时(分钟) | `"4"` / `"5"` |
| `actual_time_14` | string | 装水整理实际用时(分钟) | `"5"` / `"3"` |
| `actual_time_15` | string | 早餐安排实际用时(分钟) | `"3"` / `"4"` |
| `actual_time_16` | string | 最终准备实际用时(分钟) | `"2"` / `"3"` |

### **📅 基础信息字段**

| 字段名 | 数据类型 | 说明 | 示例值 |
|--------|----------|------|--------|
| `date` | string | 记录日期 | `"2025-01-28"` |
| `display_date` | string | 显示用日期 | `"2025年01月28日 星期二"` |
| `created` | string | 创建时间戳 | `"2025-01-28T06:30:00"` |
| `week` | string | 周数 | `"4"` |
| `weekday` | string | 星期几 | `"2"` |
| `template_version` | string | 模板版本 | `"2.0-极简版"` |

## 🔢 **计算字段规范**

### **📊 统计计算方法**

```javascript
// 完成任务数计算
const completedTasks = [
  task_1_wakeup, task_2_hygiene, task_3_water, task_4_hiit1,
  task_5_hiit2, task_6_hiit3, task_7_hiit4, task_8_hiit5,
  task_9_hiit6, task_10_hiit7, task_11_hiit8, task_12_bike,
  task_13_health, task_14_prepare, task_15_breakfast, task_16_final
].filter(task => task === true).length;

// 完成率计算
const completionRate = Math.round((completedTasks / 16) * 100);

// 总用时计算
const totalTime = [
  actual_time_1, actual_time_2, actual_time_3, actual_time_4,
  actual_time_5, actual_time_6, actual_time_7, actual_time_8,
  actual_time_9, actual_time_10, actual_time_11, actual_time_12,
  actual_time_13, actual_time_14, actual_time_15, actual_time_16
].reduce((sum, time) => sum + (parseInt(time) || 0), 0);

// 效率指数计算
const efficiency = totalTime > 0 ? Math.round((completedTasks / totalTime) * 60) : 0;
```

## 📤 **数据传输格式**

### **JSON数据结构**

```json
{
  "date": "2025-01-28",
  "type": "morning_routine",
  "completedTasks": 16,
  "totalTasks": 16,
  "completionRate": 100,
  "totalTime": 60,
  "efficiency": 16,
  "taskDetails": [
    {"taskId": "1", "name": "wakeup", "completed": true},
    {"taskId": "2", "name": "hygiene", "completed": true},
    // ... 其他任务
  ],
  "timeDetails": [
    {"taskId": "1", "actualTime": "5"},
    {"taskId": "2", "actualTime": "4"},
    // ... 其他时间记录
  ],
  "timestamp": "2025-01-28T06:30:00.000Z"
}
```

## 🤖 **AI读取指南**

### **数据提取步骤**

1. **读取frontmatter**：
   ```javascript
   const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
   const frontmatterMatch = content.match(frontmatterRegex);
   ```

2. **提取任务状态**：
   ```javascript
   const taskPattern = /task_(\d+)_\w+:\s*(true|false)/g;
   ```

3. **提取时间数据**：
   ```javascript
   const timePattern = /actual_time_(\d+):\s*"?([^"\n]*)"?/g;
   ```

4. **计算统计数据**：
   - 完成任务数：`filter(task => task === true).length`
   - 完成率：`(completedTasks / 16) * 100`
   - 总用时：`reduce((sum, time) => sum + parseInt(time))`

### **状态判断标准**

| 完成率范围 | 状态等级 | 表情符号 | 评价文本 |
|------------|----------|----------|----------|
| 90-100% | 优秀 | 🏆 | 完美执行！ |
| 75-89% | 良好 | 🌟 | 表现优秀！ |
| 50-74% | 一般 | 💪 | 继续加油！ |
| 0-49% | 需要努力 | 🔥 | 需要努力！ |

## 🔄 **数据流转路径**

```
📝 日记模板 (数据输入)
    ↓
📊 实时计算 (Dataview处理)
    ↓
📤 数据传输 (Templater脚本)
    ↓
🗃️ 数据汇总 (JSON格式存储)
    ↓
🎮 个人仪表板 (可视化展示)
    ↓
🤖 AI教练系统 (智能分析)
```

## 📋 **使用说明**

### **对于AI系统**
1. 使用此文档作为数据解析的标准规范
2. 严格按照字段名和数据类型进行提取
3. 使用提供的计算公式确保统计准确性
4. 遵循状态判断标准进行评价

### **对于开发者**
1. 新增字段时必须更新此文档
2. 修改计算逻辑时同步更新公式
3. 确保数据类型的一致性
4. 维护JSON格式的向后兼容性

---

**📅 创建时间**: 2025-01-28  
**🔄 最后更新**: 2025-01-28  
**📝 维护者**: 生态系统架构层  
**🎯 用途**: AI数据提取规范
