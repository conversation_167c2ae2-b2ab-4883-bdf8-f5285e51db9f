# 🎨 CSS隐藏YAML Frontmatter 使用说明

## 📋 功能概述

`hide-yaml-frontmatter.css` 是一个智能CSS代码片段，用于在Obsidian中隐藏YAML frontmatter，同时保持所有插件功能正常工作。

## 🔧 启用步骤

### 1. 打开CSS代码片段设置
1. 打开Obsidian设置（`Ctrl+,` 或 `Cmd+,`）
2. 点击左侧菜单中的 **"外观"**
3. 滚动到 **"CSS代码片段"** 部分

### 2. 启用代码片段
1. 在CSS代码片段列表中找到 `hide-yaml-frontmatter`
2. 点击右侧的开关按钮启用它
3. 代码片段启用后，开关会变成蓝色

### 3. 验证效果
1. 打开任何包含YAML frontmatter的文件
2. 检查顶部的YAML是否已被隐藏
3. 测试meta-bind按钮是否仍然正常工作

## ✅ 功能特性

### 🎯 **智能隐藏**
- ✅ 在所有查看模式下隐藏YAML frontmatter
- ✅ 支持源码模式、实时预览模式、预览模式
- ✅ 兼容新旧版本的Obsidian Properties系统

### 🔌 **插件兼容**
- ✅ meta-bind插件按钮功能正常
- ✅ Daily Notes Editor插件兼容
- ✅ 其他依赖frontmatter的插件正常工作

### 📱 **全平台支持**
- ✅ 桌面版Obsidian
- ✅ 移动端Obsidian
- ✅ 各种主题兼容

## 🔄 与现有CSS的协调

### auto-collapse-properties.css
如果您之前启用了 `auto-collapse-properties.css`，新的隐藏CSS会覆盖折叠行为，直接隐藏Properties。

**建议操作**：
- 禁用 `auto-collapse-properties.css`
- 启用 `hide-yaml-frontmatter.css`

## 🛠️ 故障排除

### 问题1：YAML仍然可见
**可能原因**：
- CSS代码片段未正确启用
- 浏览器缓存问题
- 主题冲突

**解决方案**：
1. 确认CSS代码片段已启用（开关为蓝色）
2. 重启Obsidian
3. 尝试禁用其他CSS代码片段测试冲突

### 问题2：meta-bind按钮不工作
**可能原因**：
- 插件冲突
- YAML语法错误

**解决方案**：
1. 检查meta-bind插件是否正常启用
2. 临时禁用隐藏CSS，检查YAML语法
3. 查看开发者控制台的错误信息

### 问题3：在某些模式下仍显示
**可能原因**：
- 特殊的编辑器模式
- 第三方插件的特殊视图

**解决方案**：
1. 切换到实时预览模式测试
2. 检查是否有其他插件影响显示
3. 联系开发者报告特殊情况

## 🎛️ 高级配置

### 临时显示YAML（调试模式）
如果需要临时显示YAML进行调试，可以：

1. **方法1**：临时禁用CSS代码片段
2. **方法2**：在CSS文件末尾取消注释调试代码
3. **方法3**：使用Toggle Meta Yaml插件快速切换

### 自定义隐藏规则
您可以编辑 `hide-yaml-frontmatter.css` 文件来自定义隐藏行为：

```css
/* 示例：只在特定文件夹隐藏 */
.workspace-leaf-content[data-path*="日记"] .metadata-container {
    display: none !important;
}

/* 示例：悬停时显示 */
.metadata-container {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.metadata-container:hover {
    opacity: 1;
}
```

## 📊 技术细节

### CSS选择器说明
- `.metadata-container`：新版Obsidian的YAML容器
- `.frontmatter-container`：旧版Obsidian的YAML容器
- `.metadata-properties`：Properties系统容器
- `!important`：确保规则优先级最高

### 兼容性测试
- ✅ Obsidian v1.0+
- ✅ Windows/macOS/Linux
- ✅ 移动端iOS/Android
- ✅ 主流社区主题

## 📞 支持与反馈

如果遇到问题或有改进建议，请：
1. 检查本文档的故障排除部分
2. 在项目中创建issue报告问题
3. 提供详细的环境信息和错误描述

---

**版本信息**：
- 文件版本：v1.0
- 创建时间：2025-07-31
- 最后更新：2025-07-31
- 兼容性：Obsidian v1.0+
