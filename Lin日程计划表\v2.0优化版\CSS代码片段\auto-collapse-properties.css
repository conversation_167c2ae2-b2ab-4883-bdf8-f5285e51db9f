/* 自动折叠Properties区域的CSS代码片段 */
/* Auto-collapse Properties section */

/* 默认折叠Properties */
.metadata-properties {
    --metadata-properties-collapsed: 1;
}

/* 折叠时只显示标题 */
.metadata-properties[data-collapsed="true"] .metadata-properties-heading {
    display: block;
}

.metadata-properties[data-collapsed="true"] .metadata-property {
    display: none;
}

/* 悬停时显示折叠/展开按钮 */
.metadata-properties-heading:hover .metadata-properties-title::after {
    content: " (点击展开)";
    font-size: 0.8em;
    opacity: 0.6;
}

/* 美化Properties标题 */
.metadata-properties-heading {
    background: var(--background-secondary);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
    cursor: pointer;
}

/* 折叠状态下的样式 */
.metadata-properties[data-collapsed="true"] {
    margin-bottom: 1em;
}

.metadata-properties[data-collapsed="true"] .metadata-properties-heading {
    border-bottom: none;
    margin-bottom: 0;
}
