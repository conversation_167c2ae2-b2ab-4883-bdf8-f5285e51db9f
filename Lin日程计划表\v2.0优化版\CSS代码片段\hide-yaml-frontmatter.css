/* 智能隐藏YAML Frontmatter - 方案2实现 */
/* Smart Hide YAML Frontmatter - Solution 2 Implementation */

/* 
 * 功能说明：
 * - 在所有查看模式下隐藏YAML frontmatter
 * - 兼容新旧版本的Obsidian
 * - 不影响meta-bind等插件的后台功能
 * - 支持Properties和传统frontmatter
 */

/* =================================== */
/* 主要隐藏规则 - Main Hiding Rules */
/* =================================== */

/* 隐藏源码模式下的YAML frontmatter */
.markdown-source-view .metadata-container {
    display: none !important;
}

/* 隐藏实时预览模式下的YAML frontmatter */
.markdown-reading-view .metadata-container {
    display: none !important;
}

/* 隐藏预览模式下的传统frontmatter */
.markdown-preview-view .frontmatter-container {
    display: none !important;
}

/* 隐藏实时预览模式下的传统frontmatter */
.markdown-source-view .frontmatter-container {
    display: none !important;
}

/* =================================== */
/* Properties系统兼容 - Properties Compatibility */
/* =================================== */

/* 隐藏新版Properties面板 - 与auto-collapse-properties.css协调 */
/* 注意：如果启用了auto-collapse-properties.css，此规则优先级更高 */
.metadata-properties {
    display: none !important;
}

/* 隐藏Properties编辑器 */
.metadata-properties-editor {
    display: none !important;
}

/* 隐藏Properties标题栏 */
.metadata-properties-heading {
    display: none !important;
}

/* 覆盖auto-collapse-properties.css的折叠行为 */
.metadata-properties[data-collapsed="true"],
.metadata-properties[data-collapsed="false"] {
    display: none !important;
}

/* =================================== */
/* 特殊情况处理 - Special Cases */
/* =================================== */

/* 确保在编辑模式下也隐藏 */
.cm-editor .metadata-container {
    display: none !important;
}

/* 隐藏Properties的添加按钮 */
.metadata-add-button {
    display: none !important;
}

/* 隐藏Properties的折叠按钮 */
.metadata-properties-title {
    display: none !important;
}

/* =================================== */
/* 移动端兼容 - Mobile Compatibility */
/* =================================== */

/* 移动端隐藏规则 */
.is-mobile .metadata-container,
.is-mobile .frontmatter-container {
    display: none !important;
}

/* =================================== */
/* 插件兼容性 - Plugin Compatibility */
/* =================================== */

/* 确保Daily Notes Editor插件兼容 */
.daily-note-view .metadata-container {
    display: none !important;
}

/* 确保其他编辑器插件兼容 */
.workspace-leaf-content[data-type="markdown"] .metadata-container {
    display: none !important;
}

/* =================================== */
/* 备用选择器 - Fallback Selectors */
/* =================================== */

/* 针对可能的其他frontmatter容器 */
.frontmatter,
.yaml-frontmatter,
.metadata-section {
    display: none !important;
}

/* 确保完全隐藏，包括可能的边距 */
.metadata-container,
.frontmatter-container {
    display: none !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* =================================== */
/* 调试模式（可选启用） - Debug Mode */
/* =================================== */

/* 
 * 如果需要临时显示YAML进行调试，
 * 可以在body标签添加 debug-yaml 类
 */
/*
body.debug-yaml .metadata-container,
body.debug-yaml .frontmatter-container {
    display: block !important;
    border: 2px solid red;
    background: rgba(255, 0, 0, 0.1);
}
*/

/* =================================== */
/* 版本信息 - Version Info */
/* =================================== */

/*
 * 文件名: hide-yaml-frontmatter.css
 * 版本: v1.0
 * 创建时间: 2025-07-31
 * 作用: 智能隐藏Obsidian中的YAML frontmatter
 * 兼容性: Obsidian v1.0+ (新旧Properties系统)
 * 方案: 方案2 - 智能隐藏（在不同模式下隐藏YAML）
 */
