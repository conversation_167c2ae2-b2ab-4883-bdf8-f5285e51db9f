// 一键财务记录 - 快速选择支出或收入
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 第一步：选择记录类型
        const recordType = await quickAddApi.suggester(
            ["💸 记录支出", "💰 记录收入", "📅 补录支出", "📅 补录收入"],
            ["expense", "income", "backlog-expense", "backlog-income"]
        );
        
        if (!recordType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 根据选择执行对应的功能
        switch (recordType) {
            case "expense":
                await quickAddApi.executeChoice("💸 今日支出");
                break;
            case "income":
                await quickAddApi.executeChoice("💰 今日收入");
                break;
            case "backlog-expense":
                await quickAddApi.executeChoice("📅 补录支出");
                break;
            case "backlog-income":
                await quickAddApi.executeChoice("📅 补录收入");
                break;
        }
        
    } catch (error) {
        console.error("一键财务记录脚本错误:", error);
        new Notice(`❌ 错误：${error.message}`);
    }
};
