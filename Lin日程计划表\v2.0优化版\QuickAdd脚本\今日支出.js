// 今日支出记录脚本
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 第一步：显示支出类型下拉菜单（32分类架构）
        const expenseType = await quickAddApi.suggester(
            [
                // 🔴 必需支出（ESSENTIAL）- 8类
                "🍽️ 主食", "☕ 饮品", "🚗 通勤", "🚕 打车",
                "🏠 房租", "💡 水电", "🏥 医疗", "💊 药品",

                // 🟡 生活支出（LIFESTYLE）- 8类
                "👕 服装", "🧴 日用品", "🎮 娱乐", "🚬 嗜好",
                "👥 社交", "🎁 礼品", "📦 快递", "📱 通讯",

                // 🟢 投资支出（INVESTMENT）- 8类
                "📚 学习", "💻 数字服务", "💪 技能", "🏃 健康",
                "🔧 工具", "🔨 维修", "🤝 人脉", "📈 投资",

                // 🔵 储备支出（RESERVE）- 8类
                "🚨 应急", "🛡️ 保险", "🎯 机会", "🧪 试错",
                "💰 储蓄", "📊 理财", "💼 其他", "🔄 缓冲"
            ],
            [
                // 必需支出
                "主食", "饮品", "通勤", "打车",
                "房租", "水电", "医疗", "药品",

                // 生活支出
                "服装", "日用品", "娱乐", "嗜好",
                "社交", "礼品", "快递", "通讯",

                // 投资支出
                "学习", "数字服务", "技能", "健康",
                "工具", "维修", "人脉", "投资",

                // 储备支出
                "应急", "保险", "机会", "试错",
                "储蓄", "理财", "其他", "缓冲"
            ]
        );
        
        if (!expenseType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第二步：输入金额
        const amount = await quickAddApi.inputPrompt("💰 输入金额（只输入数字）:");
        if (!amount) {
            new Notice("❌ 已取消操作");
            return;
        }

        // 第三步：输入具体项目
        const item = await quickAddApi.inputPrompt("📦 具体项目（如：午餐、地铁票等）:");
        if (!item) {
            new Notice("❌ 已取消操作");
            return;
        }

        // 第四步：选择必要性
        const necessity = await quickAddApi.suggester(
            ["🔴 必需", "🟡 重要", "🟢 一般", "🔵 冲动"],
            ["🔴 必需", "🟡 重要", "🟢 一般", "🔵 冲动"]
        );
        if (!necessity) {
            new Notice("❌ 已取消操作");
            return;
        }

        // 第五步：输入备注（可选）
        const note = await quickAddApi.inputPrompt("💭 备注（可选，直接回车跳过）:") || "";

        // 第六步：获取当前时间
        const currentTime = new Date().toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // 第七步：构建完整的表格记录
        // 注意：确保每个字段都有内容，避免表格格式错误
        const record = `| ${currentTime} | ${expenseType} | ${amount}元 | ${item} | ${necessity} | ${note || ''} |`;

        // 第五步：智能插入到支出记录表格中
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            new Notice("❌ 请先打开一个文件！");
            return;
        }

        let content = await app.vault.read(activeFile);

        // 使用经验文档中的正确逻辑：先找到支出记录区域，再在该区域内操作
        let insertSuccess = false;

        // 第一步：找到支出记录标题的位置
        const expenseRecordTitlePattern = /### 📉 支出记录/;
        const titleMatch = content.match(expenseRecordTitlePattern);

        if (!titleMatch) {
            // 如果没有找到支出记录标题，在文件末尾创建
            const appendContent = `\n\n### 📉 支出记录\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${record}`;
            await app.vault.modify(activeFile, content + appendContent);
            new Notice(`✅ 已创建支出记录表格并添加：${expenseType} ${amount}元`);
            return;
        }

        // 第二步：从支出记录标题开始，找到下一个标题的位置（确定支出记录区域的边界）
        const titleIndex = titleMatch.index;
        const afterTitleContent = content.slice(titleIndex + titleMatch[0].length);

        // 查找下一个同级或更高级标题（## 或 ###）
        const nextTitleMatch = afterTitleContent.match(/^(##|###)\s/m);
        const expenseRecordEndIndex = nextTitleMatch
            ? titleIndex + titleMatch[0].length + nextTitleMatch.index
            : content.length;

        // 第三步：在支出记录区域内查找表格和现有记录
        const expenseRecordSection = content.slice(titleIndex, expenseRecordEndIndex);

        // 查找表格分隔符 - 使用更精确的模式
        const tableSeparatorPattern = /\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|/;
        const separatorMatch = expenseRecordSection.match(tableSeparatorPattern);

        if (!separatorMatch) {
            // 没有找到表格，在标题后创建完整表格
            const insertPosition = titleIndex + titleMatch[0].length;
            const tableContent = `\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${record}`;
            const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
            await app.vault.modify(activeFile, newContent);
            insertSuccess = true;
        } else {
            // 找到了表格，在支出记录区域内查找现有记录
            const separatorIndex = titleIndex + separatorMatch.index + separatorMatch[0].length;
            const tableDataSection = content.slice(separatorIndex, expenseRecordEndIndex);

            // 在表格数据区域内查找所有记录行
            const recordPattern = /\| \d{2}:\d{2} \| [^|]+ \| [^|]+元 \| [^|]+ \| [^|]+ \| [^|]* \|/g;
            const recordMatches = Array.from(tableDataSection.matchAll(recordPattern));

            if (recordMatches.length > 0) {
                // 在最后一条记录后插入
                const lastMatch = recordMatches[recordMatches.length - 1];
                const lastRecordEndIndex = separatorIndex + lastMatch.index + lastMatch[0].length;
                const newContent = content.slice(0, lastRecordEndIndex) + '\n' + record + content.slice(lastRecordEndIndex);
                await app.vault.modify(activeFile, newContent);
                insertSuccess = true;
            } else {
                // 表格存在但没有数据记录，在分隔符后插入
                const newContent = content.slice(0, separatorIndex) + '\n' + record + content.slice(separatorIndex);
                await app.vault.modify(activeFile, newContent);
                insertSuccess = true;
            }
        }

        if (!insertSuccess) {
            // 如果找不到支出记录部分，在文件末尾添加
            // 注意：使用正确的表格分隔符格式
            const appendContent = `\n\n### 📉 支出记录\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${record}`;
            await app.vault.modify(activeFile, content + appendContent);
            new Notice(`✅ 已在文件末尾创建支出记录表格：${expenseType} ¥${amount}`);
        }
        
        if (insertSuccess) {
            new Notice(`✅ 已记录到支出表格：${expenseType} ¥${amount}`);
        }
        
    } catch (error) {
        console.error("今日支出脚本错误:", error);
        new Notice(`❌ 错误：${error.message}`);
    }
};
