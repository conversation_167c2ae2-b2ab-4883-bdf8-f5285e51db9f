// 今日收入记录脚本
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 第一步：显示收入类型下拉菜单
        const incomeType = await quickAddApi.suggester(
            ["💼 工资", "🎁 奖金", "💻 兼职", "📈 投资收益", "🔄 其他收入"],
            ["💼 工资", "🎁 奖金", "💻 兼职", "📈 投资收益", "🔄 其他收入"]
        );
        
        if (!incomeType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第二步：输入金额
        const amount = await quickAddApi.inputPrompt("💰 输入金额（只输入数字）:");
        if (!amount) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第三步：输入来源说明
        const source = await quickAddApi.inputPrompt("📝 来源说明（如：公司发薪、项目奖金等）:");
        if (!source) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第四步：输入备注（可选）
        const note = await quickAddApi.inputPrompt("💭 备注（可选，直接回车跳过）:") || "";
        
        // 第五步：获取当前时间
        const currentTime = new Date().toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // 第六步：构建完整的表格记录
        const record = `| ${currentTime} | ${incomeType} | ${amount}元 | ${source} | ${note} |`;
        
        // 第七步：智能插入到收入记录表格中
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            new Notice("❌ 请先打开一个文件！");
            return;
        }
        
        let content = await app.vault.read(activeFile);
        
        // 查找收入记录表格的位置
        const patterns = [
            // 模式1：完整的收入记录表格（匹配实际的分隔线格式）
            /### 📈 收入记录\s*\n[\s\S]*?\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|/,
            // 模式2：只有标题的情况
            /### 📈 收入记录/
        ];
        
        let insertSuccess = false;
        
        for (let i = 0; i < patterns.length; i++) {
            const pattern = patterns[i];
            const match = content.match(pattern);
            
            if (match) {
                if (i === 0) {
                    // 找到完整表格，在分隔线后插入
                    const insertPosition = match.index + match[0].length;
                    const newContent = content.slice(0, insertPosition) + '\n' + record + content.slice(insertPosition);
                    await app.vault.modify(activeFile, newContent);
                    insertSuccess = true;
                    break;
                } else if (i === 1) {
                    // 只找到标题，创建完整表格
                    const tableContent = `\n\n| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |\n|------|----------|------|----------|------|\n${record}`;
                    const insertPosition = match.index + match[0].length;
                    const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
                    await app.vault.modify(activeFile, newContent);
                    insertSuccess = true;
                    break;
                }
            }
        }
        
        if (!insertSuccess) {
            // 如果找不到收入记录部分，在文件末尾添加
            const appendContent = `\n\n### 📈 收入记录\n\n| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |\n|------|----------|------|----------|------|\n${record}`;
            await app.vault.modify(activeFile, content + appendContent);
            new Notice(`✅ 已在文件末尾创建收入记录表格：${incomeType} ${amount}元`);
        } else {
            new Notice(`✅ 收入记录已添加：${incomeType} ${amount}元 - ${source}`);
        }
        
    } catch (error) {
        console.error("今日收入脚本错误:", error);
        new Notice(`❌ 错误：${error.message}`);
    }
};
