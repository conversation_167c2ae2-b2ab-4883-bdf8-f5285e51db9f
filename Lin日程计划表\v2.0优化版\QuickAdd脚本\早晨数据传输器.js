// 早晨习惯数据传输器
// 将日记数据传输到生态系统汇总

module.exports = async (params) => {
    const { app, quickAddApi } = params;
    
    try {
        // 获取当前活动文件
        const activeFile = app.workspace.getActiveFile();
        if (!activeFile) {
            new Notice("❌ 请先打开日记文件");
            return;
        }
        
        // 读取文件内容
        const fileContent = await app.vault.read(activeFile);
        
        // 解析frontmatter
        const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
        const frontmatterMatch = fileContent.match(frontmatterRegex);
        
        if (!frontmatterMatch) {
            new Notice("❌ 未找到有效的frontmatter数据");
            return;
        }
        
        // 提取任务完成状态
        const taskPattern = /task_(\d+)_\w+:\s*(true|false)/g;
        const timePattern = /actual_time_(\d+):\s*"?([^"\n]*)"?/g;
        
        let taskMatches = [];
        let timeMatches = [];
        let match;
        
        // 提取任务状态
        while ((match = taskPattern.exec(frontmatterMatch[1])) !== null) {
            taskMatches.push({
                taskId: match[1],
                completed: match[2] === 'true'
            });
        }
        
        // 提取时间数据
        while ((match = timePattern.exec(frontmatterMatch[1])) !== null) {
            timeMatches.push({
                taskId: match[1],
                actualTime: match[2]
            });
        }
        
        // 计算统计数据
        const completedTasks = taskMatches.filter(task => task.completed).length;
        const totalTasks = taskMatches.length;
        const completionRate = Math.round((completedTasks / totalTasks) * 100);
        
        // 计算总用时
        const totalTime = timeMatches.reduce((sum, time) => {
            const minutes = parseInt(time.actualTime) || 0;
            return sum + minutes;
        }, 0);
        
        // 生成传输数据
        const transferData = {
            date: new Date().toISOString().split('T')[0],
            type: "morning_routine",
            completedTasks: completedTasks,
            totalTasks: totalTasks,
            completionRate: completionRate,
            totalTime: totalTime,
            efficiency: totalTime > 0 ? Math.round((completedTasks / totalTime) * 60) : 0,
            taskDetails: taskMatches,
            timeDetails: timeMatches,
            timestamp: new Date().toISOString()
        };
        
        // 创建或更新汇总文件
        const summaryPath = "03-生态系统架构层/数据汇总/早晨习惯数据汇总.md";
        
        let summaryContent = "";
        try {
            const summaryFile = app.vault.getAbstractFileByPath(summaryPath);
            if (summaryFile) {
                summaryContent = await app.vault.read(summaryFile);
            }
        } catch (error) {
            // 文件不存在，创建新文件
        }
        
        // 生成新的数据条目
        const newEntry = `
## 📅 ${transferData.date}

**完成度**: ${transferData.completionRate}% (${transferData.completedTasks}/${transferData.totalTasks})
**总用时**: ${transferData.totalTime}分钟
**效率指数**: ${transferData.efficiency}
**传输时间**: ${transferData.timestamp}

### 详细数据
\`\`\`json
${JSON.stringify(transferData, null, 2)}
\`\`\`

---
`;
        
        // 更新汇总文件
        if (summaryContent === "") {
            summaryContent = `# 🌅 早晨习惯数据汇总

> 自动生成的早晨习惯执行数据汇总

${newEntry}`;
        } else {
            // 在现有内容前插入新条目
            const insertPosition = summaryContent.indexOf('\n## 📅');
            if (insertPosition !== -1) {
                summaryContent = summaryContent.slice(0, insertPosition) + newEntry + summaryContent.slice(insertPosition);
            } else {
                summaryContent += newEntry;
            }
        }
        
        // 确保目录存在
        const summaryDir = "03-生态系统架构层/数据汇总";
        try {
            await app.vault.createFolder(summaryDir);
        } catch (error) {
            // 目录可能已存在
        }
        
        // 写入汇总文件
        try {
            const summaryFile = app.vault.getAbstractFileByPath(summaryPath);
            if (summaryFile) {
                await app.vault.modify(summaryFile, summaryContent);
            } else {
                await app.vault.create(summaryPath, summaryContent);
            }
        } catch (error) {
            new Notice(`❌ 写入汇总文件失败: ${error.message}`);
            return;
        }
        
        // 更新个人仪表板
        await updatePersonalDashboard(app, transferData);
        
        // 显示成功消息
        new Notice(`✅ 数据传输成功！完成度: ${transferData.completionRate}%`);
        
        // 可选：打开汇总文件
        const summaryFile = app.vault.getAbstractFileByPath(summaryPath);
        if (summaryFile) {
            app.workspace.getLeaf().openFile(summaryFile);
        }
        
    } catch (error) {
        new Notice(`❌ 数据传输失败: ${error.message}`);
        console.error("数据传输错误:", error);
    }
};

// 更新个人仪表板
async function updatePersonalDashboard(app, data) {
    const dashboardPath = "03-生态系统架构层/个人仪表板.md";
    
    try {
        let dashboardContent = "";
        try {
            const dashboardFile = app.vault.getAbstractFileByPath(dashboardPath);
            if (dashboardFile) {
                dashboardContent = await app.vault.read(dashboardFile);
            }
        } catch (error) {
            // 文件不存在，创建新的仪表板
        }
        
        // 更新仪表板中的早晨习惯数据
        const morningDataSection = `
## 🌅 早晨习惯状态

**今日完成度**: ${data.completionRate}% ${"█".repeat(Math.floor(data.completionRate/5))}${"░".repeat(20-Math.floor(data.completionRate/5))}
**任务完成**: ${data.completedTasks}/${data.totalTasks}
**用时效率**: ${data.totalTime}分钟 (效率指数: ${data.efficiency})
**更新时间**: ${new Date().toLocaleString()}

`;
        
        if (dashboardContent === "") {
            dashboardContent = `# 🎮 个人状态仪表板

> 实时显示各项生活指标和目标进度

${morningDataSection}`;
        } else {
            // 替换或插入早晨习惯数据
            const morningRegex = /## 🌅 早晨习惯状态[\s\S]*?(?=##|$)/;
            if (morningRegex.test(dashboardContent)) {
                dashboardContent = dashboardContent.replace(morningRegex, morningDataSection.trim() + '\n\n');
            } else {
                dashboardContent += morningDataSection;
            }
        }
        
        // 写入仪表板文件
        const dashboardFile = app.vault.getAbstractFileByPath(dashboardPath);
        if (dashboardFile) {
            await app.vault.modify(dashboardFile, dashboardContent);
        } else {
            await app.vault.create(dashboardPath, dashboardContent);
        }
        
    } catch (error) {
        console.error("更新仪表板失败:", error);
    }
}
