// 智能财务记录 - 在当前文件中添加支出记录
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 获取当前活动文件
        const activeFile = app.workspace.getActiveFile();
        
        if (!activeFile) {
            new Notice("❌ 请先打开一个文件！");
            return;
        }
        
        // 获取当前时间
        const currentTime = new Date().toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // 获取用户输入 - 下拉菜单选择
        const expenseType = await quickAddApi.suggester(
            [
                "🍽️ 餐饮", "🚗 交通", "🛍️ 购物", "🎮 娱乐", "📚 学习",
                "🏥 医疗", "🏠 房租", "💡 水电", "📱 通讯", "� 快递",
                "💄 美容", "👕 服装", "🧴 日用品", "🎁 礼品", "🚕 打车",
                "☕ 咖啡", "🍎 零食", "💊 药品", "🔧 维修", "🔄 其他"
            ],
            [
                "🍽️ 餐饮", "🚗 交通", "🛍️ 购物", "🎮 娱乐", "📚 学习",
                "🏥 医疗", "🏠 房租", "💡 水电", "📱 通讯", "📦 快递",
                "💄 美容", "👕 服装", "🧴 日用品", "🎁 礼品", "🚕 打车",
                "☕ 咖啡", "🍎 零食", "💊 药品", "🔧 维修", "� 其他"
            ]
        );
        
        if (!expenseType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const amount = await quickAddApi.inputPrompt("💰 输入金额（只输入数字）:");
        if (!amount) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const item = await quickAddApi.inputPrompt("📦 具体项目（如：午餐、地铁票等）:");
        if (!item) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const necessity = await quickAddApi.suggester(
            ["🔴 必需", "🟡 重要", "🟢 一般", "🔵 冲动"],
            ["🔴 必需", "🟡 重要", "🟢 一般", "🔵 冲动"]
        );
        if (!necessity) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const note = await quickAddApi.inputPrompt("💭 备注（可选，直接回车跳过）:") || "";
        
        // 读取当前文件内容
        let content = await app.vault.read(activeFile);
        
        // 构建新的记录行
        // 注意：确保每个字段都有内容，避免表格格式错误
        const newRecord = `| ${currentTime} | ${expenseType} | ${amount}元 | ${item} | ${necessity} | ${note || ''} |`;
        
        // 正确的逻辑：先找到支出记录区域，再在该区域内操作
        let insertSuccess = false;

        // 第一步：找到支出记录标题的位置
        const expenseRecordTitlePattern = /### 📉 支出记录/;
        const titleMatch = content.match(expenseRecordTitlePattern);

        if (!titleMatch) {
            // 如果找不到支出记录标题，在文件末尾添加完整的支出记录区域
            const appendContent = `\n\n### 📉 支出记录\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${newRecord}`;
            await app.vault.modify(activeFile, content + appendContent);
            insertSuccess = true;
        } else {
            // 第二步：从支出记录标题开始，提取该区域的内容
            const titleStartIndex = titleMatch.index;

            // 找到下一个三级标题（###）的位置，确定支出记录区域的结束位置
            const nextSectionPattern = /\n### /;
            const contentAfterTitle = content.slice(titleStartIndex + titleMatch[0].length);
            const nextSectionMatch = contentAfterTitle.match(nextSectionPattern);

            let expenseRecordSection;
            let expenseRecordEndIndex;

            if (nextSectionMatch) {
                // 有下一个区域，支出记录区域到下一个三级标题为止
                expenseRecordEndIndex = titleStartIndex + titleMatch[0].length + nextSectionMatch.index;
                expenseRecordSection = content.slice(titleStartIndex, expenseRecordEndIndex);
            } else {
                // 没有下一个区域，支出记录区域到文件末尾
                expenseRecordEndIndex = content.length;
                expenseRecordSection = content.slice(titleStartIndex);
            }

            // 第三步：在支出记录区域内查找表格
            // 先查找表头
            const tableHeaderPattern = /\| 时间 \| 支出类型 \| 金额 \| 具体项目 \| 必要性 \| 备注 \|/;
            const headerMatch = expenseRecordSection.match(tableHeaderPattern);

            if (headerMatch) {
                // 找到表头，查找对应的分隔符
                const headerEndIndex = headerMatch.index + headerMatch[0].length;
                const afterHeader = expenseRecordSection.slice(headerEndIndex);

                // 查找紧跟在表头后的分隔符行
                const separatorPattern = /\s*\n\s*\|------|----------|------|----------|--------|------\|/;
                const separatorMatch = afterHeader.match(separatorPattern);

                if (separatorMatch) {
                    // 找到完整的表格结构，在分隔符后插入新记录
                    const separatorEndInAfterHeader = separatorMatch.index + separatorMatch[0].length;
                    const insertPositionInSection = headerEndIndex + separatorEndInAfterHeader;
                    const insertPosition = titleStartIndex + insertPositionInSection;

                    const newContent = content.slice(0, insertPosition) + '\n' + newRecord + content.slice(insertPosition);
                    await app.vault.modify(activeFile, newContent);
                    insertSuccess = true;
                } else {
                    // 有表头但没有分隔符，添加分隔符和记录
                    const insertPositionInSection = headerEndIndex;
                    const insertPosition = titleStartIndex + insertPositionInSection;

                    const tableContent = `\n|------|----------|------|----------|--------|------|\n${newRecord}`;
                    const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
                    await app.vault.modify(activeFile, newContent);
                    insertSuccess = true;
                }
            } else {
                // 没找到表格，在支出记录标题后创建完整表格
                const insertPosition = titleStartIndex + titleMatch[0].length;
                const tableContent = `\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${newRecord}`;
                const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
                await app.vault.modify(activeFile, newContent);
                insertSuccess = true;
            }
        }
        
        if (!insertSuccess) {
            // 如果找不到支出记录部分，在文件末尾添加
            // 注意：使用正确的表格分隔符格式
            const appendContent = `\n\n### 📉 支出记录\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${newRecord}`;
            await app.vault.modify(activeFile, content + appendContent);
            new Notice(`✅ 已在文件末尾创建支出记录：${expenseType} ${amount}元`);
        } else {
            new Notice(`✅ 支出记录已添加：${expenseType} ${amount}元 - ${item}`);
        }
        
    } catch (error) {
        console.error("智能财务记录脚本错误:", error);
        new Notice(`❌ 记录失败：${error.message}`);
    }
};
