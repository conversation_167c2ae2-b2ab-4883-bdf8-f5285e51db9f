// 补录支出记录脚本
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 第一步：选择日期
        const dateInput = await quickAddApi.inputPrompt("📅 选择日期（YYYY-MM-DD格式）:", "如：2025-07-18", quickAddApi.date.now("YYYY-MM-DD"));
        if (!dateInput) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 验证日期格式
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(dateInput)) {
            new Notice("❌ 日期格式错误，请使用YYYY-MM-DD格式");
            return;
        }
        
        // 第二步：输入时间
        const timeInput = await quickAddApi.inputPrompt("⏰ 输入时间（HH:mm格式）:", "如：14:30");
        if (!timeInput) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 验证时间格式
        const timeRegex = /^\d{2}:\d{2}$/;
        if (!timeRegex.test(timeInput)) {
            new Notice("❌ 时间格式错误，请使用HH:mm格式");
            return;
        }
        
        // 第三步：显示支出类型下拉菜单
        const expenseType = await quickAddApi.suggester(
            [
                "🍽️ 餐饮", "🚗 交通", "🛍️ 购物", "🎮 娱乐", "📚 学习",
                "🏥 医疗", "🏠 房租", "💡 水电", "📱 通讯", "📦 快递",
                "💄 美容", "👕 服装", "🧴 日用品", "🎁 礼品", "🚕 打车",
                "☕ 咖啡", "🍎 零食", "💊 药品", "🔧 维修", "🔄 其他"
            ],
            [
                "🍽️ 餐饮", "🚗 交通", "🛍️ 购物", "🎮 娱乐", "📚 学习",
                "🏥 医疗", "🏠 房租", "💡 水电", "📱 通讯", "📦 快递",
                "💄 美容", "👕 服装", "🧴 日用品", "🎁 礼品", "🚕 打车",
                "☕ 咖啡", "🍎 零食", "💊 药品", "🔧 维修", "🔄 其他"
            ]
        );
        
        if (!expenseType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第四步：输入金额
        const amount = await quickAddApi.inputPrompt("💰 输入金额（只输入数字）:");
        if (!amount) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第五步：输入具体项目
        const item = await quickAddApi.inputPrompt("📦 具体项目（如：午餐、地铁票等）:");
        if (!item) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第六步：选择必要性
        const necessity = await quickAddApi.suggester(
            ["🔴 必需", "🟡 重要", "🟢 一般", "🔵 冲动"],
            ["🔴 必需", "🟡 重要", "🟢 一般", "🔵 冲动"]
        );
        if (!necessity) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第七步：输入备注（可选）
        const note = await quickAddApi.inputPrompt("💭 备注（可选，直接回车跳过）:") || "";
        
        // 第八步：构建完整的表格记录
        const record = `| ${timeInput} | ${expenseType} | ${amount}元 | ${item} | ${necessity} | ${note} |`;
        
        // 第九步：确定目标文件路径
        // 使用相对于vault根目录的正确路径，并进行路径标准化
        const targetFilePath = `01-人工记录输入层/记录界面/日记/2025/07-July/${dateInput}.md`;

        // 第十步：检查文件是否存在，不存在则创建
        let targetFile;
        try {
            // 使用标准化路径查找文件
            targetFile = app.vault.getAbstractFileByPath(targetFilePath);

            // 调试信息
            console.log(`补录支出 - 查找文件路径: ${targetFilePath}`);
            console.log(`补录支出 - 文件是否存在: ${targetFile ? '是' : '否'}`);

            if (!targetFile) {
                // 文件不存在，提示用户
                new Notice(`❌ 未找到文件：${dateInput}.md，请确认日期格式正确且文件存在`);
                return;
            }
        } catch (error) {
            console.error("补录支出 - 文件查找错误:", error);
            new Notice(`❌ 文件操作错误：${error.message}`);
            return;
        }
        
        // 第十一步：智能插入到支出记录表格中
        let content = await app.vault.read(targetFile);

        // 使用经验文档中的正确逻辑：先找到支出记录区域，再在该区域内操作
        let insertSuccess = false;

        // 第一步：找到支出记录标题的位置
        const expenseRecordTitlePattern = /### 📉 支出记录/;
        const titleMatch = content.match(expenseRecordTitlePattern);

        if (!titleMatch) {
            // 如果没有找到支出记录标题，在文件末尾创建
            const appendContent = `\n\n### 📉 支出记录\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${record}`;
            await app.vault.modify(targetFile, content + appendContent);
            new Notice(`✅ 已创建支出记录表格并添加：${expenseType} ${amount}元`);
            return;
        }

        // 第二步：从支出记录标题开始，找到下一个标题的位置（确定支出记录区域的边界）
        const titleIndex = titleMatch.index;
        const afterTitleContent = content.slice(titleIndex + titleMatch[0].length);

        // 查找下一个同级或更高级标题（## 或 ###）
        const nextTitleMatch = afterTitleContent.match(/^(##|###)\s/m);
        const expenseRecordEndIndex = nextTitleMatch
            ? titleIndex + titleMatch[0].length + nextTitleMatch.index
            : content.length;

        // 第三步：在支出记录区域内查找表格和现有记录
        const expenseRecordSection = content.slice(titleIndex, expenseRecordEndIndex);

        // 查找表格分隔符 - 使用更精确的模式
        const tableSeparatorPattern = /\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|\s*-+\s*\|/;
        const separatorMatch = expenseRecordSection.match(tableSeparatorPattern);

        if (!separatorMatch) {
            // 没有找到表格，在标题后创建完整表格
            const insertPosition = titleIndex + titleMatch[0].length;
            const tableContent = `\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n|------|----------|------|----------|--------|------|\n${record}`;
            const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
            await app.vault.modify(targetFile, newContent);
            insertSuccess = true;
        } else {
            // 找到了表格，在支出记录区域内查找现有记录
            const separatorIndex = titleIndex + separatorMatch.index + separatorMatch[0].length;
            const tableDataSection = content.slice(separatorIndex, expenseRecordEndIndex);

            // 在表格数据区域内查找所有记录行
            const recordPattern = /\| \d{2}:\d{2} \| [^|]+ \| [^|]+元 \| [^|]+ \| [^|]+ \| [^|]* \|/g;
            const recordMatches = Array.from(tableDataSection.matchAll(recordPattern));

            if (recordMatches.length > 0) {
                // 在最后一条记录后插入
                const lastMatch = recordMatches[recordMatches.length - 1];
                const lastRecordEndIndex = separatorIndex + lastMatch.index + lastMatch[0].length;
                const newContent = content.slice(0, lastRecordEndIndex) + '\n' + record + content.slice(lastRecordEndIndex);
                await app.vault.modify(targetFile, newContent);
                insertSuccess = true;
            } else {
                // 表格存在但没有数据记录，在分隔符后插入
                const newContent = content.slice(0, separatorIndex) + '\n' + record + content.slice(separatorIndex);
                await app.vault.modify(targetFile, newContent);
                insertSuccess = true;
            }
        }
        
        if (!insertSuccess) {
            // 如果找不到支出记录部分，在文件末尾添加
            const appendContent = `\n\n### 📉 支出记录\n\n| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |\n| --- | ---- | --- | ---- | --- | --- |\n${record}`;
            await app.vault.modify(targetFile, content + appendContent);
            new Notice(`✅ 已在${dateInput}文件末尾创建支出记录表格`);
        } else {
            new Notice(`✅ 已补录到${dateInput}：${expenseType} ${amount}元 - ${item}`);
        }
        
    } catch (error) {
        console.error("补录支出脚本错误:", error);
        new Notice(`❌ 错误：${error.message}`);
    }
};
