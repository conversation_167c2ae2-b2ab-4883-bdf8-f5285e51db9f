// 补录收入记录脚本
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 第一步：选择日期
        const dateInput = await quickAddApi.inputPrompt("📅 选择日期（YYYY-MM-DD格式）:", "如：2025-07-18", quickAddApi.date.now("YYYY-MM-DD"));
        if (!dateInput) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 验证日期格式
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(dateInput)) {
            new Notice("❌ 日期格式错误，请使用YYYY-MM-DD格式");
            return;
        }
        
        // 第二步：输入时间
        const timeInput = await quickAddApi.inputPrompt("⏰ 输入时间（HH:mm格式）:", "如：14:30");
        if (!timeInput) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 验证时间格式
        const timeRegex = /^\d{2}:\d{2}$/;
        if (!timeRegex.test(timeInput)) {
            new Notice("❌ 时间格式错误，请使用HH:mm格式");
            return;
        }
        
        // 第三步：显示收入类型下拉菜单
        const incomeType = await quickAddApi.suggester(
            ["💼 工资", "🎁 奖金", "💻 兼职", "📈 投资收益", "🔄 其他收入"],
            ["💼 工资", "🎁 奖金", "💻 兼职", "📈 投资收益", "🔄 其他收入"]
        );
        
        if (!incomeType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第四步：输入金额
        const amount = await quickAddApi.inputPrompt("💰 输入金额（只输入数字）:");
        if (!amount) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第五步：输入来源说明
        const source = await quickAddApi.inputPrompt("📝 来源说明（如：公司发薪、项目奖金等）:");
        if (!source) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 第六步：输入备注（可选）
        const note = await quickAddApi.inputPrompt("💭 备注（可选，直接回车跳过）:") || "";
        
        // 第七步：构建完整的表格记录
        const record = `| ${timeInput} | ${incomeType} | ${amount}元 | ${source} | ${note} |`;
        
        // 第八步：确定目标文件路径
        // 使用相对于vault根目录的正确路径
        const targetFilePath = `01-人工记录输入层/记录界面/日记/2025/07-July/${dateInput}.md`;

        // 第九步：检查文件是否存在，不存在则创建
        let targetFile;
        try {
            // 使用标准化路径查找文件
            targetFile = app.vault.getAbstractFileByPath(targetFilePath);

            // 调试信息
            console.log(`补录收入 - 查找文件路径: ${targetFilePath}`);
            console.log(`补录收入 - 文件是否存在: ${targetFile ? '是' : '否'}`);

            if (!targetFile) {
                // 文件不存在，提示用户
                new Notice(`❌ 未找到文件：${dateInput}.md，请确认日期格式正确且文件存在`);
                return;
            }
        } catch (error) {
            console.error("补录收入 - 文件查找错误:", error);
            new Notice(`❌ 文件操作错误：${error.message}`);
            return;
        }
        
        // 第十步：智能插入到收入记录表格中
        let content = await app.vault.read(targetFile);

        // 使用精确定位收入记录表格的逻辑
        let insertSuccess = false;

        // 方法1：查找收入记录表格的完整结构（标题+表头+分隔符）
        const completeTablePattern = /### 📈 收入记录\s*\n[\s\S]*?\| 时间 \| 收入类型 \| 金额 \| 来源说明 \| 备注 \|\s*\n\s*\|------|----------|------|----------|------\|/;
        const completeTableMatch = content.match(completeTablePattern);

        if (completeTableMatch) {
            // 找到完整的收入记录表格，在分隔符行后查找现有记录
            const tableStartIndex = completeTableMatch.index + completeTableMatch[0].length;
            const remainingContent = content.slice(tableStartIndex);

            // 在收入记录表格区域内查找现有记录
            const recordPattern = /\| \d{2}:\d{2} \| [^|]+ \| [^|]+元 \| [^|]+ \| [^|]* \|/;
            const recordMatches = [];
            let searchIndex = 0;
            let match;

            // 查找所有在收入记录表格区域内的记录
            while ((match = recordPattern.exec(remainingContent.slice(searchIndex))) !== null) {
                // 检查是否遇到了下一个标题（表示表格结束）
                const beforeMatch = remainingContent.slice(searchIndex, searchIndex + match.index);
                if (beforeMatch.includes('###') || beforeMatch.includes('##')) {
                    break; // 遇到新的标题，停止搜索
                }

                recordMatches.push({
                    match: match[0],
                    absoluteIndex: tableStartIndex + searchIndex + match.index
                });
                searchIndex += match.index + match[0].length;
            }

            if (recordMatches.length > 0) {
                // 在最后一条记录后插入
                const lastRecord = recordMatches[recordMatches.length - 1];
                const insertPosition = lastRecord.absoluteIndex + lastRecord.match.length;
                const newContent = content.slice(0, insertPosition) + '\n' + record + content.slice(insertPosition);
                await app.vault.modify(targetFile, newContent);
                insertSuccess = true;
            } else {
                // 表格存在但没有记录，在分隔符后插入
                const insertPosition = tableStartIndex;
                const newContent = content.slice(0, insertPosition) + '\n' + record + content.slice(insertPosition);
                await app.vault.modify(targetFile, newContent);
                insertSuccess = true;
            }
        } else {
            // 方法2：查找收入记录标题，创建完整表格
            const titlePattern = /### 📈 收入记录/;
            const titleMatch = content.match(titlePattern);

            if (titleMatch) {
                const insertPosition = titleMatch.index + titleMatch[0].length;
                const tableContent = `\n\n| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |\n|------|----------|------|----------|------|\n${record}`;
                const newContent = content.slice(0, insertPosition) + tableContent + content.slice(insertPosition);
                await app.vault.modify(targetFile, newContent);
                insertSuccess = true;
            }
        }
        
        if (!insertSuccess) {
            // 如果找不到收入记录部分，在文件末尾添加
            const appendContent = `\n\n### 📈 收入记录\n\n| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |\n| --- | ---- | --- | ---- | --- |\n${record}`;
            await app.vault.modify(targetFile, content + appendContent);
            new Notice(`✅ 已在${dateInput}文件末尾创建收入记录表格`);
        } else {
            new Notice(`✅ 已补录到${dateInput}：${incomeType} ${amount}元 - ${source}`);
        }
        
    } catch (error) {
        console.error("补录收入脚本错误:", error);
        new Notice(`❌ 错误：${error.message}`);
    }
};
