// 财务状态查询脚本 - 快速显示当前财务状况
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 获取所有日记文件
        const files = app.vault.getMarkdownFiles()
            .filter(file => file.path.includes('日记') && file.name.match(/\d{4}-\d{2}-\d{2}/));
        
        let totalExpense = 0;
        let categoryTotals = {};
        let todayExpense = 0;
        let todayCategories = {};
        
        const today = new Date().toISOString().split('T')[0];
        
        // 遍历所有日记文件提取支出数据
        for (let file of files) {
            const content = await app.vault.read(file);
            const isToday = file.name.includes(today);
            
            // 匹配支出记录表格
            const expenseMatches = content.match(/\| \d{2}:\d{2} \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]*) \|/g);
            
            if (expenseMatches) {
                for (let match of expenseMatches) {
                    const parts = match.split('|').map(p => p.trim());
                    if (parts.length >= 6) {
                        const category = parts[2];
                        const amountStr = parts[3];
                        
                        const amountMatch = amountStr.match(/(\d+(?:\.\d+)?)/);
                        if (amountMatch) {
                            const amount = parseFloat(amountMatch[1]);
                            
                            // 总支出统计
                            totalExpense += amount;
                            if (!categoryTotals[category]) {
                                categoryTotals[category] = 0;
                            }
                            categoryTotals[category] += amount;
                            
                            // 今日支出统计
                            if (isToday) {
                                todayExpense += amount;
                                if (!todayCategories[category]) {
                                    todayCategories[category] = 0;
                                }
                                todayCategories[category] += amount;
                            }
                        }
                    }
                }
            }
        }
        
        const totalBudget = 99.04;
        const currentBalance = totalBudget - totalExpense;
        const usageRate = (totalExpense / totalBudget * 100);
        
        // 构建财务状态报告
        let report = `💰 财务状态报告\n`;
        report += `═══════════════════\n`;
        report += `💰 总账余额: ${totalBudget.toFixed(2)}元\n`;
        report += `💸 累计支出: ${totalExpense.toFixed(2)}元\n`;
        report += `💵 当前余额: ${currentBalance.toFixed(2)}元\n`;
        report += `📊 使用率: ${usageRate.toFixed(1)}%\n\n`;
        
        // 健康状态
        let healthStatus = "";
        if (usageRate < 20) healthStatus = "🟢 优秀";
        else if (usageRate < 50) healthStatus = "🟡 良好";
        else if (usageRate < 80) healthStatus = "🟠 注意";
        else healthStatus = "🔴 警告";
        
        report += `🏥 财务健康: ${healthStatus}\n\n`;
        
        // 今日支出
        if (todayExpense > 0) {
            report += `📅 今日支出: ${todayExpense.toFixed(2)}元\n`;
            report += `───────────────────\n`;
            
            const sortedTodayCategories = Object.entries(todayCategories)
                .sort((a, b) => b[1] - a[1]);
            
            for (let [category, amount] of sortedTodayCategories) {
                const percentage = (amount / todayExpense * 100).toFixed(1);
                report += `${getCategoryIcon(category)} ${category}: ${amount.toFixed(2)}元 (${percentage}%)\n`;
            }
            report += `\n`;
        }
        
        // 主要支出分类
        if (Object.keys(categoryTotals).length > 0) {
            report += `📋 主要支出分类 (累计)\n`;
            report += `───────────────────\n`;
            
            const sortedCategories = Object.entries(categoryTotals)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5); // 只显示前5项
            
            for (let [category, amount] of sortedCategories) {
                const percentage = (amount / totalExpense * 100).toFixed(1);
                report += `${getCategoryIcon(category)} ${category}: ${amount.toFixed(2)}元 (${percentage}%)\n`;
            }
        }
        
        // 预算提醒
        const budgets = {
            "餐饮": 30.00, "交通": 20.00, "购物": 15.00, "娱乐": 10.00,
            "学习": 8.00, "医疗": 5.00, "快递": 3.00
        };
        
        report += `\n🎯 预算状态\n`;
        report += `───────────────────\n`;
        
        for (let [category, budget] of Object.entries(budgets)) {
            const spent = categoryTotals[category] || 0;
            const remaining = budget - spent;
            const rate = budget > 0 ? (spent / budget * 100) : 0;
            
            let status = "🟢";
            if (rate >= 100) status = "🔴";
            else if (rate >= 85) status = "🟠";
            else if (rate >= 70) status = "🟡";
            
            report += `${status} ${category}: 剩余${remaining.toFixed(2)}元\n`;
        }
        
        // 显示报告
        const modal = new Modal(app);
        modal.titleEl.setText("💰 财务状态");
        
        const contentEl = modal.contentEl;
        contentEl.empty();
        
        const preEl = contentEl.createEl("pre");
        preEl.style.whiteSpace = "pre-wrap";
        preEl.style.fontFamily = "monospace";
        preEl.style.fontSize = "14px";
        preEl.style.lineHeight = "1.5";
        preEl.textContent = report;
        
        // 添加操作按钮
        const buttonContainer = contentEl.createDiv();
        buttonContainer.style.marginTop = "20px";
        buttonContainer.style.textAlign = "center";
        
        const openPanelBtn = buttonContainer.createEl("button");
        openPanelBtn.textContent = "📊 打开财务面板";
        openPanelBtn.style.marginRight = "10px";
        openPanelBtn.onclick = async () => {
            modal.close();
            // 打开财务状态面板
            const panelFile = app.vault.getAbstractFileByPath("Lin日程计划表/v2.0优化版/02-AI协作处理层/数据存储池/财务数据/财务状态面板.md");
            if (panelFile) {
                await app.workspace.openLinkText("财务状态面板", "", false);
            }
        };
        
        const recordExpenseBtn = buttonContainer.createEl("button");
        recordExpenseBtn.textContent = "💸 记录支出";
        recordExpenseBtn.onclick = async () => {
            modal.close();
            // 调用支出记录脚本
            await quickAddApi.executeChoice("💸 今日支出");
        };
        
        modal.open();
        
    } catch (error) {
        console.error("财务状态查询错误:", error);
        new Notice(`❌ 查询失败：${error.message}`);
    }
};

// 获取分类图标
function getCategoryIcon(category) {
    const icons = {
        "餐饮": "🍽️", "交通": "🚗", "购物": "🛍️", "娱乐": "🎮",
        "学习": "📚", "医疗": "🏥", "快递": "📦", "房租": "🏠",
        "水电": "💡", "通讯": "📱", "美容": "💄", "服装": "👕",
        "日用品": "🧴", "礼品": "🎁", "打车": "🚕", "咖啡": "☕",
        "零食": "🍎", "药品": "💊", "维修": "🔧", "其他": "🔄"
    };
    return icons[category] || "❓";
}
