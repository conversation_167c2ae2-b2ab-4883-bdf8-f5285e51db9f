// 简单的财务记录脚本
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 获取今天的日期
        const today = new Date().toISOString().split('T')[0];
        const currentTime = new Date().toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // 构建日记文件路径 - 使用实际的路径结构
        const dailyNotePath = `Lin日程计划表/v2.0优化版/01-人工记录输入层/记录界面/日记/2025/${today}.md`;
        
        // 检查文件是否存在
        let dailyNote = app.vault.getAbstractFileByPath(dailyNotePath);
        
        if (!dailyNote) {
            // 创建新的日记文件
            const newContent = `# ${today} 日记

### 📈 收入记录
| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |
|------|----------|------|----------|------|

### 📉 支出记录
| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|

### 📊 今日财务总结
- 今日总收入：___
- 今日总支出：___
- 是否有节约空间：___
- 明日预计支出：___
`;
            dailyNote = await app.vault.create(dailyNotePath, newContent);
        }
        
        // 获取用户输入 - 简化版本
        const expenseType = await quickAddApi.suggester(
            [
                "🍽️ 餐饮", "🚗 交通", "🛍️ 购物", "🎮 娱乐", "📚 学习",
                "🏥 医疗", "🏠 房租", "💡 水电", "📱 通讯", "📦 快递",
                "💄 美容", "👕 服装", "🧴 日用品", "🎁 礼品", "🚕 打车",
                "☕ 咖啡", "🍎 零食", "💊 药品", "🔧 维修", "🔄 其他"
            ],
            [
                "🍽️ 餐饮", "🚗 交通", "🛍️ 购物", "🎮 娱乐", "📚 学习",
                "🏥 医疗", "🏠 房租", "💡 水电", "📱 通讯", "📦 快递",
                "💄 美容", "👕 服装", "🧴 日用品", "🎁 礼品", "🚕 打车",
                "☕ 咖啡", "🍎 零食", "💊 药品", "🔧 维修", "🔄 其他"
            ]
        );
        
        if (!expenseType) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const amount = await quickAddApi.inputPrompt("💰 输入金额（只输入数字）:");
        if (!amount) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const item = await quickAddApi.inputPrompt("📦 具体项目（如：午餐、地铁票等）:");
        if (!item) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        const note = await quickAddApi.inputPrompt("💭 备注（可选，直接回车跳过）:") || "";
        
        // 自动判断必要性
        let necessity = "🟡 重要";
        if (expenseType.includes("餐饮") || expenseType.includes("交通") || expenseType.includes("房租") || expenseType.includes("医疗")) {
            necessity = "🔴 必需";
        } else if (expenseType.includes("娱乐") || expenseType.includes("购物")) {
            necessity = "🟢 一般";
        }
        
        // 读取当前文件内容
        let content = await app.vault.read(dailyNote);
        
        // 构建新的记录行
        const newRecord = `| ${currentTime} | ${expenseType} | ${amount}元 | ${item} | ${necessity} | ${note} |`;
        
        // 查找支出记录表格并插入新记录
        const tablePattern = /### 📉 支出记录\n\| 时间 \| 支出类型 \| 金额 \| 具体项目 \| 必要性 \| 备注 \|\n\|------|----------|------|----------|--------|------\|/;
        
        if (tablePattern.test(content)) {
            // 在表格头后插入新记录
            const newContent = content.replace(
                tablePattern,
                `### 📉 支出记录
| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|
${newRecord}`
            );
            
            await app.vault.modify(dailyNote, newContent);
            new Notice(`✅ 支出记录已添加：${expenseType} ${amount}元 - ${item}`);
        } else {
            // 如果找不到表格，在文件末尾添加
            const appendContent = `\n${newRecord}`;
            await app.vault.modify(dailyNote, content + appendContent);
            new Notice(`✅ 支出记录已添加到文件末尾：${expenseType} ${amount}元`);
        }
        
    } catch (error) {
        console.error("财务记录脚本错误:", error);
        new Notice(`❌ 记录失败：${error.message}`);
    }
};
