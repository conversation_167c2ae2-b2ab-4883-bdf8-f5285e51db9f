// 路径测试脚本 - 用于调试文件路径问题
module.exports = async (params) => {
    const { quickAddApi, app } = params;
    
    try {
        // 测试日期输入
        const dateInput = await quickAddApi.inputPrompt("输入测试日期（格式：2025-07-23）:");
        if (!dateInput) {
            new Notice("❌ 已取消操作");
            return;
        }
        
        // 构建文件路径
        const targetFilePath = `01-人工记录输入层/记录界面/日记/2025/07-July/${dateInput}.md`;
        
        // 输出调试信息
        console.log("=== 路径测试调试信息 ===");
        console.log(`输入日期: ${dateInput}`);
        console.log(`构建路径: ${targetFilePath}`);
        
        // 尝试查找文件
        const targetFile = app.vault.getAbstractFileByPath(targetFilePath);
        console.log(`文件对象: ${targetFile}`);
        console.log(`文件是否存在: ${targetFile ? '是' : '否'}`);
        
        if (targetFile) {
            console.log(`文件名: ${targetFile.name}`);
            console.log(`文件路径: ${targetFile.path}`);
            console.log(`文件大小: ${targetFile.stat.size} bytes`);
        }
        
        // 列出所有可能的文件
        console.log("=== 日记文件夹中的所有文件 ===");
        const allFiles = app.vault.getMarkdownFiles();
        const julyFiles = allFiles.filter(file => file.path.includes('07-July'));
        julyFiles.forEach(file => {
            console.log(`文件: ${file.path}`);
        });
        
        // 显示结果
        if (targetFile) {
            new Notice(`✅ 找到文件：${targetFile.name}`);
        } else {
            new Notice(`❌ 未找到文件：${dateInput}.md`);
        }
        
        // 显示所有July文件
        const fileList = julyFiles.map(f => f.name).join(', ');
        new Notice(`📁 July文件夹中的文件：${fileList}`);
        
    } catch (error) {
        console.error("路径测试错误:", error);
        new Notice(`❌ 测试失败：${error.message}`);
    }
};
