const { <PERSON>lugin, <PERSON>emVie<PERSON>, WorkspaceLeaf } = require('obsidian');

const VIEW_TYPE_FINANCE_PANEL = "finance-panel-view";

class FinancePanelView extends ItemView {
    constructor(leaf) {
        super(leaf);
    }

    getViewType() {
        return VIEW_TYPE_FINANCE_PANEL;
    }

    getDisplayText() {
        return "💰 财务快捷面板";
    }

    getIcon() {
        return "wallet";
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        
        // 创建标题
        container.createEl("h4", { text: "💰 财务快捷操作" });
        
        // 创建按钮容器
        const buttonContainer = container.createEl("div", {
            cls: "finance-button-container"
        });
        
        // 添加CSS样式
        const style = container.createEl("style");
        style.textContent = `
            .finance-button-container {
                display: flex;
                flex-direction: column;
                gap: 8px;
                padding: 10px;
            }
            .finance-button {
                background: #4a5568;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 13px;
                transition: background 0.2s;
            }
            .finance-button:hover {
                background: #2d3748;
            }
            .expense-btn {
                background: #e53e3e;
            }
            .income-btn {
                background: #38a169;
            }
        `;
        
        // 创建财务按钮
        const buttons = [
            { text: "💸 今日支出", class: "expense-btn", command: "💸 今日支出" },
            { text: "💰 今日收入", class: "income-btn", command: "💰 今日收入" },
            { text: "📅 补录支出", class: "expense-btn", command: "📅 补录支出" },
            { text: "📈 补录收入", class: "income-btn", command: "📈 补录收入" }
        ];
        
        buttons.forEach(btnConfig => {
            const button = buttonContainer.createEl("button", {
                text: btnConfig.text,
                cls: `finance-button ${btnConfig.class}`
            });
            
            button.addEventListener('click', () => {
                this.executeFinanceCommand(btnConfig.command);
            });
        });
        
        // 添加状态显示
        this.statusEl = container.createEl("div", {
            text: "准备就绪",
            cls: "finance-status"
        });
    }

    executeFinanceCommand(commandName) {
        try {
            // 尝试执行CMDR命令
            const success = this.app.commands.executeCommandById(`cmdr:${commandName}`);
            if (success) {
                this.showStatus(`✅ 执行成功: ${commandName}`, "success");
                return;
            }
            
            // 如果CMDR失败，尝试QuickAdd命令
            this.app.commands.executeCommandById(`quickadd:choice:${commandName}`);
            this.showStatus(`✅ 执行成功: ${commandName}`, "success");
            
        } catch (error) {
            console.error('执行命令失败:', error);
            this.showStatus(`❌ 执行失败: ${commandName}`, "error");
        }
    }
    
    showStatus(message, type) {
        if (this.statusEl) {
            this.statusEl.textContent = message;
            this.statusEl.style.color = type === "success" ? "#38a169" : "#e53e3e";
            
            // 3秒后恢复
            setTimeout(() => {
                this.statusEl.textContent = "准备就绪";
                this.statusEl.style.color = "";
            }, 3000);
        }
    }

    async onClose() {
        // 清理工作
    }
}

class FinancePanelPlugin extends Plugin {
    async onload() {
        // 注册自定义视图
        this.registerView(
            VIEW_TYPE_FINANCE_PANEL,
            (leaf) => new FinancePanelView(leaf)
        );

        // 添加命令来打开面板
        this.addCommand({
            id: 'open-finance-panel',
            name: '打开财务面板',
            callback: () => {
                this.activateView();
            }
        });

        // 添加ribbon图标
        this.addRibbonIcon('wallet', '财务面板', () => {
            this.activateView();
        });
        
        console.log('财务面板插件已加载');
    }

    async onunload() {
        // 卸载时清理视图
        this.app.workspace.detachLeavesOfType(VIEW_TYPE_FINANCE_PANEL);
        console.log('财务面板插件已卸载');
    }

    async activateView() {
        // 检查是否已经存在
        const existing = this.app.workspace.getLeavesOfType(VIEW_TYPE_FINANCE_PANEL);
        if (existing.length > 0) {
            // 如果已存在，就激活它
            this.app.workspace.revealLeaf(existing[0]);
            return;
        }

        // 在右侧边栏创建新的视图
        await this.app.workspace.getRightLeaf(false).setViewState({
            type: VIEW_TYPE_FINANCE_PANEL,
            active: true,
        });

        // 显示面板
        this.app.workspace.revealLeaf(
            this.app.workspace.getLeavesOfType(VIEW_TYPE_FINANCE_PANEL)[0]
        );
    }
}

module.exports = FinancePanelPlugin;
