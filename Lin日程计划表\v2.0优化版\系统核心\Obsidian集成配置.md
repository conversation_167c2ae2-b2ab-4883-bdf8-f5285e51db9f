# Obsidian集成配置指南 v2.0

## 🎯 配置目标

实现林海建个人AI协作系统与Obsidian的深度集成，支持自动化记录、数据提取和智能分析。

## 📦 必需插件列表

### 核心插件组合
1. **Periodic Notes** - 时间维度记录系统
2. **Calendar** - 可视化日历导航
3. **Templater** - 动态模板生成
4. **Dataview** - 数据查询和统计
5. **Daily Notes** - 每日笔记基础功能

### 辅助插件推荐
6. **QuickAdd** - 快速创建和操作
7. **Natural Language Dates** - 自然语言日期
8. **Tag Wrangler** - 标签管理
9. **Advanced Tables** - 表格增强
10. **Excalidraw** - 图表绘制

## ⚙️ 详细配置步骤

### 1. Periodic Notes 配置

#### 基础设置
```
Daily Notes:
- Format: YYYY-MM-DD
- Folder: 01-人工记录输入层/记录界面/日记
- Template: 01-人工记录输入层/Obsidian模板库/日记模板.md

Weekly Notes:
- Format: YYYY-[W]ww
- Folder: 01-人工记录输入层/记录界面/周记
- Template: 01-人工记录输入层/Obsidian模板库/周记模板.md

Monthly Notes:
- Format: YYYY-MM
- Folder: 01-人工记录输入层/记录界面/月记
- Template: 01-人工记录输入层/Obsidian模板库/月记模板.md
```

#### 高级设置
```
Auto-create:
- ✅ Enable auto-creation for daily notes
- ✅ Enable auto-creation for weekly notes
- ⚪ Enable auto-creation for monthly notes (手动创建)

Navigation:
- ✅ Show week number
- ✅ Show month name
- ✅ Enable ribbon icon
```

### 2. Calendar 插件配置

#### 显示设置
```
Calendar View:
- Start week on: Monday (周一开始)
- Show week numbers: Yes
- Confirm before creating: No (快速创建)

Integration:
- ✅ Connect with Daily Notes
- ✅ Connect with Periodic Notes
- ✅ Show dots for existing notes
```

### 3. Templater 配置

#### 模板文件夹
```
Template folder location: 01-人工记录输入层/Obsidian模板库/
Trigger Templater on new file creation: Yes
```

#### 自定义函数
```javascript
// 获取当前周的所有日记
tp.user.getWeeklyNotes = function() {
    const startOfWeek = moment().startOf('week');
    const endOfWeek = moment().endOf('week');
    // 返回本周所有日记文件
}

// 计算数据统计
tp.user.calculateStats = function(dataType) {
    // 计算财务、时间、情绪等数据统计
}
```

### 4. Dataview 配置

#### 基础设置
```
Enable JavaScript Queries: Yes
Enable Inline Queries: Yes
Enable Inline JavaScript Queries: Yes
```

#### 常用查询模板

##### 财务数据统计
```dataview
TABLE 
  sum(finance.income) as "总收入",
  sum(finance.expense) as "总支出",
  (sum(finance.income) - sum(finance.expense)) as "净收入"
FROM "01-人工记录输入层/记录界面"
WHERE type = "daily-note" 
  AND date >= date(today) - dur(30 days)
GROUP BY dateformat(date, "yyyy-MM") as "月份"
```

##### 时间分配分析
```dataview
TABLE 
  avg(time.work_hours) as "平均工作时间",
  avg(time.study_hours) as "平均学习时间",
  avg(time.exercise_hours) as "平均运动时间"
FROM "01-人工记录输入层/记录界面"
WHERE type = "daily-note" 
  AND date >= date(today) - dur(7 days)
```

##### 情绪趋势分析
```dataview
LINE CHART
  emotion.overall_mood,
  emotion.stress_level
FROM "01-人工记录输入层/记录界面"
WHERE type = "daily-note" 
  AND date >= date(today) - dur(30 days)
```

## 🚀 快捷键设置

### 推荐快捷键配置
```
Ctrl+D: 创建今日日记
Ctrl+W: 创建本周周记
Ctrl+M: 创建本月月记
Ctrl+Shift+T: 插入当前时间
Ctrl+Shift+D: 插入当前日期
F1: 快速切换到日记文件夹
F2: 快速切换到周记文件夹
```

### QuickAdd 快速操作
```
快速记录财务: Ctrl+Alt+F
快速记录情绪: Ctrl+Alt+E
快速记录健康: Ctrl+Alt+H
快速记录工作: Ctrl+Alt+W
```

## 📁 文件夹结构配置

### 建议的文件夹组织
```
01-人工记录输入层/
├─ 记录界面/
│  ├─ 日记/
│  │  ├─ 2025/
│  │  │  ├─ 01-January/
│  │  │  └─ 02-February/
│  ├─ 周记/
│  │  └─ 2025/
│  └─ 月记/
│     └─ 2025/
├─ Obsidian模板库/
└─ 插件配置/
```

## 🔧 自动化工作流

### 每日工作流
1. **早晨启动**：自动打开今日日记
2. **记录输入**：使用模板快速记录
3. **AI处理**：自动识别和分类数据
4. **晚间总结**：完善记录和反思

### 每周工作流
1. **周一规划**：创建本周周记，设定目标
2. **周中检查**：查看进度和调整
3. **周末总结**：完成周记，分析数据

### 数据同步流程
1. **实时更新**：YAML字段自动更新
2. **数据验证**：检查数据完整性
3. **智能分析**：生成洞察报告
4. **反馈优化**：根据使用效果调整

## 📊 数据可视化配置

### Dashboard 设置
创建一个总览面板，包含：
- 今日数据快览
- 本周趋势图表
- 本月统计汇总
- 重要指标监控

### 图表类型
- **折线图**：情绪和压力趋势
- **柱状图**：时间分配对比
- **饼图**：支出类别分布
- **热力图**：活动频率分析

## 🚨 注意事项

### 性能优化
- 定期清理无用数据
- 优化Dataview查询效率
- 控制模板复杂度
- 合理使用插件数量

### 数据备份
- 定期导出重要数据
- 设置自动备份机制
- 保持多个备份版本
- 测试恢复流程

### 隐私安全
- 敏感信息加密存储
- 控制数据访问权限
- 定期更新插件版本
- 注意数据同步安全

---

**配置完成后，请测试所有功能是否正常工作，并根据实际使用效果进行调整优化。**
