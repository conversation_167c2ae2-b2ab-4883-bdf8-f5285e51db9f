# 新手Obsidian配置指南 - 超级简化版

## 🎯 目标
让您能够：
- 每天快速创建日记
- 用打勾的方式记录
- AI自动帮您分析数据

## 📦 只需要安装这3个插件

### 1. Daily Notes（每日笔记）
**作用**：每天自动创建日记  
**安装**：设置 → 插件 → 核心插件 → 开启"每日笔记"

**简单设置**：
- 日期格式：`YYYY-MM-DD`
- 新文件位置：`日记文件夹`
- 模板文件位置：选择我们的`新手友好日记模板.md`

### 2. Calendar（日历）
**作用**：点击日历就能创建日记  
**安装**：设置 → 社区插件 → 搜索"Calendar" → 安装

**简单设置**：
- 开启后左侧会出现日历
- 点击任意日期就能创建/打开日记

### 3. Templater（模板）
**作用**：自动填入日期和时间  
**安装**：设置 → 社区插件 → 搜索"Templater" → 安装

**简单设置**：
- 模板文件夹：选择`Obsidian模板库`文件夹
- 开启"新建文件时触发模板"

## 🚀 使用方法（超级简单）

### 每日使用流程
1. **打开Obsidian**
2. **点击左侧日历的今天**
3. **开始记录**：
   - 在"今日记录"区域写：`7点运动-HIIT-4组`
   - 在下面的选择区域打勾 ✅
4. **完成**！

### 记录示例
```
## 📝 今日记录
7点运动-HIIT-4组
中午花了30元吃饭
下午学习Python 2小时
晚上和朋友聊天很开心

## ✅ 快速选择
✅ 今天有支出（30元，吃饭）
✅ 学习/看书（2小时）
✅ 运动健身（30分钟，HIIT）
✅ 很开心
✅ 和朋友聊天
```

## 🎯 AI如何帮您分析

### AI会自动识别
- **财务**：30元支出，餐饮类
- **时间**：学习2小时，运动30分钟
- **情绪**：开心，社交满足
- **健康**：有运动，HIIT训练
- **社交**：和朋友互动

### AI会自动分类存储
- 财务数据 → 支出30元
- 时间数据 → 学习2h，运动0.5h
- 情绪数据 → 正面情绪
- 健康数据 → 运动记录
- 社交数据 → 朋友互动

## 📱 快捷操作

### 推荐快捷键
- `Ctrl + T`：创建今日日记
- `Ctrl + D`：插入今日日期
- `Ctrl + ;`：插入当前时间

### 手机使用
- 下载Obsidian手机版
- 同步设置后可以随时记录
- 打勾操作在手机上也很方便

## 🔧 常见问题

### Q：模板没有自动加载？
A：检查Templater插件设置，确保选择了正确的模板文件夹

### Q：日历没有显示？
A：重启Obsidian，或者在左侧面板手动开启Calendar

### Q：不知道怎么打勾？
A：在`- [ ]`中间加x变成`- [x]`，或者直接点击

### Q：AI分析在哪里？
A：目前需要手动请AI分析，后续可以配置自动化

## 🎪 进阶功能（可选）

### 如果您想要更多功能
- **Dataview插件**：自动统计数据
- **QuickAdd插件**：快速添加常用内容
- **自定义快捷键**：一键完成常用操作

### 数据统计示例
```
本周运动：5次
本周支出：500元
平均心情：8/10
学习时间：15小时
```

## 🚨 重要提醒

### 新手建议
1. **先用简单的**：只用打勾和简单记录
2. **养成习惯**：每天记录一点点
3. **逐步完善**：熟悉后再添加更多功能
4. **不要贪多**：功能够用就行

### 数据安全
- 定期备份笔记文件夹
- 可以同步到云盘
- 重要数据多备份几份

---

**记住：简单开始，逐步完善！不要被复杂功能吓到，先用起来最重要！** 🎯
