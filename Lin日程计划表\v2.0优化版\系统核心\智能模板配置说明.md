# 智能动态模板配置说明

## 🎯 这个模板的智能功能

### **自动识别星期**
- **周一三五**：自动显示力量训练安排
- **周二四**：自动显示走路1万步安排  
- **周六**：自动显示自由运动选择
- **周日**：自动显示休息日安排

### **固定时间安排**
- 自动显示每日固定时间段
- 只需要填写具体内容和打勾
- 追踪实际用时vs计划用时

### **渐进式优化**
- 记录实际用时，用于下次调整
- 追踪效率变化
- 支持目标时间的动态调整

## 📦 需要的插件

### 1. Templater（必需）
**作用**：实现智能判断星期几  
**安装**：社区插件搜索"Templater"

**重要设置**：
```
设置 → Templater → Template folder location
选择：01-人工记录输入层/Obsidian模板库/

✅ 开启：Trigger Templater on new file creation
✅ 开启：Enable System Commands
```

### 2. Daily Notes + Calendar
**作用**：点击日期自动创建日记  
**设置**：模板选择"智能动态日记模板.md"

## 🔧 个性化定制

### 修改运动安排
在模板文件中找到这部分，修改成您的安排：

```javascript
// 周一三五的安排
if (dayNum === 1 || dayNum === 3 || dayNum === 5) {
  // 这里改成您的力量训练内容
}

// 周二四的安排  
else if (dayNum === 2 || dayNum === 4) {
  // 这里改成您的有氧运动内容
}
```

### 修改时间安排
```
🌅 上午时段
- 7:00-8:00 改成您的晨练时间
- 8:00-9:00 改成您的早餐时间
- 9:00-12:00 改成您的工作时间
```

### 调整目标时间
```
力量训练（目标：30分钟 → 改成您的目标时间）
走路1万步（目标：60分钟 → 改成您的目标时间）
```

## 🎪 使用方法

### 每日使用流程
1. **点击日历今天** → 自动创建今日日记
2. **查看运动安排** → 根据星期自动显示
3. **执行并打勾** → 完成就勾，记录实际时间
4. **填写时间段** → 记录具体做了什么
5. **评估效率** → 为下次优化提供数据

### 使用示例（周一）
```
💪 力量训练日（周一/三/五）
✅ 热身（5分钟）
✅ 力量训练（目标：30分钟，实际：25分钟）
  ✅ 上肢训练
  ✅ 下肢训练  
  ✅ 核心训练
✅ 拉伸放松（5分钟）
训练感受：适中😐
完成质量：很好👍
```

## 📈 渐进式优化流程

### 第1周：建立基线
- 记录每项活动的实际用时
- 不要急于调整，先收集数据
- 重点是养成记录习惯

### 第2-4周：观察模式
- 对比计划时间vs实际时间
- 识别哪些活动用时稳定缩短
- 哪些活动需要更多时间

### 第5周开始：动态调整
```
力量训练：30分钟 → 实际只需20分钟 → 调整目标为25分钟
多出的5分钟可以：
- 增加新的训练项目
- 延长拉伸时间
- 增加有氧运动
```

## 🔄 模板迭代升级

### 阶段1：基础版（现在）
- 固定运动安排
- 基本时间追踪
- 简单打勾操作

### 阶段2：进阶版（熟练后）
- 添加强度等级选择
- 增加心率监测
- 添加营养记录

### 阶段3：高级版（深度使用）
- 自动计算卡路里
- 生成运动报告
- 智能建议调整

## 🎯 关键优势

### 1. 智能适配
- 不同星期显示不同内容
- 减少重复输入
- 专注于执行和记录

### 2. 渐进优化
- 追踪实际vs计划时间
- 数据驱动的调整
- 持续改进效率

### 3. 习惯养成
- 固定的时间框架
- 简单的打勾操作
- 清晰的进度反馈

## 🚨 使用技巧

### 新手建议
1. **先用1-2周**：熟悉模板结构
2. **重点记录时间**：这是优化的关键数据
3. **不要频繁调整**：给自己适应时间
4. **关注趋势**：而不是单日表现

### 常见问题
- **模板没有自动判断星期**：检查Templater插件设置
- **时间安排不合适**：直接修改模板文件
- **想添加新项目**：在对应星期的部分添加
- **数据分析不够**：可以配合Dataview插件

---

**记住：这个模板会随着您的使用而不断优化，关键是开始使用并持续记录！** 🎯
