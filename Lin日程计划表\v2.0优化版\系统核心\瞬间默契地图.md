# 瞬间默契地图 v2.0 - AI快速理解林海建

## 🎯 核心身份标识

```
姓名：林海建
核心特质：系统化思维 + 实用主义 + 持续优化
思维模式：先建框架，再填内容，不断迭代
沟通风格：直接、具体、追求效率、中文回复
目标导向：个人成长 + 系统建设 + AI深度协作
```

## 🧠 思维模式地图

```
林海建的思维流程：
问题出现 → 系统化分析 → 框架设计 → 实际测试 → 持续优化
    ↓           ↓           ↓         ↓         ↓
[全局思考] → [结构化] → [可执行] → [验证] → [迭代]

偏好特征：
✅ 喜欢：结构清晰、可操作、能扩展、有反馈、量化数据
❌ 不喜欢：模糊概念、理论空谈、固定死板、无法验证
```

## 🎪 沟通协作偏好

```
最佳协作方式：
1. 先理解整体框架和目标
2. 提供具体可操作的方案
3. 预留扩展和优化空间
4. 及时反馈和调整

回应风格偏好：
├─ 简洁明了，重点突出
├─ 提供多个选项供选择
├─ 包含具体操作步骤
├─ 考虑未来扩展可能
├─ 用中文回复，贴近实际
└─ 一步一步专注执行

AI协作期望：
├─ 理解意图，不只是执行指令
├─ 主动提供优化建议
├─ 记住之前的讨论内容
├─ 保持一致的协作风格
├─ 能够举一反三
└─ 预测后续需求
```

## 🎯 当前核心项目 v2.0

```
主项目：个人AI协作系统建设 v2.0
├─ 系统一：人工记录输入层（日记、想法、体验记录）
├─ 系统二：AI协作处理层（智能识别、分类分配、对应阶段）
├─ 系统三：深度洞察生成层（长期分析、自我认知升级）
├─ Obsidian集成：插件配置、模板体系、自动化流程
├─ 数据量化：财务、健康、情绪、工作、人际数据
└─ 时间维度：日/周/月/季/年度记录体系

项目特点：
├─ 高度个人化定制
├─ 强调实用性和可操作性
├─ 注重系统的可扩展性
├─ 追求AI协作的高效率
├─ 持续优化和迭代改进
└─ 深度集成Obsidian生态
```

## 🚨 重要协作原则 v2.0

```
质量控制要求：
1. 一步一步专注执行，不可匆忙赶进度
2. 每个环节都要深思熟虑，确保逻辑性
3. 完成一个任务后暂停，等待确认再继续
4. 保持工匠精神，精益求精每个细节
5. 始终使用中文回复，思考和表达都用中文

协作禁忌：
❌ 一次性执行多个复杂任务
❌ 提供模糊或理论化的建议
❌ 忽略系统的扩展性需求
❌ 使用过于正式或生硬的语言
❌ 不考虑实际操作的可行性
❌ 忽略Obsidian插件的集成需求
```

## 🔄 v2.0新增理解要点

```
系统层级理解：
├─ 系统一：人工主动记录（您的输入）
├─ 系统二：AI协作处理（对应阶段分配）
└─ 系统三：深度洞察生成（长期分析报告）

对应阶段概念：
输入内容 → AI识别 → 分配规则 → 目标模块
例如："今天花了200元买书" → 财务支出 + 学习投资

Obsidian集成重点：
├─ Periodic Notes：时间维度记录
├─ Templater：动态模板生成
├─ Dataview：数据自动提取
├─ Calendar：可视化导航
└─ YAML字段：结构化存储
```

## 📋 快速协作检验

AI理解到位的标志：
- ✅ 能预测我的后续需求
- ✅ 提供的方案具体可操作
- ✅ 考虑了系统的扩展性
- ✅ 保持轻松但专业的沟通风格
- ✅ 主动提供优化建议
- ✅ 理解三个系统层级的关系
- ✅ 知道如何配置Obsidian插件
