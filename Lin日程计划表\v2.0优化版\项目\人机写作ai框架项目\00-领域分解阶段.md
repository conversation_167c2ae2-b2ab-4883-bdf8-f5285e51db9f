# 第零阶段：领域分解 - 通用人机协作框架

## 🎯 阶段目标

**引导发现** - 不是直接给答案，而是帮助用户发现"不知道的东西"，然后引导选择最适合的学习路径。

## 🔍 核心操作流程

### 第1步：接收用户需求
```
用户说："我想了解 [某个领域]"
```

### 第2步：引导式提问（而不是直接回答）
```
我应该问：
1. 您是想要系统性学习，还是解决特定问题？
2. 您的相关背景如何？
3. 您希望应用到什么场景？
4. 您的时间安排如何？
```

### 第3步：分解展示（拓展认知）
```
展示用户可能不知道的领域构成：
"[领域名称] 其实包含这些您可能不知道的方向：
- 方向1：[简单说明]
- 方向2：[简单说明]  
- 方向3：[简单说明]
- ...

现在您可以选择最感兴趣的方向深入..."
```

### 第4步：三版本确认理解
- **A版本（您看）** - 简单清晰的选择菜单
- **B版本（我理解）** - 技术分类和处理逻辑  
- **C版本（我们确认）** - 详细确认我们说的是同一件事

## 📋 实际操作模板

### 模板1：学习类需求
```
用户："我想学习AI"

引导提问：
- 您是想要理论学习还是实际应用？
- 您有编程基础吗？
- 您想解决什么具体问题？

分解展示：
"AI领域包含这些您可能不知道的方向：
- 机器学习算法（数据分析预测）
- 深度学习框架（图像识别、语言处理）
- 自然语言处理（聊天机器人、文本分析）
- 计算机视觉（图像识别、自动驾驶）
- 强化学习（游戏AI、决策优化）
- AI工具平台（ChatGPT、Midjourney等）
- 实际应用场景（工作自动化、创意辅助）

您最感兴趣的是哪个方向？"
```

### 模板2：分析类需求  
```
用户："我想分析世界杯"

引导提问：
- 您是想要预测比赛结果还是分析历史数据？
- 您关注球队表现还是球员个人？
- 您有数据分析经验吗？

分解展示：
"世界杯分析包含这些您可能不知道的角度：
- 球队实力分析（历史战绩、球员配置）
- 比赛数据分析（进球数、控球率、传球成功率）
- 战术分析（阵型、打法、策略变化）
- 球员个人分析（技术统计、状态评估）
- 赛程影响分析（休息时间、地理因素）
- 历史趋势分析（夺冠规律、爆冷概率）

您最想深入了解哪个角度？"
```

### 模板3：工具掌握类需求
```
用户："我想掌握黑曜石软件"

引导提问：
- 您主要用来做什么？（学习笔记/工作文档/知识管理）
- 您用过其他笔记软件吗？
- 您希望多快上手？

分解展示：
"黑曜石软件包含这些您可能不知道的功能：
- 基础笔记功能（编辑、格式化、保存）
- 双向链接系统（知识关联、图谱构建）
- 插件生态系统（功能扩展、个性化定制）
- 模板和自动化（提高效率、标准化流程）
- 数据管理（导入导出、备份同步）
- 高级应用（数据库、看板、日程管理）

您最希望先掌握哪个功能？"
```

## ✅ 质量检查清单

### 引导提问是否有效？
- [ ] 问题能帮助了解用户真实需求
- [ ] 问题能确定用户的基础水平
- [ ] 问题能明确用户的应用场景
- [ ] 问题数量适中（3-5个）

### 分解展示是否完整？
- [ ] 覆盖了领域的主要方向
- [ ] 每个方向都有简单说明
- [ ] 展示了用户可能不知道的内容
- [ ] 便于用户做出选择

### 三版本是否一致？
- [ ] A版本用户能轻松理解
- [ ] B版本AI能准确处理
- [ ] C版本确保理解一致

## 🔄 下一步

当用户选择了具体方向后，进入下一阶段：**路径规划阶段**

---

**注意**：这个阶段的关键是**引导发现**，不要急于给出完整答案，而是要帮助用户发现更多可能性，然后做出明智的选择。
