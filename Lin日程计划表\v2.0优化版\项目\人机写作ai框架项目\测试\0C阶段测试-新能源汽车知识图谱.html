<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新能源汽车知识图谱 - 零阶段探索</title>
    <style>
        /* 基础重置和全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 头部样式 */
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .current-position {
            display: inline-block;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* 主体布局 - 重新设计为三栏布局 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            grid-template-rows: 1fr auto;
            gap: 20px;
            flex: 1;
            min-height: 700px;
        }

        .visualization-container {
            display: grid;
            grid-template-rows: 1fr 300px;
            gap: 20px;
        }

        .visualization-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .visualization-area h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .knowledge-space {
            width: 100%;
            height: 500px;
            position: relative;
            border: 2px solid #e0e6ed;
            border-radius: 15px;
            background:
                radial-gradient(circle at 20% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(231, 76, 60, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 70%, rgba(243, 156, 18, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            overflow: hidden;
            perspective: 1000px;
            transform-style: preserve-3d;
        }

        .knowledge-space::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(90deg, rgba(52, 152, 219, 0.05) 1px, transparent 1px),
                linear-gradient(rgba(52, 152, 219, 0.05) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
        }

        /* 知识节点样式 - 增强3D效果 */
        .knowledge-node {
            position: absolute;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;
            transform-style: preserve-3d;
        }

        .node-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.9);
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.15),
                0 4px 10px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            background: linear-gradient(135deg, currentColor 0%, currentColor 100%);
        }

        .node-circle::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
            z-index: -1;
        }

        .node-circle::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            box-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
        }

        .node-label {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .node-year {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.7rem;
            color: #7f8c8d;
            font-weight: 500;
        }

        .knowledge-node:hover .node-circle {
            transform: scale(1.3) translateZ(10px);
            box-shadow:
                0 12px 35px rgba(0, 0, 0, 0.25),
                0 6px 15px rgba(0, 0, 0, 0.15);
        }

        .knowledge-node:hover .node-label {
            opacity: 1;
        }

        .node-year {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 100;
        }

        .knowledge-node:hover .node-year {
            opacity: 1;
        }

        .knowledge-node.current {
            animation: pulse 2s infinite;
        }

        .knowledge-node.selected {
            transform: scale(1.2);
            z-index: 20;
        }

        .knowledge-node.selected .node-circle {
            box-shadow:
                0 0 0 4px rgba(52, 152, 219, 0.3),
                0 12px 35px rgba(0, 0, 0, 0.25);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.15) translateZ(5px); }
        }

        /* 2D影响力路径视图 */
        .influence-path-view {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border-top: 3px solid #3498db;
        }

        .influence-path-view h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .path-canvas {
            width: 100%;
            height: 250px;
            border: 1px solid #e0e6ed;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
        }

        .path-node {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .path-node:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        .path-node.center {
            width: 50px;
            height: 50px;
            border: 3px solid #fff;
            box-shadow:
                0 0 0 3px rgba(52, 152, 219, 0.3),
                0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .path-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
            transform-origin: left center;
            opacity: 0.7;
            animation: flowPath 2s ease-in-out infinite;
        }

        @keyframes flowPath {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .path-info {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .path-info h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .path-info p {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* 连接线样式 */
        .connection-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .connection-line {
            stroke-width: 2;
            opacity: 0.6;
            transition: all 0.3s ease;
        }

        .connection-line.strong {
            stroke-width: 3;
            opacity: 0.9;
        }

        .connection-line.medium {
            stroke-width: 2;
            opacity: 0.7;
            stroke-dasharray: 5,5;
        }

        .connection-line.weak {
            stroke-width: 1;
            opacity: 0.5;
            stroke-dasharray: 2,3;
        }

        /* 信息面板样式 */
        .info-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            height: fit-content;
        }

        .info-panel h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 层次筛选按钮 */
        .layer-filters {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 25px;
        }

        .layer-btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .layer-btn.policy {
            background: #e74c3c;
            color: white;
        }

        .layer-btn.tech {
            background: #3498db;
            color: white;
        }

        .layer-btn.business {
            background: #f39c12;
            color: white;
        }

        .layer-btn.application {
            background: #27ae60;
            color: white;
        }

        .layer-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .layer-btn.inactive {
            opacity: 0.4;
            transform: none;
        }

        /* 增强的节点详情区域 */
        .node-details {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            border: 1px solid #e0e6ed;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .node-details.expanded {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .node-details h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .detail-close-btn {
            margin-left: auto;
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #7f8c8d;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .detail-close-btn:hover {
            background: #e74c3c;
            color: white;
        }

        .detail-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .detail-label {
            font-weight: 600;
            color: #34495e;
            min-width: 70px;
            font-size: 0.9rem;
        }

        .detail-value {
            flex: 1;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .detail-description {
            background: rgba(52, 152, 219, 0.05);
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #3498db;
            margin-top: 15px;
        }

        .detail-description p {
            color: #2c3e50;
            font-size: 0.95rem;
            line-height: 1.6;
            margin: 0;
        }

        .expand-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .expand-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .detailed-info {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            border: 1px solid #e0e6ed;
            display: none;
        }

        .detailed-info.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .info-section {
            margin-bottom: 15px;
        }

        .info-section h5 {
            color: #2c3e50;
            font-size: 0.95rem;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .info-section p {
            color: #7f8c8d;
            font-size: 0.85rem;
            line-height: 1.5;
            margin: 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .knowledge-space {
                height: 400px;
            }

            .layer-filters {
                grid-template-columns: 1fr;
            }

            .node-label {
                font-size: 0.7rem;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 1.1rem;
            color: #7f8c8d;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部：标题和说明 -->
        <div class="header">
            <h1>新能源汽车知识图谱</h1>
            <div class="subtitle">探索发展脉络，找到最佳切入点</div>
            <div class="current-position">
                📍 您当前位置：初学者 → 了解全貌 → 选择切入点
            </div>
        </div>
        
        <!-- 主体：可视化区域 + 信息面板 -->
        <div class="main-content">
            <div class="visualization-container">
                <!-- 3D知识空间 -->
                <div class="visualization-area">
                    <h2>🗺️ 知识空间全景</h2>
                    <div class="knowledge-space" id="knowledge-space">
                        <div class="loading">
                            <div class="spinner"></div>
                            正在加载知识图谱...
                        </div>
                        <!-- SVG连接线层 -->
                        <svg class="connection-layer" id="connection-layer"></svg>
                        <!-- 动态生成的知识节点将在这里插入 -->
                    </div>
                </div>

                <!-- 2D影响力路径视图 -->
                <div class="influence-path-view" id="influence-path-view" style="display: none;">
                    <h3>🔗 影响力路径分析</h3>
                    <div class="path-canvas" id="path-canvas">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #7f8c8d;">
                            点击知识节点查看影响力路径
                        </div>
                    </div>
                    <div class="path-info" id="path-info" style="display: none;">
                        <h4 id="path-title">影响力分析</h4>
                        <p id="path-description">选择节点后将显示其在知识网络中的影响力路径</p>
                    </div>
                </div>
            </div>
            
            <div class="info-panel">
                <h3>🎯 四层关注焦点</h3>
                
                <!-- 四层焦点选择器 -->
                <div class="layer-filters">
                    <button class="layer-btn policy" data-layer="policy">
                        🏛️ 政策层
                    </button>
                    <button class="layer-btn tech" data-layer="tech">
                        🔬 技术层
                    </button>
                    <button class="layer-btn business" data-layer="business">
                        💼 商业层
                    </button>
                    <button class="layer-btn application" data-layer="application">
                        📱 应用层
                    </button>
                </div>
                
                <!-- 增强的节点详情显示区 -->
                <div class="node-details" id="node-details" style="display: none;">
                    <h4 id="detail-title">
                        选择节点查看详情
                        <button class="detail-close-btn" id="detail-close-btn">×</button>
                    </h4>

                    <div class="detail-item">
                        <span class="detail-label">时间:</span>
                        <span class="detail-value" id="detail-year">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">层次:</span>
                        <span class="detail-value" id="detail-layer">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">影响力:</span>
                        <span class="detail-value" id="detail-impact">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">连接数:</span>
                        <span class="detail-value" id="detail-connections">-</span>
                    </div>

                    <div class="detail-description">
                        <p id="detail-description">点击知识节点查看详细信息</p>
                    </div>

                    <button class="expand-btn" id="expand-btn">查看详细分析</button>

                    <div class="detailed-info" id="detailed-info">
                        <div class="info-section">
                            <h5>📋 背景信息</h5>
                            <p id="detail-background">-</p>
                        </div>
                        <div class="info-section">
                            <h5>🎯 发展历程</h5>
                            <p id="detail-development">-</p>
                        </div>
                        <div class="info-section">
                            <h5>💡 影响分析</h5>
                            <p id="detail-influence">-</p>
                        </div>
                        <div class="info-section">
                            <h5>🔮 未来趋势</h5>
                            <p id="detail-future">-</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 完整的JavaScript功能 -->
    <script>
        // 模块2：数据注入与渲染

        // 阶段1：数据注入 - 新能源汽车领域知识图谱数据
        const knowledgeData = {
            domain: "新能源汽车",
            timeRange: { start: 1990, end: 2025 },

            // 节点数据
            nodes: [
                // 政策层节点
                {
                    id: "policy_1",
                    year: 2009,
                    layer: "policy",
                    title: "十城千辆工程",
                    impactLevel: 4,
                    position: { x: 25, y: 80, z: 0 },
                    style: { color: "#e74c3c", size: 16, opacity: 0.8 },
                    current: false,
                    description: "中国首个大规模新能源汽车推广示范工程",
                    detailedInfo: {
                        background: "2009年，科技部、财政部、发改委、工信部联合启动'十城千辆节能与新能源汽车示范推广应用工程'，计划用3年左右时间，每年发展10个城市，每个城市推出1000辆新能源汽车开展示范运行。",
                        development: "该工程分三个阶段实施：第一批13个试点城市（2009年）、第二批7个试点城市（2010年）、第三批5个试点城市（2012年），最终覆盖25个城市。",
                        influence: "为中国新能源汽车产业发展奠定了基础，培育了初期市场，推动了技术进步，为后续大规模推广积累了宝贵经验。",
                        future: "该工程的成功经验为后续的新能源汽车推广政策提供了重要参考，成为中国新能源汽车产业政策体系的重要起点。"
                    }
                },
                {
                    id: "policy_2",
                    year: 2012,
                    layer: "policy",
                    title: "新能源汽车产业发展规划",
                    impactLevel: 5,
                    position: { x: 35, y: 90, z: 0 },
                    style: { color: "#e74c3c", size: 18, opacity: 0.8 },
                    current: false,
                    description: "确立新能源汽车为战略性新兴产业"
                },
                {
                    id: "policy_3",
                    year: 2020,
                    layer: "policy",
                    title: "新能源汽车产业发展规划2021-2035",
                    impactLevel: 5,
                    position: { x: 75, y: 85, z: 0 },
                    style: { color: "#e74c3c", size: 18, opacity: 1.0 },
                    current: true,
                    description: "新时期新能源汽车产业发展的纲领性文件"
                },

                // 技术层节点
                {
                    id: "tech_1",
                    year: 1991,
                    layer: "tech",
                    title: "锂离子电池商业化",
                    impactLevel: 5,
                    position: { x: 5, y: 75, z: 25 },
                    style: { color: "#3498db", size: 18, opacity: 0.8 },
                    current: false,
                    description: "为电动汽车提供了可行的能源存储解决方案",
                    detailedInfo: {
                        background: "1991年，索尼公司首次实现锂离子电池的商业化生产，这一突破性技术为便携式电子设备和后来的电动汽车提供了高能量密度的储能解决方案。",
                        development: "从最初的小型电子设备应用，逐步发展到电动工具、电动自行车，最终在2000年代开始应用于电动汽车领域，技术不断成熟。",
                        influence: "锂离子电池的商业化是电动汽车产业发展的技术基石，没有这一技术突破，现代电动汽车产业就不可能存在。",
                        future: "随着固态电池、硅纳米线等新技术的发展，锂离子电池技术仍在不断进步，能量密度和安全性持续提升。"
                    }
                },
                {
                    id: "tech_2",
                    year: 2008,
                    layer: "tech",
                    title: "磷酸铁锂电池技术突破",
                    impactLevel: 4,
                    position: { x: 22, y: 65, z: 25 },
                    style: { color: "#3498db", size: 16, opacity: 0.8 },
                    current: false,
                    description: "提高了电池安全性和循环寿命"
                },
                {
                    id: "tech_3",
                    year: 2015,
                    layer: "tech",
                    title: "三元锂电池规模应用",
                    impactLevel: 4,
                    position: { x: 55, y: 70, z: 25 },
                    style: { color: "#3498db", size: 16, opacity: 0.8 },
                    current: false,
                    description: "提升了电池能量密度和续航里程"
                },
                {
                    id: "tech_4",
                    year: 2022,
                    layer: "tech",
                    title: "固态电池技术突破",
                    impactLevel: 5,
                    position: { x: 85, y: 80, z: 25 },
                    style: { color: "#3498db", size: 18, opacity: 1.0 },
                    current: true,
                    description: "下一代电池技术，有望解决安全和能量密度问题"
                },

                // 商业层节点
                {
                    id: "business_1",
                    year: 2003,
                    layer: "business",
                    title: "特斯拉成立",
                    impactLevel: 5,
                    position: { x: 15, y: 85, z: 50 },
                    style: { color: "#f39c12", size: 18, opacity: 0.8 },
                    current: false,
                    description: "重新定义了电动汽车的商业模式和用户体验",
                    detailedInfo: {
                        background: "2003年，马丁·艾伯哈德和马克·塔彭宁创立特斯拉汽车公司，以发明家尼古拉·特斯拉命名。2004年埃隆·马斯克投资并成为董事长。",
                        development: "从2008年Roadster跑车开始，到Model S、Model X、Model 3、Model Y的产品线扩展，特斯拉逐步从小众豪华品牌发展为大众电动汽车制造商。",
                        influence: "特斯拉彻底改变了人们对电动汽车的认知，证明了电动汽车可以兼具性能、续航和智能化，推动了整个汽车行业的电动化转型。",
                        future: "特斯拉继续在自动驾驶、能源存储、充电网络等领域创新，目标是加速世界向可持续能源的转变。"
                    }
                },
                {
                    id: "business_2",
                    year: 2014,
                    layer: "business",
                    title: "中国新能源车企崛起",
                    impactLevel: 4,
                    position: { x: 50, y: 60, z: 50 },
                    style: { color: "#f39c12", size: 16, opacity: 0.8 },
                    current: false,
                    description: "比亚迪、蔚来等本土品牌快速发展"
                },
                {
                    id: "business_3",
                    year: 2020,
                    layer: "business",
                    title: "传统车企电动化转型",
                    impactLevel: 4,
                    position: { x: 75, y: 55, z: 50 },
                    style: { color: "#f39c12", size: 16, opacity: 1.0 },
                    current: true,
                    description: "大众、通用等传统车企全面转向电动化"
                },

                // 应用层节点
                {
                    id: "app_1",
                    year: 2010,
                    layer: "application",
                    title: "公共交通电动化",
                    impactLevel: 3,
                    position: { x: 30, y: 40, z: 75 },
                    style: { color: "#27ae60", size: 14, opacity: 0.8 },
                    current: false,
                    description: "电动公交车开始规模化应用"
                },
                {
                    id: "app_2",
                    year: 2016,
                    layer: "application",
                    title: "网约车电动化",
                    impactLevel: 3,
                    position: { x: 60, y: 35, z: 75 },
                    style: { color: "#27ae60", size: 14, opacity: 0.8 },
                    current: false,
                    description: "滴滴等平台推动网约车电动化"
                },
                {
                    id: "app_3",
                    year: 2021,
                    layer: "application",
                    title: "私人消费市场爆发",
                    impactLevel: 5,
                    position: { x: 80, y: 45, z: 75 },
                    style: { color: "#27ae60", size: 18, opacity: 1.0 },
                    current: true,
                    description: "新能源汽车进入私人消费主流市场"
                }
            ],

            // 连接数据
            connections: [
                {
                    from: "tech_1",
                    to: "business_1",
                    type: "direct",
                    strength: 0.9,
                    style: { color: "#4CAF50", width: 3, opacity: 0.7 }
                },
                {
                    from: "policy_1",
                    to: "business_2",
                    type: "influence",
                    strength: 0.8,
                    style: { color: "#FF9800", width: 2, opacity: 0.6 }
                },
                {
                    from: "tech_2",
                    to: "app_1",
                    type: "enable",
                    strength: 0.7,
                    style: { color: "#2196F3", width: 2, opacity: 0.5 }
                },
                {
                    from: "policy_2",
                    to: "business_2",
                    type: "direct",
                    strength: 0.9,
                    style: { color: "#4CAF50", width: 3, opacity: 0.7 }
                },
                {
                    from: "tech_3",
                    to: "business_3",
                    type: "enable",
                    strength: 0.8,
                    style: { color: "#FF9800", width: 2, opacity: 0.6 }
                },
                {
                    from: "business_2",
                    to: "app_2",
                    type: "influence",
                    strength: 0.6,
                    style: { color: "#9C27B0", width: 1, opacity: 0.5 }
                },
                {
                    from: "policy_3",
                    to: "tech_4",
                    type: "direct",
                    strength: 0.8,
                    style: { color: "#4CAF50", width: 3, opacity: 0.7 }
                },
                {
                    from: "business_3",
                    to: "app_3",
                    type: "direct",
                    strength: 0.9,
                    style: { color: "#4CAF50", width: 3, opacity: 0.7 }
                }
            ],

            // 配置数据
            config: {
                colors: {
                    policy: "#e74c3c",
                    tech: "#3498db",
                    business: "#f39c12",
                    application: "#27ae60"
                },
                layout: { width: 800, height: 500, depth: 400 },
                interaction: { enableZoom: true, enableRotate: true, enableSelect: true }
            }
        };

        // 全局变量
        let activeLayer = null;
        let selectedNode = null;

        // 阶段2：节点渲染
        function generateNodes() {
            const knowledgeSpace = document.getElementById('knowledge-space');
            const loading = knowledgeSpace.querySelector('.loading');

            // 移除加载提示
            if (loading) {
                loading.remove();
            }

            knowledgeData.nodes.forEach(node => {
                // 创建节点DOM元素
                const nodeElement = document.createElement('div');
                nodeElement.className = 'knowledge-node';
                nodeElement.setAttribute('data-id', node.id);
                nodeElement.setAttribute('data-layer', node.layer);

                // 设置位置
                nodeElement.style.left = `${node.position.x}%`;
                nodeElement.style.top = `${node.position.y}%`;

                // 创建节点圆圈
                const circle = document.createElement('div');
                circle.className = 'node-circle';
                circle.style.backgroundColor = node.style.color;
                circle.style.width = `${node.style.size}px`;
                circle.style.height = `${node.style.size}px`;
                circle.style.opacity = node.style.opacity;

                // 创建节点标签
                const label = document.createElement('div');
                label.className = 'node-label';
                label.textContent = node.title;

                // 创建年份标签
                const year = document.createElement('div');
                year.className = 'node-year';
                year.textContent = node.year;

                // 组装节点
                nodeElement.appendChild(circle);
                nodeElement.appendChild(label);
                nodeElement.appendChild(year);

                // 添加当前节点标识
                if (node.current) {
                    nodeElement.classList.add('current');
                }

                // 添加事件监听
                nodeElement.addEventListener('click', () => showNodeDetails(node));

                // 插入到容器
                knowledgeSpace.appendChild(nodeElement);
            });
        }

        // 阶段3：连接线渲染
        function renderConnections() {
            const svg = document.getElementById('connection-layer');
            const knowledgeSpace = document.getElementById('knowledge-space');
            const rect = knowledgeSpace.getBoundingClientRect();

            // 设置SVG尺寸
            svg.setAttribute('width', '100%');
            svg.setAttribute('height', '100%');

            knowledgeData.connections.forEach(connection => {
                const fromNode = knowledgeData.nodes.find(n => n.id === connection.from);
                const toNode = knowledgeData.nodes.find(n => n.id === connection.to);

                if (fromNode && toNode) {
                    // 计算连接线坐标
                    const x1 = fromNode.position.x;
                    const y1 = fromNode.position.y;
                    const x2 = toNode.position.x;
                    const y2 = toNode.position.y;

                    // 创建连接线
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', `${x1}%`);
                    line.setAttribute('y1', `${y1}%`);
                    line.setAttribute('x2', `${x2}%`);
                    line.setAttribute('y2', `${y2}%`);
                    line.setAttribute('stroke', connection.style.color);
                    line.setAttribute('stroke-width', connection.style.width);
                    line.setAttribute('opacity', connection.style.opacity);

                    // 根据强度设置样式
                    if (connection.strength >= 0.8) {
                        line.classList.add('strong');
                    } else if (connection.strength >= 0.6) {
                        line.classList.add('medium');
                    } else {
                        line.classList.add('weak');
                    }

                    svg.appendChild(line);
                }
            });
        }

        // 模块3：交互功能实现

        // 阶段1：核心交互功能

        // 第1步：基础节点详情显示功能
        function showNodeDetails(node) {
            console.log('📋 显示节点详情:', node.title);
            selectedNode = node;

            try {
                const detailsPanel = document.getElementById('node-details');
                const title = document.getElementById('detail-title');
                const year = document.getElementById('detail-year');
                const layer = document.getElementById('detail-layer');
                const impact = document.getElementById('detail-impact');
                const connections = document.getElementById('detail-connections');
                const description = document.getElementById('detail-description');

                // 计算连接数
                const nodeConnections = knowledgeData.connections.filter(
                    conn => conn.from === node.id || conn.to === node.id
                ).length;

                // 更新基础详情内容
                title.innerHTML = `${node.title} <button class="detail-close-btn" id="detail-close-btn">×</button>`;
                year.textContent = node.year;
                layer.textContent = getLayerName(node.layer);
                impact.textContent = '★'.repeat(node.impactLevel) + '☆'.repeat(5 - node.impactLevel);
                connections.textContent = nodeConnections;
                description.textContent = node.description;

                // 更新详细信息（如果存在）
                if (node.detailedInfo) {
                    const backgroundEl = document.getElementById('detail-background');
                    const developmentEl = document.getElementById('detail-development');
                    const influenceEl = document.getElementById('detail-influence');
                    const futureEl = document.getElementById('detail-future');

                    if (backgroundEl) backgroundEl.textContent = node.detailedInfo.background;
                    if (developmentEl) developmentEl.textContent = node.detailedInfo.development;
                    if (influenceEl) influenceEl.textContent = node.detailedInfo.influence;
                    if (futureEl) futureEl.textContent = node.detailedInfo.future;
                }

                // 显示详情面板
                detailsPanel.style.display = 'block';
                detailsPanel.classList.add('expanded');

                // 高亮选中节点
                highlightSelectedNode(node.id);

                // 第2步功能：显示2D影响力路径（如果功能已启用）
                if (typeof show2DInfluencePath === 'function') {
                    show2DInfluencePath(node);
                }

                // 绑定关闭按钮事件
                const closeBtn = document.getElementById('detail-close-btn');
                if (closeBtn) {
                    closeBtn.addEventListener('click', closeNodeDetails);
                }

                console.log('✅ 节点详情显示成功');
            } catch (error) {
                console.error('❌ 显示节点详情时出错:', error);
            }
        }

        // 关闭节点详情
        function closeNodeDetails() {
            console.log('🔒 关闭节点详情面板');

            const detailsPanel = document.getElementById('node-details');
            const influenceView = document.getElementById('influence-path-view');

            if (detailsPanel) {
                detailsPanel.style.display = 'none';
                detailsPanel.classList.remove('expanded');
            }

            if (influenceView) {
                influenceView.style.display = 'none';
            }

            // 移除选中状态
            document.querySelectorAll('.knowledge-node').forEach(node => {
                node.classList.remove('selected');
                node.style.transform = '';
                node.style.zIndex = '10';
            });

            selectedNode = null;
            console.log('✅ 节点详情面板已关闭，可以重新点击其他节点');

            // 显示重新开启提示
            showReopenHint();
        }

        // 显示重新开启提示
        function showReopenHint() {
            // 检查是否已存在提示
            let hint = document.getElementById('reopen-hint');
            if (hint) {
                hint.remove();
            }

            // 创建重新开启提示
            hint = document.createElement('div');
            hint.id = 'reopen-hint';
            hint.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(52, 152, 219, 0.9);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                z-index: 1000;
                font-size: 0.9rem;
                max-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;

            hint.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <div>
                        <strong>💡 提示</strong><br>
                        点击任意节点重新查看详情
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-left: 10px;">×</button>
                </div>
            `;

            document.body.appendChild(hint);

            // 3秒后自动消失
            setTimeout(() => {
                if (hint && hint.parentElement) {
                    hint.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (hint && hint.parentElement) {
                            hint.remove();
                        }
                    }, 300);
                }
            }, 3000);
        }

        // 获取层次中文名称
        function getLayerName(layer) {
            const layerNames = {
                'policy': '政策层',
                'tech': '技术层',
                'business': '商业层',
                'application': '应用层'
            };
            return layerNames[layer] || layer;
        }

        // 高亮选中节点
        function highlightSelectedNode(nodeId) {
            // 移除之前的高亮
            document.querySelectorAll('.knowledge-node').forEach(node => {
                node.classList.remove('selected');
                node.style.transform = '';
                node.style.zIndex = '10';
            });

            // 高亮当前选中节点
            if (nodeId) {
                const selectedElement = document.querySelector(`[data-id="${nodeId}"]`);
                if (selectedElement) {
                    selectedElement.classList.add('selected');
                    selectedElement.style.zIndex = '20';
                }
            }
        }

        // 层次筛选功能
        function setupLayerFilters() {
            const filterButtons = document.querySelectorAll('.layer-btn');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const layer = button.getAttribute('data-layer');
                    toggleLayer(layer, button);
                });
            });
        }

        // 切换层次显示
        function toggleLayer(layer, button) {
            const nodes = document.querySelectorAll(`[data-layer="${layer}"]`);
            const isActive = !button.classList.contains('inactive');

            if (isActive) {
                // 隐藏该层次
                button.classList.add('inactive');
                nodes.forEach(node => {
                    node.style.opacity = '0.2';
                    node.style.pointerEvents = 'none';
                });

                // 隐藏相关连接线
                hideConnectionsForLayer(layer);
            } else {
                // 显示该层次
                button.classList.remove('inactive');
                nodes.forEach(node => {
                    node.style.opacity = '';
                    node.style.pointerEvents = '';
                });

                // 显示相关连接线
                showConnectionsForLayer(layer);
            }
        }

        // 隐藏层次相关连接线
        function hideConnectionsForLayer(layer) {
            const layerNodes = knowledgeData.nodes.filter(n => n.layer === layer).map(n => n.id);
            const svg = document.getElementById('connection-layer');
            const lines = svg.querySelectorAll('line');

            knowledgeData.connections.forEach((connection, index) => {
                if (layerNodes.includes(connection.from) || layerNodes.includes(connection.to)) {
                    if (lines[index]) {
                        lines[index].style.opacity = '0.1';
                    }
                }
            });
        }

        // 显示层次相关连接线
        function showConnectionsForLayer(layer) {
            const layerNodes = knowledgeData.nodes.filter(n => n.layer === layer).map(n => n.id);
            const svg = document.getElementById('connection-layer');
            const lines = svg.querySelectorAll('line');

            knowledgeData.connections.forEach((connection, index) => {
                if (layerNodes.includes(connection.from) || layerNodes.includes(connection.to)) {
                    if (lines[index]) {
                        lines[index].style.opacity = connection.style.opacity;
                    }
                }
            });
        }

        // 阶段2：增强功能和优化

        // 响应式适配
        function setupResponsiveFeatures() {
            // 检测移动设备
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // 移动端优化
                document.querySelectorAll('.knowledge-node').forEach(node => {
                    const circle = node.querySelector('.node-circle');
                    const currentSize = parseInt(circle.style.width);
                    circle.style.width = `${Math.max(currentSize, 20)}px`;
                    circle.style.height = `${Math.max(currentSize, 20)}px`;
                });

                // 调整连接线粗细
                document.querySelectorAll('line').forEach(line => {
                    const currentWidth = parseInt(line.getAttribute('stroke-width'));
                    line.setAttribute('stroke-width', Math.max(currentWidth, 2));
                });
            }
        }

        // 窗口大小变化处理
        function handleResize() {
            setupResponsiveFeatures();
            // 重新渲染连接线以适应新尺寸
            const svg = document.getElementById('connection-layer');
            svg.innerHTML = '';
            renderConnections();
        }

        // 键盘支持
        function setupKeyboardSupport() {
            document.addEventListener('keydown', (e) => {
                switch(e.key) {
                    case 'Escape':
                        // ESC键关闭详情面板
                        closeNodeDetails();
                        break;
                    case '1':
                        // 数字键快速切换层次
                        document.querySelector('[data-layer="policy"]').click();
                        break;
                    case '2':
                        document.querySelector('[data-layer="tech"]').click();
                        break;
                    case '3':
                        document.querySelector('[data-layer="business"]').click();
                        break;
                    case '4':
                        document.querySelector('[data-layer="application"]').click();
                        break;
                }
            });
        }

        // 动画效果
        function addAnimationEffects() {
            // 节点出现动画
            const nodes = document.querySelectorAll('.knowledge-node');
            nodes.forEach((node, index) => {
                node.style.opacity = '0';
                node.style.transform = 'scale(0)';

                setTimeout(() => {
                    node.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                    node.style.opacity = '';
                    node.style.transform = '';
                }, index * 100);
            });

            // 连接线出现动画
            const lines = document.querySelectorAll('line');
            lines.forEach((line, index) => {
                const length = Math.sqrt(
                    Math.pow(line.getAttribute('x2') - line.getAttribute('x1'), 2) +
                    Math.pow(line.getAttribute('y2') - line.getAttribute('y1'), 2)
                );

                line.style.strokeDasharray = `${length} ${length}`;
                line.style.strokeDashoffset = length;

                setTimeout(() => {
                    line.style.transition = 'stroke-dashoffset 1s ease-in-out';
                    line.style.strokeDashoffset = '0';
                }, 500 + index * 50);
            });
        }

        // 显示2D影响力路径
        function show2DInfluencePath(node) {
            const pathCanvas = document.getElementById('path-canvas');
            const influenceView = document.getElementById('influence-path-view');
            const pathInfo = document.getElementById('path-info');
            const pathTitle = document.getElementById('path-title');
            const pathDescription = document.getElementById('path-description');

            // 清空画布
            pathCanvas.innerHTML = '';

            // 获取与该节点相关的连接
            const relatedConnections = knowledgeData.connections.filter(
                conn => conn.from === node.id || conn.to === node.id
            );

            // 如果没有连接，显示提示信息
            if (relatedConnections.length === 0) {
                pathCanvas.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #7f8c8d;">该节点没有影响力连接</div>';
                influenceView.style.display = 'block';
                pathInfo.style.display = 'none';
                return;
            }

            // 收集相关节点
            const relatedNodeIds = new Set();
            relatedConnections.forEach(conn => {
                relatedNodeIds.add(conn.from);
                relatedNodeIds.add(conn.to);
            });

            const relatedNodes = knowledgeData.nodes.filter(n => relatedNodeIds.has(n.id));

            // 创建中心节点
            const centerNode = document.createElement('div');
            centerNode.className = 'path-node center';
            centerNode.style.backgroundColor = node.style.color;
            centerNode.style.left = '50%';
            centerNode.style.top = '50%';
            centerNode.style.transform = 'translate(-50%, -50%)';
            centerNode.textContent = node.year;
            centerNode.setAttribute('title', node.title);
            pathCanvas.appendChild(centerNode);

            // 创建相关节点和连接线
            relatedConnections.forEach((conn, index) => {
                const isSource = conn.from === node.id;
                const otherNodeId = isSource ? conn.to : conn.from;
                const otherNode = knowledgeData.nodes.find(n => n.id === otherNodeId);

                if (!otherNode) return;

                // 计算位置（围绕中心节点的圆形布局）
                const nodeAngle = (index * (360 / relatedConnections.length)) * (Math.PI / 180);
                const radius = 100; // 圆的半径
                const x = 50 + Math.cos(nodeAngle) * radius / 2; // 百分比位置
                const y = 50 + Math.sin(nodeAngle) * radius / 2; // 百分比位置

                // 创建相关节点
                const nodeElement = document.createElement('div');
                nodeElement.className = 'path-node';
                nodeElement.style.backgroundColor = otherNode.style.color;
                nodeElement.style.left = `${x}%`;
                nodeElement.style.top = `${y}%`;
                nodeElement.style.transform = 'translate(-50%, -50%)';
                nodeElement.textContent = otherNode.year;
                nodeElement.setAttribute('title', otherNode.title);
                nodeElement.addEventListener('click', () => showNodeDetails(otherNode));
                pathCanvas.appendChild(nodeElement);

                // 创建连接线
                const connection = document.createElement('div');
                connection.className = 'path-connection';

                // 计算连接线位置和角度
                const centerX = 50;
                const centerY = 50;
                const dx = x - centerX;
                const dy = y - centerY;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const connectionAngle = Math.atan2(dy, dx) * (180 / Math.PI);

                connection.style.width = `${distance}%`;
                connection.style.left = `${centerX}%`;
                connection.style.top = `${centerY}%`;
                connection.style.transform = `rotate(${connectionAngle}deg)`;

                // 设置连接线样式
                connection.style.backgroundColor = conn.style.color;
                connection.style.height = `${conn.style.width}px`;
                connection.style.opacity = conn.style.opacity;

                // 设置箭头方向
                if (!isSource) {
                    connection.style.transform = `rotate(${connectionAngle + 180}deg)`;
                }

                pathCanvas.appendChild(connection);
            });

            // 生成详细的连接关系说明
            generateDetailedConnectionInfo(node, relatedConnections);

            // 显示影响力视图和信息
            influenceView.style.display = 'block';
            pathInfo.style.display = 'block';
        }

        // 生成详细的连接关系信息
        function generateDetailedConnectionInfo(centerNode, connections) {
            const pathTitle = document.getElementById('path-title');
            const pathDescription = document.getElementById('path-description');

            // 更新标题
            pathTitle.textContent = `🔗 ${centerNode.title} 的影响力网络分析`;

            // 生成详细描述
            let description = `
                <div class="detailed-connection-info">
                    <div class="node-summary" style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">📊 节点概览</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9rem;">
                            <div><strong>时间节点：</strong>${centerNode.year}年</div>
                            <div><strong>影响层次：</strong>${getLayerName(centerNode.layer)}</div>
                            <div><strong>影响力等级：</strong>${'★'.repeat(centerNode.impactLevel)}${'☆'.repeat(5 - centerNode.impactLevel)}</div>
                            <div><strong>直接连接：</strong>${connections.length}个节点</div>
                        </div>
                    </div>

                    <div class="connections-analysis">
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">🔍 连接关系详析</h4>
            `;

            // 分析连接类型
            const influenceOut = connections.filter(c => c.from === centerNode.id);
            const influenceIn = connections.filter(c => c.to === centerNode.id);

            if (influenceOut.length > 0) {
                description += `
                    <div class="influence-out" style="margin-bottom: 15px;">
                        <h5 style="color: #e74c3c;">📤 对外影响 (${influenceOut.length}个)</h5>
                `;

                influenceOut.forEach(conn => {
                    const targetNode = knowledgeData.nodes.find(n => n.id === conn.to);
                    if (targetNode) {
                        const timeGap = targetNode.year - centerNode.year;
                        description += `
                            <div style="margin: 8px 0; padding: 8px; background: rgba(231, 76, 60, 0.1); border-left: 3px solid #e74c3c; border-radius: 5px;">
                                <strong style="color: ${targetNode.style.color};">${targetNode.title} (${targetNode.year})</strong>
                                <div style="font-size: 0.85rem; color: #666; margin-top: 3px;">
                                    ${getConnectionTypeDescription(conn.type)} •
                                    ${timeGap > 0 ? `${timeGap}年后实现` : '同期发生'} •
                                    影响强度: ${getConnectionStrength(conn.style.width)}
                                </div>
                            </div>
                        `;
                    }
                });

                description += `</div>`;
            }

            if (influenceIn.length > 0) {
                description += `
                    <div class="influence-in" style="margin-bottom: 15px;">
                        <h5 style="color: #3498db;">📥 受到影响 (${influenceIn.length}个)</h5>
                `;

                influenceIn.forEach(conn => {
                    const sourceNode = knowledgeData.nodes.find(n => n.id === conn.from);
                    if (sourceNode) {
                        const timeGap = centerNode.year - sourceNode.year;
                        description += `
                            <div style="margin: 8px 0; padding: 8px; background: rgba(52, 152, 219, 0.1); border-left: 3px solid #3498db; border-radius: 5px;">
                                <strong style="color: ${sourceNode.style.color};">${sourceNode.title} (${sourceNode.year})</strong>
                                <div style="font-size: 0.85rem; color: #666; margin-top: 3px;">
                                    ${getConnectionTypeDescription(conn.type)} •
                                    ${timeGap > 0 ? `基于${timeGap}年前的基础` : '同期互动'} •
                                    依赖程度: ${getConnectionStrength(conn.style.width)}
                                </div>
                            </div>
                        `;
                    }
                });

                description += `</div>`;
            }

            // 添加影响力总结
            description += `
                    </div>
                    <div class="impact-summary" style="background: rgba(46, 204, 113, 0.1); padding: 15px; border-radius: 10px; margin-top: 15px;">
                        <h4 style="color: #27ae60; margin-bottom: 10px;">💡 影响力总结</h4>
                        <p style="line-height: 1.6; color: #2c3e50;">${generateImpactSummary(centerNode, influenceOut, influenceIn)}</p>
                    </div>
                </div>
            `;

            pathDescription.innerHTML = description;
        }

        // 获取连接类型的中文描述
        function getConnectionTypeDescription(type) {
            const types = {
                'influence': '直接影响',
                'enable': '技术支撑',
                'compete': '竞争关系',
                'cooperate': '合作发展',
                'derive': '衍生发展'
            };
            return types[type] || '相关联系';
        }

        // 获取连接强度描述
        function getConnectionStrength(width) {
            if (width >= 4) return '强 ●●●';
            if (width >= 3) return '中 ●●○';
            return '弱 ●○○';
        }

        // 生成影响力总结
        function generateImpactSummary(node, outConnections, inConnections) {
            let summary = `${node.title}`;

            if (outConnections.length > inConnections.length) {
                summary += `是新能源汽车发展的重要推动力，主要影响了${outConnections.length}个关键发展节点`;
            } else if (inConnections.length > outConnections.length) {
                summary += `是多个前期发展成果的集成体现，受到${inConnections.length}个重要因素的共同推动`;
            } else if (outConnections.length > 0 && inConnections.length > 0) {
                summary += `在产业发展中起到承上启下的关键作用，既承接了前期的技术积累，又推动了后续的创新发展`;
            } else {
                summary += `是一个相对独立的发展节点，可能代表了某个特定领域的突破`;
            }

            // 根据影响力等级添加评价
            if (node.impactLevel >= 5) {
                summary += `，具有里程碑式的重大意义，深刻改变了整个行业的发展轨迹。`;
            } else if (node.impactLevel >= 4) {
                summary += `，在行业发展中具有重要的战略意义。`;
            } else if (node.impactLevel >= 3) {
                summary += `，为行业发展做出了重要贡献。`;
            } else {
                summary += `，在特定领域发挥了积极作用。`;
            }

            return summary;
        }

        // 详细信息展开/收起功能
        function setupDetailedInfoToggle() {
            console.log('🔧 设置详细信息切换功能...');

            const expandBtn = document.getElementById('expand-btn');
            const detailedInfo = document.getElementById('detailed-info');

            if (!expandBtn) {
                console.warn('⚠️ 找不到expand-btn元素，详细信息切换功能将在节点点击时设置');
                return;
            }

            if (!detailedInfo) {
                console.warn('⚠️ 找不到detailed-info元素');
                return;
            }

            expandBtn.addEventListener('click', function() {
                if (detailedInfo.classList.contains('show')) {
                    detailedInfo.classList.remove('show');
                    expandBtn.textContent = '查看详细分析';
                    console.log('📖 详细信息已收起');
                } else {
                    detailedInfo.classList.add('show');
                    expandBtn.textContent = '收起详细分析';
                    console.log('📖 详细信息已展开');
                }
            });

            console.log('✅ 详细信息切换功能设置完成');
        }

        // 第1步：基础初始化 - 只加载3D视图
        function initializeVisualization() {
            console.log('🚀 第1步：开始初始化基础3D知识图谱...');
            console.log('📊 数据检查:', {
                节点数量: knowledgeData.nodes.length,
                连接数量: knowledgeData.connections.length,
                领域: knowledgeData.domain
            });

            try {
                // 检查DOM元素
                const knowledgeSpace = document.getElementById('knowledge-space');
                const connectionLayer = document.getElementById('connection-layer');

                if (!knowledgeSpace) {
                    throw new Error('找不到knowledge-space元素');
                }
                if (!connectionLayer) {
                    throw new Error('找不到connection-layer元素');
                }

                console.log('✅ DOM元素检查通过');

                // 生成节点和连接
                generateNodes();
                console.log('✅ 节点生成任务已启动');

                // 延迟渲染连接线，等待节点生成完成
                setTimeout(() => {
                    renderConnections();
                    console.log('✅ 连接线渲染完成');
                }, knowledgeData.nodes.length * 100 + 200);

                // 设置基础交互功能
                setupLayerFilters();
                console.log('✅ 层次筛选功能设置完成');

                setupResponsiveFeatures();
                console.log('✅ 响应式功能设置完成');

                setupKeyboardSupport();
                console.log('✅ 键盘支持设置完成');

                // 监听窗口大小变化
                window.addEventListener('resize', handleResize);

                console.log('🎉 第1步完成：基础3D知识图谱初始化成功！');
                console.log('💡 提示：点击节点查看详情，使用按钮筛选层次，按ESC关闭详情');

                // 延迟执行第2步
                setTimeout(() => {
                    console.log('🚀 准备执行第2步：添加详细信息功能...');
                    try {
                        setupDetailedInfoToggle();
                        console.log('✅ 第2步完成：详细信息功能已添加');
                    } catch (error) {
                        console.error('❌ 第2步执行失败:', error);
                    }
                }, 2000);

            } catch (error) {
                console.error('❌ 初始化过程中出现错误:', error);
                // 显示错误信息给用户
                const knowledgeSpace = document.getElementById('knowledge-space');
                if (knowledgeSpace) {
                    knowledgeSpace.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #e74c3c; text-align: center;">
                            <div>
                                <h3>❌ 初始化失败</h3>
                                <p>错误信息: ${error.message}</p>
                                <p>请检查浏览器控制台获取详细信息</p>
                            </div>
                        </div>
                    `;
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeVisualization);

        console.log('交互功能实现完成');
    </script>
</body>
</html>
