<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新能源汽车知识领域全景图 - 优化版专业模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .container {
            display: grid;
            grid-template-rows: auto 2fr 1fr;
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .current-position {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            display: inline-block;
            font-size: 1.1em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .visualization-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
            position: relative;
        }
        
        .visualization-area h2 {
            color: #495057;
            margin-bottom: 25px;
            font-size: 1.5em;
            text-align: center;
            border-bottom: 2px solid #27ae60;
            padding-bottom: 10px;
        }
        
        .knowledge-space {
            position: relative;
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .knowledge-node {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
            border: 2px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .knowledge-node:hover {
            transform: scale(1.5);
            z-index: 100;
            box-shadow: 0 5px 20px rgba(0,0,0,0.4);
        }
        
        .knowledge-node.current {
            width: 30px;
            height: 30px;
            border: 3px solid #ffd700;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 10px rgba(255, 215, 0, 0.6); }
            50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.9); }
        }
        
        .policy { background: #e74c3c; }
        .tech { background: #3498db; }
        .business { background: #f39c12; }
        .application { background: #27ae60; }
        
        .node-label {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 11px;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            transform: translateX(-50%);
            top: -35px;
            left: 50%;
        }
        
        .knowledge-node:hover .node-label {
            opacity: 1;
        }
        
        .dimension-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .dimension-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            text-align: center;
        }
        
        .dimension-card:hover {
            transform: translateY(-3px);
        }
        
        .dimension-card.core { border-left-color: #007bff; }
        .dimension-card.evolution { border-left-color: #28a745; }
        .dimension-card.ecosystem { border-left-color: #ffc107; }
        .dimension-card.application { border-left-color: #dc3545; }
        .dimension-card.challenge { border-left-color: #6f42c1; }
        .dimension-card.opportunity { border-left-color: #20c997; }
        
        .dimension-card .icon {
            font-size: 1.5em;
            margin-bottom: 8px;
            display: block;
        }
        
        .dimension-card h4 {
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .dimension-card p {
            color: #6c757d;
            font-size: 0.8em;
        }
        
        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
            height: fit-content;
        }
        
        .info-panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        
        .focus-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .focus-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .focus-item.policy { border-left-color: #e74c3c; }
        .focus-item.tech { border-left-color: #3498db; }
        .focus-item.business { border-left-color: #f39c12; }
        .focus-item.application { border-left-color: #27ae60; }
        
        .focus-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .focus-icon.policy { background: #e74c3c; }
        .focus-icon.tech { background: #3498db; }
        .focus-icon.business { background: #f39c12; }
        .focus-icon.application { background: #27ae60; }
        
        .focus-content h4 {
            margin-bottom: 5px;
            color: #495057;
        }
        
        .focus-content p {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .node-details {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #27ae60;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        
        .timeline-section {
            padding: 30px;
            text-align: center;
        }
        
        .timeline-section h3 {
            color: #495057;
            margin-bottom: 20px;
        }
        
        .enhanced-timeline {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .timeline-period {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .timeline-period.current {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
        }
        
        .action-section {
            padding: 30px;
            text-align: center;
        }
        
        .action-section h3 {
            color: #495057;
            margin-bottom: 20px;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: #27ae60;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        
        .action-btn:hover {
            background: #219a52;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 新能源汽车知识领域全景图</h1>
            <div class="subtitle">从政策驱动到技术突破的完整发展脉络</div>
            <div class="current-position">
                📍 当前位置：产业化加速期 (2020-2025)
            </div>
        </div>
        
        <div class="main-content">
            <div class="visualization-area">
                <h2>🗺️ 知识空间全景</h2>
                <div class="knowledge-space" id="knowledge-space">
                    <!-- 节点将通过JavaScript动态生成 -->
                </div>
                
                <div class="dimension-grid">
                    <div class="dimension-card core">
                        <span class="icon">🔋</span>
                        <h4>核心技术</h4>
                        <p>电池、电机、电控</p>
                    </div>
                    
                    <div class="dimension-card evolution">
                        <span class="icon">📈</span>
                        <h4>发展演进</h4>
                        <p>政策→技术→市场</p>
                    </div>
                    
                    <div class="dimension-card ecosystem">
                        <span class="icon">🌐</span>
                        <h4>产业生态</h4>
                        <p>整车厂、供应链、基础设施</p>
                    </div>
                    
                    <div class="dimension-card application">
                        <span class="icon">🚀</span>
                        <h4>应用场景</h4>
                        <p>乘用车、商用车、特种车</p>
                    </div>
                    
                    <div class="dimension-card challenge">
                        <span class="icon">⚠️</span>
                        <h4>挑战瓶颈</h4>
                        <p>续航、充电、成本</p>
                    </div>
                    
                    <div class="dimension-card opportunity">
                        <span class="icon">💡</span>
                        <h4>发展机遇</h4>
                        <p>智能化、网联化</p>
                    </div>
                </div>
            </div>
            
            <div class="info-panel">
                <h3>🎯 四层关注焦点</h3>
                
                <div class="focus-item policy" onclick="selectFocus('policy')">
                    <div class="focus-icon policy">🏛️</div>
                    <div class="focus-content">
                        <h4>政策监管层</h4>
                        <p>补贴政策、排放标准、产业规划</p>
                    </div>
                </div>
                
                <div class="focus-item tech" onclick="selectFocus('tech')">
                    <div class="focus-icon tech">🔬</div>
                    <div class="focus-content">
                        <h4>技术创新层</h4>
                        <p>电池技术、智能驾驶、充电技术</p>
                    </div>
                </div>
                
                <div class="focus-item business" onclick="selectFocus('business')">
                    <div class="focus-icon business">💼</div>
                    <div class="focus-content">
                        <h4>商业价值层</h4>
                        <p>市场规模、商业模式、投资机会</p>
                    </div>
                </div>
                
                <div class="focus-item application" onclick="selectFocus('application')">
                    <div class="focus-icon application">🚀</div>
                    <div class="focus-content">
                        <h4>应用服务层</h4>
                        <p>用户体验、服务生态、使用场景</p>
                    </div>
                </div>
                
                <div id="node-details" class="node-details">
                    <h4 id="detail-title">节点详情</h4>
                    <div id="detail-content"></div>
                </div>

                <!-- 信息空间导航器 -->
                <div class="info-space-navigator" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;">
                    <h3 style="color: #495057; margin-bottom: 15px; font-size: 1.1em;">🗺️ 三维信息空间</h3>

                    <div class="axis-info" style="margin-bottom: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.85em;">
                            <div style="display: flex; align-items: center;">
                                <span style="color: #e74c3c;">📅</span>
                                <span style="margin-left: 8px;"><strong>时间轴:</strong> 过去 ←→ 未来</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <span style="color: #3498db;">📊</span>
                                <span style="margin-left: 8px;"><strong>信息轴:</strong> 外在 ←→ 内在</span>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <span style="color: #f39c12;">🎯</span>
                                <span style="margin-left: 8px;"><strong>注意力轴:</strong> 概览 ←→ 深度</span>
                            </div>
                        </div>
                    </div>

                    <div class="region-filters" style="margin-bottom: 15px;">
                        <h5 style="color: #495057; margin-bottom: 10px; font-size: 0.9em;">🔍 区域筛选</h5>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 5px;">
                            <button class="region-btn" onclick="filterByRegion('历史')" style="padding: 5px 8px; font-size: 0.8em; border: 1px solid #dee2e6; background: white; border-radius: 4px; cursor: pointer;">📚 历史信息</button>
                            <button class="region-btn" onclick="filterByRegion('现在')" style="padding: 5px 8px; font-size: 0.8em; border: 1px solid #dee2e6; background: white; border-radius: 4px; cursor: pointer;">⚡ 现在信息</button>
                            <button class="region-btn" onclick="filterByRegion('外在')" style="padding: 5px 8px; font-size: 0.8em; border: 1px solid #dee2e6; background: white; border-radius: 4px; cursor: pointer;">🌍 外在信息</button>
                            <button class="region-btn" onclick="filterByRegion('内在')" style="padding: 5px 8px; font-size: 0.8em; border: 1px solid #dee2e6; background: white; border-radius: 4px; cursor: pointer;">🔬 内在信息</button>
                            <button class="region-btn" onclick="filterByRegion('概览')" style="padding: 5px 8px; font-size: 0.8em; border: 1px solid #dee2e6; background: white; border-radius: 4px; cursor: pointer;">👁️ 概览认知</button>
                            <button class="region-btn" onclick="filterByRegion('深度')" style="padding: 5px 8px; font-size: 0.8em; border: 1px solid #dee2e6; background: white; border-radius: 4px; cursor: pointer;">🎯 深度认知</button>
                        </div>
                        <button onclick="resetInfoSpaceView()" style="width: 100%; margin-top: 8px; padding: 5px; font-size: 0.8em; border: 1px solid #27ae60; background: #27ae60; color: white; border-radius: 4px; cursor: pointer;">🔄 重置视图</button>
                    </div>

                    <div class="current-node-position" id="current-node-position" style="display: none; padding: 10px; background: #e8f5e8; border-radius: 5px; font-size: 0.85em;">
                        <h5 style="color: #27ae60; margin-bottom: 5px;">📍 当前节点位置</h5>
                        <div id="position-info"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bottom-section">
            <div class="timeline-section">
                <h3>⏰ 发展时间线</h3>
                <div class="enhanced-timeline">
                    <div class="timeline-period">
                        <strong>1990-2008：</strong> 技术萌芽期
                    </div>
                    <div class="timeline-period">
                        <strong>2009-2015：</strong> 政策推动期
                    </div>
                    <div class="timeline-period current">
                        <strong>2016-2025：</strong> 产业化加速期 ⭐
                    </div>
                    <div class="timeline-period">
                        <strong>2025+：</strong> 智能化融合期
                    </div>
                </div>
            </div>
            
            <div class="action-section">
                <h3>🎯 下一步行动</h3>
                <div class="action-buttons">
                    <button class="action-btn" onclick="selectFocus('tech')">🔬 深入技术层</button>
                    <button class="action-btn" onclick="selectFocus('business')">💼 探索商业层</button>
                    <button class="action-btn" onclick="selectFocus('application')">🚀 关注应用层</button>
                    <button class="action-btn" onclick="selectFocus('policy')">🏛️ 了解政策层</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 新能源汽车领域配置 - 三维信息空间版
        const domainConfig = {
            title: "新能源汽车领域",
            nodes: [
                {
                    id: 1, title: "锂离子电池商业化", year: 1991, layer: "tech", x: 8, y: 70, current: false,
                    description: "为电动汽车提供了可行的能源存储解决方案",
                    impact: 5, connections: [2, 3],
                    infoSpace: {
                        time: 0.1,      // 历史信息
                        info: 0.8,      // 内在信息（核心技术）
                        attention: 0.6  // 偏深度认知
                    },
                    region: "历史-内在-深度",
                    details: {
                        background: "索尼公司首次实现锂离子电池商业化，为便携式电子设备提供高能量密度电源",
                        significance: "奠定了现代电动汽车动力电池的技术基础",
                        influence: "直接推动了后续电动汽车技术的发展可能性"
                    }
                },
                {
                    id: 2, title: "丰田普锐斯", year: 1997, layer: "application", x: 15, y: 80, current: false,
                    description: "全球首款量产混合动力汽车",
                    impact: 4, connections: [1, 3, 5],
                    infoSpace: {
                        time: 0.2,      // 历史信息
                        info: 0.4,      // 偏外在信息（市场应用）
                        attention: 0.3  // 概览认知
                    },
                    region: "历史-外在-概览",
                    details: {
                        background: "丰田汽车推出世界首款量产混合动力汽车，开创了新能源汽车商业化先河",
                        significance: "证明了电动化技术的商业可行性",
                        influence: "启发了全球汽车厂商对新能源技术的重视"
                    }
                },
                {
                    id: 3, title: "特斯拉成立", year: 2003, layer: "business", x: 25, y: 60, current: false,
                    description: "重新定义电动汽车的商业模式和用户体验",
                    impact: 5, connections: [1, 2, 5, 10],
                    infoSpace: {
                        time: 0.3,      // 历史信息
                        info: 0.5,      // 中等（商业模式）
                        attention: 0.7  // 深度认知
                    },
                    region: "历史-中性-深度",
                    details: {
                        background: "马丁·艾伯哈德和马克·塔彭宁创立特斯拉，专注于高性能电动汽车",
                        significance: "颠覆了传统汽车行业对电动车的认知",
                        influence: "推动了整个行业向电动化、智能化转型"
                    }
                },
                {
                    id: 4, title: "十城千辆工程", year: 2009, layer: "policy", x: 35, y: 20, current: false,
                    description: "中国首个大规模新能源汽车推广示范工程",
                    impact: 4, connections: [6, 7],
                    infoSpace: {
                        time: 0.4,      // 历史信息
                        info: 0.2,      // 外在信息（政策）
                        attention: 0.4  // 概览认知
                    },
                    region: "历史-外在-概览",
                    details: {
                        background: "中国政府启动新能源汽车示范推广试点工程，在10个城市推广1000辆新能源汽车",
                        significance: "标志着中国新能源汽车产业政策支持的开始",
                        influence: "为后续大规模产业化奠定了政策基础"
                    }
                },
                {
                    id: 5, title: "特斯拉Model S", year: 2012, layer: "application", x: 45, y: 75, current: false,
                    description: "高端电动汽车的里程碑产品",
                    impact: 4, connections: [2, 3, 7, 10],
                    infoSpace: {
                        time: 0.45,     // 历史信息
                        info: 0.6,      // 偏内在信息（产品技术）
                        attention: 0.5  // 中等认知
                    },
                    region: "历史-内在-中等",
                    details: {
                        background: "特斯拉推出Model S，续航里程超过400公里，重新定义了电动汽车的性能标准",
                        significance: "证明了纯电动汽车可以在性能上超越传统燃油车",
                        influence: "激发了全球对高性能电动汽车的需求"
                    }
                },
                {
                    id: 6, title: "新能源汽车推广", year: 2013, layer: "policy", x: 50, y: 15, current: false,
                    description: "中国新能源汽车产业发展规划",
                    impact: 4, connections: [4, 7, 8],
                    infoSpace: {
                        time: 0.46,     // 历史信息
                        info: 0.1,      // 外在信息（政策规划）
                        attention: 0.6  // 偏深度认知
                    },
                    region: "历史-外在-深度",
                    details: {
                        background: "中国政府发布新能源汽车产业发展规划，明确发展目标和支持政策",
                        significance: "确立了中国在新能源汽车领域的战略地位",
                        influence: "推动了中国新能源汽车产业的快速发展"
                    }
                },
                {
                    id: 7, title: "比亚迪崛起", year: 2015, layer: "business", x: 55, y: 50, current: false,
                    description: "中国新能源汽车领军企业的崛起",
                    impact: 4, connections: [4, 5, 6, 9, 11],
                    infoSpace: {
                        time: 0.5,      // 历史信息
                        info: 0.4,      // 偏外在信息（商业竞争）
                        attention: 0.4  // 概览认知
                    },
                    region: "历史-外在-概览",
                    details: {
                        background: "比亚迪凭借电池技术优势和政策支持，成为全球新能源汽车销量领先企业",
                        significance: "证明了中国企业在新能源汽车领域的竞争力",
                        influence: "推动了中国新能源汽车产业链的完善"
                    }
                },
                {
                    id: 8, title: "双积分政策", year: 2017, layer: "policy", x: 65, y: 10, current: false,
                    description: "强制性新能源汽车发展政策",
                    impact: 5, connections: [6, 9, 11],
                    infoSpace: {
                        time: 0.55,     // 历史信息
                        info: 0.15,     // 外在信息（政策机制）
                        attention: 0.8  // 深度认知
                    },
                    region: "历史-外在-深度",
                    details: {
                        background: "中国实施乘用车企业平均燃料消耗量与新能源汽车积分并行管理办法",
                        significance: "通过市场化手段强制推动新能源汽车发展",
                        influence: "迫使所有车企必须发展新能源汽车业务"
                    }
                },
                {
                    id: 9, title: "宁德时代IPO", year: 2018, layer: "business", x: 70, y: 45, current: false,
                    description: "全球动力电池龙头企业上市",
                    impact: 4, connections: [7, 8, 10, 12],
                    infoSpace: {
                        time: 0.58,     // 历史信息
                        info: 0.7,      // 内在信息（核心企业）
                        attention: 0.5  // 中等认知
                    },
                    region: "历史-内在-中等",
                    details: {
                        background: "宁德时代在深交所创业板上市，成为全球动力电池市场份额第一的企业",
                        significance: "标志着中国在动力电池领域的全球领先地位",
                        influence: "推动了全球动力电池技术的快速发展"
                    }
                },
                {
                    id: 10, title: "特斯拉上海工厂", year: 2019, layer: "business", x: 75, y: 65, current: true,
                    description: "外资新能源汽车企业在华本土化生产",
                    impact: 5, connections: [3, 5, 9, 11],
                    infoSpace: {
                        time: 0.6,      // 现在信息
                        info: 0.3,      // 外在信息（市场布局）
                        attention: 0.6  // 偏深度认知
                    },
                    region: "现在-外在-深度",
                    details: {
                        background: "特斯拉上海超级工厂投产，成为中国首个外商独资汽车制造项目",
                        significance: "加速了中国新能源汽车市场的竞争和发展",
                        influence: "推动了中国新能源汽车产业链的升级"
                    }
                },
                {
                    id: 11, title: "传统车企电动化转型", year: 2020, layer: "business", x: 80, y: 55, current: true,
                    description: "全球传统汽车制造商的战略转型",
                    impact: 5, connections: [7, 8, 10, 12, 13],
                    infoSpace: {
                        time: 0.65,     // 现在信息
                        info: 0.4,      // 偏外在信息（行业转型）
                        attention: 0.7  // 深度认知
                    },
                    region: "现在-外在-深度",
                    details: {
                        background: "大众、通用、福特等传统车企宣布全面电动化转型战略",
                        significance: "标志着汽车行业进入电动化转型的关键时期",
                        influence: "加速了全球新能源汽车技术和市场的发展"
                    }
                },
                {
                    id: 12, title: "固态电池突破", year: 2023, layer: "tech", x: 85, y: 85, current: true,
                    description: "下一代动力电池技术的重大突破",
                    impact: 5, connections: [9, 11, 13],
                    infoSpace: {
                        time: 0.75,     // 现在信息
                        info: 0.9,      // 内在信息（核心技术）
                        attention: 0.9  // 深度认知
                    },
                    region: "现在-内在-深度",
                    details: {
                        background: "多家企业在固态电池技术方面取得重大突破，有望解决续航和安全问题",
                        significance: "可能彻底改变电动汽车的性能表现",
                        influence: "将推动新能源汽车进入新的发展阶段"
                    }
                },
                {
                    id: 13, title: "智能驾驶普及", year: 2024, layer: "application", x: 90, y: 75, current: true,
                    description: "自动驾驶技术在新能源汽车中的广泛应用",
                    impact: 4, connections: [11, 12, 14],
                    infoSpace: {
                        time: 0.8,      // 现在信息
                        info: 0.6,      // 偏内在信息（应用技术）
                        attention: 0.5  // 中等认知
                    },
                    region: "现在-内在-中等",
                    details: {
                        background: "L3级别自动驾驶技术开始在新能源汽车中普及应用",
                        significance: "新能源汽车与智能化技术深度融合",
                        influence: "重新定义了汽车的使用方式和价值"
                    }
                },
                {
                    id: 14, title: "碳中和目标", year: 2025, layer: "policy", x: 95, y: 25, current: false,
                    description: "全球碳中和目标推动新能源汽车发展",
                    impact: 5, connections: [13],
                    infoSpace: {
                        time: 0.85,     // 未来信息
                        info: 0.1,      // 外在信息（政策环境）
                        attention: 0.8  // 深度认知
                    },
                    region: "未来-外在-深度",
                    details: {
                        background: "全球主要国家和地区设定碳中和目标，新能源汽车成为重要实现路径",
                        significance: "为新能源汽车发展提供了长期政策保障",
                        influence: "将推动新能源汽车成为主流交通工具"
                    }
                }
            ]
        };

        // 生成知识节点 - 三维信息空间版
        function generateNodes() {
            const container = document.getElementById('knowledge-space');

            domainConfig.nodes.forEach(node => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `knowledge-node ${node.layer} ${node.current ? 'current' : ''}`;
                nodeElement.setAttribute('data-id', node.id);
                nodeElement.style.left = node.x + '%';
                nodeElement.style.top = node.y + '%';
                nodeElement.innerHTML = `<div class="node-label">${node.title} (${node.year})</div>`;

                nodeElement.addEventListener('click', () => showEnhancedNodeDetail(node));
                container.appendChild(nodeElement);
            });
        }

        // 显示增强的节点详情 - 包含三维信息空间
        function showEnhancedNodeDetail(node) {
            const detailDiv = document.getElementById('node-details');
            const title = document.getElementById('detail-title');
            const content = document.getElementById('detail-content');

            const layerNames = {
                'policy': '政策监管层',
                'tech': '技术创新层',
                'business': '商业价值层',
                'application': '应用服务层'
            };

            title.textContent = `${node.title} (${node.year}年)`;

            // 显示节点在信息空间中的位置
            showNodePosition(node);

            // 生成连接关系信息
            const connections = node.connections || [];
            const connectedNodes = connections.map(id =>
                domainConfig.nodes.find(n => n.id === id)
            ).filter(n => n);

            content.innerHTML = `
                <div style="margin-bottom: 15px;">
                    <p><strong>🏷️ 类型：</strong>${layerNames[node.layer]}</p>
                    <p><strong>📅 时间：</strong>${node.year}年</p>
                    <p><strong>⭐ 影响力：</strong>${'★'.repeat(node.impact)}${'☆'.repeat(5 - node.impact)}</p>
                    <p><strong>🔗 连接数：</strong>${connections.length}个节点</p>
                    ${node.current ? '<p><strong>🔥 这是当前关键节点！</strong></p>' : ''}
                </div>

                <div style="margin-bottom: 15px;">
                    <h5 style="color: #27ae60; margin-bottom: 8px;">📋 核心描述</h5>
                    <p style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 0.9em;">
                        ${node.description}
                    </p>
                </div>

                ${node.details ? `
                <div style="margin-bottom: 15px;">
                    <h5 style="color: #27ae60; margin-bottom: 8px;">🎯 背景信息</h5>
                    <p style="font-size: 0.85em; line-height: 1.5;">${node.details.background}</p>
                </div>

                <div style="margin-bottom: 15px;">
                    <h5 style="color: #27ae60; margin-bottom: 8px;">💡 重要意义</h5>
                    <p style="font-size: 0.85em; line-height: 1.5;">${node.details.significance}</p>
                </div>

                <div style="margin-bottom: 15px;">
                    <h5 style="color: #27ae60; margin-bottom: 8px;">🌊 影响作用</h5>
                    <p style="font-size: 0.85em; line-height: 1.5;">${node.details.influence}</p>
                </div>
                ` : ''}

                ${connectedNodes.length > 0 ? `
                <div style="margin-bottom: 15px;">
                    <h5 style="color: #27ae60; margin-bottom: 8px;">🔗 关联节点 (${connectedNodes.length}个)</h5>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                        ${connectedNodes.map(connNode => `
                            <span onclick="showEnhancedNodeDetail(domainConfig.nodes.find(n => n.id === ${connNode.id}))"
                                  style="background: ${getLayerColor(connNode.layer)}; color: white; padding: 4px 8px;
                                         border-radius: 12px; font-size: 0.8em; cursor: pointer;
                                         transition: all 0.3s ease;"
                                  onmouseover="this.style.transform='scale(1.1)'"
                                  onmouseout="this.style.transform='scale(1)'">
                                ${connNode.title} (${connNode.year})
                            </span>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                <div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border-radius: 5px;">
                    <h5 style="color: #27ae60; margin-bottom: 5px;">📊 发展阶段分析</h5>
                    <p style="font-size: 0.85em;">${generateStageAnalysis(node)}</p>
                </div>

                <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 5px;">
                    <h5 style="color: #3498db; margin-bottom: 8px;">🗺️ 信息空间坐标</h5>
                    <div style="font-size: 0.8em; line-height: 1.4;">
                        <p><strong>📅 时间维度:</strong> ${getTimeDescription(node.infoSpace.time)}</p>
                        <p><strong>📊 信息维度:</strong> ${getInfoDescription(node.infoSpace.info)}</p>
                        <p><strong>🎯 注意力维度:</strong> ${getAttentionDescription(node.infoSpace.attention)}</p>
                        <p><strong>🏷️ 所属区域:</strong> <span style="background: #3498db; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.75em;">${node.region}</span></p>
                    </div>
                </div>
            `;

            detailDiv.style.display = 'block';
            detailDiv.scrollTop = 0;
        }

        // 显示节点在信息空间中的位置
        function showNodePosition(node) {
            const positionDiv = document.getElementById('current-node-position');
            const positionInfo = document.getElementById('position-info');

            positionInfo.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr; gap: 5px;">
                    <div><strong>${node.title}</strong> 位于 <span style="background: #27ae60; color: white; padding: 2px 6px; border-radius: 8px; font-size: 0.8em;">${node.region}</span></div>
                    <div style="font-size: 0.8em; color: #666;">
                        时间: ${(node.infoSpace.time * 100).toFixed(0)}% |
                        信息: ${(node.infoSpace.info * 100).toFixed(0)}% |
                        注意力: ${(node.infoSpace.attention * 100).toFixed(0)}%
                    </div>
                </div>
            `;

            positionDiv.style.display = 'block';
        }

        // 获取时间维度描述
        function getTimeDescription(timeValue) {
            if (timeValue < 0.3) return "历史信息 (已发生的重要事件)";
            if (timeValue < 0.7) return "现在信息 (当前正在发生)";
            return "未来信息 (趋势和预测)";
        }

        // 获取信息维度描述
        function getInfoDescription(infoValue) {
            if (infoValue < 0.3) return "外在信息 (政策、市场、环境因素)";
            if (infoValue < 0.7) return "中性信息 (商业模式、应用场景)";
            return "内在信息 (核心技术、机制原理)";
        }

        // 获取注意力维度描述
        function getAttentionDescription(attentionValue) {
            if (attentionValue < 0.4) return "概览认知 (快速了解、关键要点)";
            if (attentionValue < 0.7) return "中等认知 (基本理解、重要细节)";
            return "深度认知 (专业分析、深入机制)";
        }

        // 按区域筛选节点
        function filterByRegion(filterType) {
            // 重置所有按钮样式
            document.querySelectorAll('.region-btn').forEach(btn => {
                btn.style.background = 'white';
                btn.style.color = '#495057';
            });

            // 高亮当前按钮
            event.target.style.background = '#27ae60';
            event.target.style.color = 'white';

            // 筛选节点
            document.querySelectorAll('.knowledge-node').forEach(nodeElement => {
                const nodeId = parseInt(nodeElement.getAttribute('data-id'));
                const node = domainConfig.nodes.find(n => n.id === nodeId);

                if (node && node.region.includes(filterType)) {
                    nodeElement.style.opacity = '1';
                    nodeElement.style.transform = 'scale(1.2)';
                } else {
                    nodeElement.style.opacity = '0.2';
                    nodeElement.style.transform = 'scale(0.8)';
                }
            });

            // 显示筛选统计
            const filteredNodes = domainConfig.nodes.filter(n => n.region.includes(filterType));
            console.log(`🔍 筛选结果: ${filterType} - 共${filteredNodes.length}个节点`);
        }

        // 重置信息空间视图
        function resetInfoSpaceView() {
            // 重置按钮样式
            document.querySelectorAll('.region-btn').forEach(btn => {
                btn.style.background = 'white';
                btn.style.color = '#495057';
            });

            // 重置节点显示
            document.querySelectorAll('.knowledge-node').forEach(node => {
                node.style.opacity = '1';
                node.style.transform = 'scale(1)';
            });

            // 隐藏位置信息
            document.getElementById('current-node-position').style.display = 'none';

            console.log('🔄 信息空间视图已重置');
        }

        // 获取层次颜色
        function getLayerColor(layer) {
            const colors = {
                'policy': '#e74c3c',
                'tech': '#3498db',
                'business': '#f39c12',
                'application': '#27ae60'
            };
            return colors[layer] || '#95a5a6';
        }

        // 生成阶段分析
        function generateStageAnalysis(node) {
            const currentYear = 2024;
            const yearDiff = currentYear - node.year;

            if (yearDiff < 0) {
                return `这是未来${Math.abs(yearDiff)}年的预期发展节点，代表了行业的发展趋势和方向。`;
            } else if (yearDiff <= 2) {
                return `这是近期的重要发展，正在对当前行业格局产生深远影响。`;
            } else if (yearDiff <= 10) {
                return `这是近十年的关键发展，为当前行业状态奠定了重要基础。`;
            } else {
                return `这是历史性的重要节点，为整个行业的发展开创了先河。`;
            }
        }

        // 焦点选择功能 - 增强版
        function selectFocus(layer) {
            const layerNames = {
                'policy': '政策监管层',
                'tech': '技术创新层',
                'business': '商业价值层',
                'application': '应用服务层'
            };

            // 高亮选中的焦点
            document.querySelectorAll('.focus-item').forEach(item => {
                item.style.background = '#f8f9fa';
            });
            document.querySelector(`.focus-item.${layer}`).style.background = '#e8f5e8';

            // 筛选显示对应的节点
            document.querySelectorAll('.knowledge-node').forEach(node => {
                if (node.classList.contains(layer)) {
                    node.style.opacity = '1';
                    node.style.transform = 'scale(1.2)';
                } else {
                    node.style.opacity = '0.3';
                    node.style.transform = 'scale(0.8)';
                }
            });

            // 显示层次统计信息
            const layerNodes = domainConfig.nodes.filter(n => n.layer === layer);
            const avgImpact = layerNodes.reduce((sum, n) => sum + n.impact, 0) / layerNodes.length;

            console.log(`用户选择了${layerNames[layer]}作为关注焦点`);
            console.log(`该层次包含${layerNodes.length}个节点，平均影响力：${avgImpact.toFixed(1)}星`);
        }

        // 重置视图
        function resetView() {
            document.querySelectorAll('.focus-item').forEach(item => {
                item.style.background = '#f8f9fa';
            });

            document.querySelectorAll('.knowledge-node').forEach(node => {
                node.style.opacity = '1';
                node.style.transform = 'scale(1)';
            });
        }

        // 添加键盘支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.getElementById('node-details').style.display = 'none';
            } else if (e.key === 'r' || e.key === 'R') {
                resetView();
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateNodes();
            console.log('🚗 新能源汽车三维信息空间知识图谱已加载');
            console.log('� 三维轴系统：时间轴 × 信息轴 × 注意力轴');
            console.log('�💡 功能提示：');
            console.log('  - 点击节点查看详情和三维坐标');
            console.log('  - 使用区域筛选按钮探索不同信息空间');
            console.log('  - 按ESC关闭详情，按R重置视图');
            console.log('🎯 当前共有8个信息区域，14个知识节点');
        });
    </script>
</body>
</html>
