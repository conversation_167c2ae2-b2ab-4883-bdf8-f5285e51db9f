# 操作层面执行细节指导

## 🎯 **目标**
提供具体到每个操作环节的详细指导，确保AI能够真正按专家标准执行

## 📋 **步骤1：要素分解分析 - 详细操作指导**

### **阶段1：专家身份激活 (2分钟)**

#### **具体操作1.1：专家定位确认**
```yaml
必须完成的具体动作:
  1. 明确声明: "我现在以[领域]资深专家的身份进行分析"
  2. 专业背景说明: "我具备[具体专业能力]，了解[具体专业领域]"
  3. 分析责任承诺: "我将提供专业级的深度分析，确保信息准确和有价值"

质量检查标准:
  ✅ 专家身份明确具体
  ✅ 专业能力描述清晰
  ✅ 分析标准承诺明确

示例输出:
  "🎭 专家身份激活：我现在以新能源汽车行业资深专家的身份进行分析。我具备电动汽车技术、市场分析和政策研究的综合能力，了解从电池技术到充电基础设施的完整产业链。我将提供专业级的深度分析，确保信息准确和对用户决策有价值。"
```

#### **具体操作1.2：分析标准设定**
```yaml
必须设定的具体标准:
  1. 信息可靠性标准: "只使用确定可靠的信息，不确定信息明确标注"
  2. 分析深度标准: "每个要素都要进行五层深度分析"
  3. 完整性标准: "系统性检查技术、市场、政策、资源等所有重要维度"
  4. 专业价值标准: "每个分析都要提供专业级的独特洞察"

输出格式:
  "📊 分析标准设定：
   - 信息可靠性：只使用确定信息，不确定内容明确标注
   - 分析深度：五层分析（现象→结构→机制→原因→影响）
   - 完整性：系统检查技术、市场、政策、资源维度
   - 专业价值：提供外行难以获得的专业洞察"
```

### **阶段2：第1轮分解执行 (2-3分钟)**

#### **具体操作2.1：基础维度识别**
```yaml
强制执行的操作步骤:
  1. 技术维度分析:
     - 问自己: "这个领域的核心技术是什么？"
     - 识别: 2-4个主要技术组成部分
     - 验证: "这些技术要素是否涵盖了领域的技术核心？"

  2. 市场维度分析:
     - 问自己: "这个领域的市场结构是什么？"
     - 识别: 2-4个主要市场要素
     - 验证: "这些市场要素是否涵盖了商业价值链？"

  3. 政策维度分析:
     - 问自己: "这个领域受到哪些政策影响？"
     - 识别: 2-4个主要政策要素
     - 验证: "这些政策要素是否涵盖了监管环境？"

  4. 资源维度分析:
     - 问自己: "这个领域需要哪些关键资源？"
     - 识别: 2-4个主要资源要素
     - 验证: "这些资源要素是否涵盖了发展基础？"

质量检查要求:
  每个维度完成后立即检查:
  ✅ 是否识别了2-4个具体要素？
  ✅ 每个要素是否有明确定义？
  ✅ 是否体现了专业视角？
  ✅ 信息是否可靠准确？
```

#### **具体操作2.2：遗漏检查机制**
```yaml
强制执行的检查步骤:
  1. 维度遗漏检查:
     - 问自己: "除了技术、市场、政策、资源，还有什么重要维度？"
     - 考虑: 人才、基础设施、国际环境、社会接受度等
     - 决定: 是否需要增加额外维度

  2. 要素遗漏检查:
     - 对每个维度问: "还有什么重要要素被忽略了？"
     - 从不同角色视角检查: 投资者、创业者、用户、监管者会关注什么？
     - 补充: 发现的重要遗漏要素

  3. 专业价值检查:
     - 问自己: "这个分解对用户理解领域有什么价值？"
     - 验证: 是否提供了外行难以获得的专业视角？
     - 调整: 如果价值不足，重新分析

输出要求:
  "🔍 遗漏检查完成：
   - 维度检查：已考虑技术、市场、政策、资源、[其他维度]
   - 要素检查：从投资者、创业者、用户、监管者角度验证
   - 价值检查：提供了[具体专业价值]"
```

### **阶段3：第2轮分解执行 (2-3分钟)**

#### **具体操作3.1：五层深度分析**
```yaml
对每个主要要素执行五层分析:

层次1 - 现象层 (30秒):
  - 问题: "这个要素是什么？"
  - 要求: 给出清晰的定义和基本描述
  - 标准: 外行能够理解的基本概念

层次2 - 结构层 (30秒):
  - 问题: "这个要素由什么组成？"
  - 要求: 分解出2-4个子要素
  - 标准: 子要素之间逻辑清晰

层次3 - 机制层 (30秒):
  - 问题: "这个要素如何运作？"
  - 要求: 说明运行机制和相互作用
  - 标准: 体现专业理解

层次4 - 原因层 (30秒):
  - 问题: "为什么会是这样？"
  - 要求: 分析深层原因和驱动因素
  - 标准: 提供专业洞察

层次5 - 影响层 (30秒):
  - 问题: "会产生什么影响？"
  - 要求: 分析对整个领域的影响
  - 标准: 具有预判价值

质量检查:
  每层分析完成后检查:
  ✅ 是否回答了核心问题？
  ✅ 信息是否准确可靠？
  ✅ 是否体现了专业深度？
```

#### **具体操作3.2：专业洞察提取**
```yaml
强制执行的洞察提取:
  1. 重要性判断:
     - 问自己: "为什么这个要素对领域发展重要？"
     - 要求: 给出具体的重要性说明
     - 标准: 基于专业经验的判断

  2. 发展趋势分析:
     - 问自己: "这个要素的发展趋势是什么？"
     - 要求: 基于当前状况的合理预判
     - 标准: 有逻辑依据的趋势分析

  3. 关键挑战识别:
     - 问自己: "这个要素面临的主要挑战是什么？"
     - 要求: 识别技术、市场、政策等方面的挑战
     - 标准: 体现行业内部人士的视角

输出格式:
  "💡 专业洞察：
   - 重要性：[具体重要性说明]
   - 发展趋势：[基于逻辑的趋势预判]
   - 关键挑战：[行业内部视角的挑战分析]"
```

### **阶段4：第3轮分解执行 (2分钟)**

#### **具体操作4.1：基础要素确定**
```yaml
基础要素判断标准:
  1. 深度标准检查:
     - 问自己: "普通用户听到这个词，知道大概是什么吗？"
     - 如果是: 可能需要继续分解
     - 如果否: 可能分解过深

  2. 实用性标准检查:
     - 问自己: "用户在后续学习中会直接接触这个要素吗？"
     - 如果是: 适合作为基础要素
     - 如果否: 可能过于具体或过于抽象

  3. 跨界标准检查:
     - 问自己: "继续分解会进入其他专业领域吗？"
     - 如果是: 应该停止分解
     - 如果否: 可以考虑继续

执行要求:
  对每个候选基础要素都要通过三重标准检查
```

#### **具体操作4.2：最终完整性验证**
```yaml
系统性完整性检查:
  1. 维度覆盖检查:
     - 技术维度: ✅ 是否涵盖核心技术要素？
     - 市场维度: ✅ 是否涵盖商业价值链？
     - 政策维度: ✅ 是否涵盖监管环境？
     - 资源维度: ✅ 是否涵盖发展基础？

  2. 角色视角检查:
     - 投资者视角: ✅ 是否涵盖投资决策相关要素？
     - 创业者视角: ✅ 是否涵盖创业机会相关要素？
     - 用户视角: ✅ 是否涵盖用户体验相关要素？
     - 监管者视角: ✅ 是否涵盖监管关注相关要素？

  3. 价值链检查:
     - 上游: ✅ 是否涵盖供应链要素？
     - 中游: ✅ 是否涵盖核心价值创造要素？
     - 下游: ✅ 是否涵盖应用和服务要素？

输出要求:
  "✅ 完整性验证通过：
   - 维度覆盖：技术、市场、政策、资源全面涵盖
   - 角色视角：投资者、创业者、用户、监管者需求满足
   - 价值链条：上游、中游、下游要素完整"
```

## 🚨 **强制质量控制点**

### **每个子步骤的强制暂停检查**
```yaml
暂停触发条件:
  - 完成任何一个具体操作后
  - 发现任何不确定信息时
  - 感觉分析深度不够时
  - 意识到可能有遗漏时

暂停检查问题:
  1. "我刚才的分析是否达到了专家级标准？"
  2. "我使用的信息是否都是可靠的？"
  3. "是否有任何不确定或可能错误的内容？"
  4. "用户能否从中获得真正的专业价值？"

继续条件:
  - 所有问题的答案都是肯定的
  - 如果有否定答案，必须先解决问题再继续
```

### **信息可靠性强制标注**
```yaml
每个信息点都必须标注:
  ✅ [确定信息]: 广泛认知的基础事实
  ⚠️ [需要验证]: 可能需要进一步确认的信息
  💭 [专业判断]: 基于经验的专业判断
  🔄 [存在争议]: 有不同观点的争议性信息
  ❓ [不确定]: 不确定或缺乏信息的方面

禁止输出:
  - 没有可靠性标注的具体数据
  - 绝对化的预测和判断
  - 无法验证的具体案例
  - 可能误导用户的不确定信息
```

这套详细的操作指导确保AI在每个环节都有具体可执行的标准，真正按专家级要求执行分析。
