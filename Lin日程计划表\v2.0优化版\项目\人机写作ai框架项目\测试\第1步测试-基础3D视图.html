<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第1步测试：基础3D知识图谱</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .knowledge-space {
            width: 100%;
            height: 500px;
            position: relative;
            border: 2px solid #e0e6ed;
            border-radius: 15px;
            background: 
                radial-gradient(circle at 20% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(231, 76, 60, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            overflow: hidden;
            perspective: 1000px;
            transform-style: preserve-3d;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .knowledge-node {
            position: absolute;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;
            transform-style: preserve-3d;
        }

        .node-circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.9);
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.15),
                0 4px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .knowledge-node:hover .node-circle {
            transform: scale(1.3);
            box-shadow: 
                0 12px 35px rgba(0, 0, 0, 0.25),
                0 6px 15px rgba(0, 0, 0, 0.15);
        }

        .node-label {
            position: absolute;
            top: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 100;
        }

        .knowledge-node:hover .node-label {
            opacity: 1;
        }

        .connection-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .status h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .status p {
            color: #7f8c8d;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 第1步测试：基础3D知识图谱</h1>
            <p>验证基础节点生成和3D视觉效果</p>
        </div>

        <div class="main-content">
            <h2>🗺️ 知识空间全景</h2>
            <div class="knowledge-space" id="knowledge-space">
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    正在加载知识图谱...
                </div>
                <svg class="connection-layer" id="connection-layer"></svg>
            </div>

            <div class="status" id="status">
                <h3>📊 初始化状态</h3>
                <p id="status-text">等待初始化...</p>
            </div>
        </div>
    </div>

    <script>
        // 简化的测试数据
        const testData = {
            nodes: [
                {
                    id: "test_1",
                    year: 2009,
                    layer: "policy",
                    title: "十城千辆工程",
                    impactLevel: 4,
                    position: { x: 25, y: 80, z: 0 },
                    style: { color: "#e74c3c", size: 16, opacity: 0.8 },
                    description: "中国首个大规模新能源汽车推广示范工程"
                },
                {
                    id: "test_2",
                    year: 1991,
                    layer: "tech",
                    title: "锂离子电池商业化",
                    impactLevel: 5,
                    position: { x: 75, y: 30, z: 25 },
                    style: { color: "#3498db", size: 18, opacity: 0.8 },
                    description: "为电动汽车提供了可行的能源存储解决方案"
                },
                {
                    id: "test_3",
                    year: 2003,
                    layer: "business",
                    title: "特斯拉成立",
                    impactLevel: 5,
                    position: { x: 50, y: 50, z: 50 },
                    style: { color: "#f39c12", size: 18, opacity: 0.8 },
                    description: "重新定义了电动汽车的商业模式和用户体验"
                }
            ],
            connections: [
                {
                    from: "test_1",
                    to: "test_2",
                    type: "influence",
                    style: { color: "#3498db", width: 2, opacity: 0.6 }
                },
                {
                    from: "test_2",
                    to: "test_3",
                    type: "enable",
                    style: { color: "#e74c3c", width: 3, opacity: 0.7 }
                }
            ]
        };

        // 更新状态显示
        function updateStatus(message) {
            document.getElementById('status-text').textContent = message;
            console.log('📊 状态更新:', message);
        }

        // 生成测试节点
        function generateTestNodes() {
            updateStatus('开始生成节点...');
            const knowledgeSpace = document.getElementById('knowledge-space');
            const loading = document.getElementById('loading');
            
            // 移除加载提示
            if (loading) {
                loading.remove();
            }

            testData.nodes.forEach((node, index) => {
                setTimeout(() => {
                    const nodeElement = document.createElement('div');
                    nodeElement.className = 'knowledge-node';
                    nodeElement.setAttribute('data-id', node.id);
                    
                    // 设置位置
                    nodeElement.style.left = `${node.position.x}%`;
                    nodeElement.style.top = `${node.position.y}%`;
                    
                    // 创建节点圆圈
                    const circle = document.createElement('div');
                    circle.className = 'node-circle';
                    circle.style.backgroundColor = node.style.color;
                    circle.style.width = `${node.style.size}px`;
                    circle.style.height = `${node.style.size}px`;
                    
                    // 创建标签
                    const label = document.createElement('div');
                    label.className = 'node-label';
                    label.textContent = `${node.title} (${node.year})`;
                    
                    nodeElement.appendChild(circle);
                    nodeElement.appendChild(label);
                    
                    // 添加点击事件
                    nodeElement.addEventListener('click', () => {
                        updateStatus(`点击了节点: ${node.title}`);
                        console.log('🎯 节点点击:', node);
                    });
                    
                    knowledgeSpace.appendChild(nodeElement);
                    updateStatus(`节点 ${index + 1}/${testData.nodes.length} 已生成: ${node.title}`);
                }, index * 200);
            });
            
            setTimeout(() => {
                updateStatus('所有节点生成完成！');
                generateTestConnections();
            }, testData.nodes.length * 200 + 100);
        }

        // 生成测试连接线
        function generateTestConnections() {
            updateStatus('开始生成连接线...');
            const svg = document.getElementById('connection-layer');
            const knowledgeSpace = document.getElementById('knowledge-space');
            const rect = knowledgeSpace.getBoundingClientRect();
            
            svg.setAttribute('width', rect.width);
            svg.setAttribute('height', rect.height);
            
            testData.connections.forEach((conn, index) => {
                setTimeout(() => {
                    const fromNode = testData.nodes.find(n => n.id === conn.from);
                    const toNode = testData.nodes.find(n => n.id === conn.to);
                    
                    if (fromNode && toNode) {
                        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                        
                        const x1 = (fromNode.position.x / 100) * rect.width;
                        const y1 = (fromNode.position.y / 100) * rect.height;
                        const x2 = (toNode.position.x / 100) * rect.width;
                        const y2 = (toNode.position.y / 100) * rect.height;
                        
                        line.setAttribute('x1', x1);
                        line.setAttribute('y1', y1);
                        line.setAttribute('x2', x2);
                        line.setAttribute('y2', y2);
                        line.setAttribute('stroke', conn.style.color);
                        line.setAttribute('stroke-width', conn.style.width);
                        line.setAttribute('opacity', conn.style.opacity);
                        
                        svg.appendChild(line);
                        updateStatus(`连接线 ${index + 1}/${testData.connections.length} 已生成`);
                    }
                }, index * 300);
            });
            
            setTimeout(() => {
                updateStatus('✅ 第1步测试完成！基础3D视图正常工作');
            }, testData.connections.length * 300 + 100);
        }

        // 初始化测试
        function initTest() {
            updateStatus('🚀 开始第1步测试...');
            setTimeout(generateTestNodes, 500);
        }

        // 页面加载完成后开始测试
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
