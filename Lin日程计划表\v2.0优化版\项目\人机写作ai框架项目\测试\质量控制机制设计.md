# 质量控制机制设计

## 🎯 **核心目标**
确保AI真正"用心执行"每个步骤，避免机械化完成和信息编造

## 🔄 **五步质量控制循环**

### **步骤1：执行前准备 (Pre-Execution)**
```yaml
时间分配: 每个步骤开始前30秒
核心任务:
  - 明确当前步骤的专家级标准
  - 激活相关的专业知识和思维模式
  - 设定本步骤的具体质量目标
  - 准备知识边界处理机制

执行检查:
  - [ ] 我是否明确了本步骤的专家标准？
  - [ ] 我是否激活了相关的专业思维？
  - [ ] 我是否设定了具体的质量目标？
  - [ ] 我是否准备好处理不确定信息？

输出要求:
  "🎯 本步骤专家目标：[具体目标]"
  "🧠 激活专业思维：[思维模式]"
  "📊 质量标准：[具体标准]"
```

### **步骤2：分段执行 (Segmented Execution)**
```yaml
执行原则:
  - 将每个步骤分解为2-3个子任务
  - 每个子任务完成后进行微检查
  - 发现问题立即调整，不等到最后

子任务划分示例（要素分解分析）:
  子任务1: 第1轮分解 - 主要构成识别
  子任务2: 第2轮分解 - 子要素展开  
  子任务3: 第3轮分解 - 基础要素确定

微检查要求:
  每个子任务完成后问自己：
  - 这个子任务是否达到了专家级水准？
  - 是否有明显的遗漏或错误？
  - 是否需要立即调整？
```

### **步骤3：暂停质量检查 (Quality Check Pause)**
```yaml
检查时机: 每个主要步骤完成后
检查时长: 1-2分钟
检查维度:

准确性检查:
  - [ ] 所有信息是否准确可靠？
  - [ ] 是否有任何不确定或可能错误的信息？
  - [ ] 是否需要标注信息来源或可靠性？

完整性检查:
  - [ ] 是否覆盖了所有重要方面？
  - [ ] 是否有明显的遗漏点？
  - [ ] 是否需要补充更多信息？

专业性检查:
  - [ ] 是否体现了专家级的深度？
  - [ ] 是否提供了独特的专业洞察？
  - [ ] 是否超越了表面信息？

用户价值检查:
  - [ ] 用户能否从中获得有价值的信息？
  - [ ] 是否符合用户的背景和需求？
  - [ ] 是否有助于用户决策？

输出要求:
  "✅ 质量检查通过：[通过原因]"
  或
  "⚠️ 发现问题：[具体问题] → 需要精进"
```

### **步骤4：精进优化 (Refinement)**
```yaml
触发条件: 质量检查发现问题
精进原则:
  - 针对具体问题进行定向改进
  - 不满足于小修小补，追求根本性提升
  - 必要时重新执行整个子步骤

常见问题及精进方案:

信息准确性问题:
  - 重新验证可疑信息
  - 标注不确定信息
  - 提供替代信息获取途径

分析深度不足:
  - 深入挖掘表面现象背后的原因
  - 增加专家级的洞察和判断
  - 提供更多维度的分析

完整性不足:
  - 系统性检查可能的遗漏点
  - 从不同角度重新审视问题
  - 补充重要的缺失信息

用户针对性不足:
  - 重新理解用户背景和需求
  - 调整信息的表达方式和重点
  - 增加针对性的建议和指导

输出要求:
  "🔧 精进完成：[改进内容]"
  "📈 提升效果：[具体提升]"
```

### **步骤5：继续执行 (Continue Execution)**
```yaml
继续条件: 质量检查通过或精进完成
继续原则:
  - 将本步骤的经验应用到下一步骤
  - 保持专家思维的连续性
  - 确保整体逻辑的一致性

经验传递:
  - 记录本步骤的成功做法
  - 识别需要在下一步骤注意的问题
  - 保持专业分析的深度和标准

输出要求:
  "➡️ 继续下一步骤"
  "📝 经验传递：[关键经验]"
  "🎯 下步重点：[注意事项]"
```

## 🚨 **防止信息编造的专门机制**

### **信息可靠性分级系统：**
```yaml
A级信息 (高可靠性):
  - 广泛认知的基础事实
  - 可以直接使用，无需特别标注

B级信息 (中等可靠性):
  - 需要验证的具体数据
  - 使用时标注："据公开资料显示..."

C级信息 (低可靠性):
  - 争议性或新兴信息
  - 使用时标注："存在不同观点..."

D级信息 (不可靠):
  - 明显不确定或可能错误
  - 禁止使用，或标注："需要进一步验证"
```

### **不确定性处理标准话术：**
```yaml
对于不确定信息:
  "根据可获得的信息，[内容]，但这一点需要进一步验证"
  
对于争议性信息:
  "在这个问题上存在不同观点：[观点A] vs [观点B]"
  
对于缺失信息:
  "关于[具体方面]的详细信息，建议查阅[具体来源]"
  
对于预测性信息:
  "基于当前趋势分析，可能的发展方向是[内容]，但存在不确定性"
```

## 📋 **质量控制执行清单**

### **每个步骤必须完成：**
- [ ] 执行前准备：明确专家标准和质量目标
- [ ] 分段执行：将步骤分解为子任务并微检查
- [ ] 暂停检查：从四个维度全面检查质量
- [ ] 精进优化：针对问题进行定向改进（如需要）
- [ ] 继续执行：传递经验并保持标准

### **整体流程必须确保：**
- [ ] 所有信息都经过可靠性分级
- [ ] 不确定信息都有明确标注
- [ ] 每个步骤都体现专家级水准
- [ ] 用户能获得真正有价值的洞察

## ⏰ **时间分配调整**

考虑到质量控制机制，建议调整时间分配：
- 原步骤时间 + 质量控制时间 = 新总时间
- 步骤1：6-8分钟 + 2分钟 = 8-10分钟
- 步骤2：4-5分钟 + 1分钟 = 5-6分钟  
- 步骤3：7-9分钟 + 2分钟 = 9-11分钟
- 步骤4：3-4分钟 + 1分钟 = 4-5分钟
- **新总时间：26-32分钟**
