# 0A阶段：多角色视角信息收集操作清单 - 通用八角色框架

## 🎯 **阶段目标**

通过多个专家角色的视角，全面收集领域内的所有关键信息节点，确保信息的**完整性**和**多维度覆盖**，为后续的三维信息空间构建奠定基础。

## 🎭 **核心设计原理**

### **多角色交叉验证机制**
- **目的**：防止单一视角的信息遗漏和认知偏差
- **方法**：从不同利益相关者角度收集信息
- **标准**：每个信息节点至少从2个角色视角验证

### **信息全面性优先**
- **原则**：宁可信息冗余，不可重要遗漏
- **标准**：全面覆盖领域内的所有重要信息节点
- **检查**：通过多角色视角交叉检查确保无盲区

## 🏗️ **通用生态链角色框架**

### **核心设计逻辑：价值链流转关系**
```yaml
设计原理:
  - 按照价值创造→传递→实现的完整链条
  - 每个环节都有具体的工作内容和信息来源
  - 角色之间是协作关系，不是上下级关系
  - 更加务实和具体的职能定位
  - 确保信息收集的全面性和差异性
```

### **八大核心角色**（适用于任何领域）

#### **🔬 创新驱动者**
```yaml
通用原理: 
  在任何领域中，都需要有专门负责创新、改进、突破现状的角色。
  他们的核心功能是发现问题、提出解决方案、推动技术或方法的进步。
  这些角色通常具有前瞻性思维，能够识别发展趋势，并将创新想法转化为可行方案。

应用示例（包括但不限于）:
  - 体育竞技: 战术创新教练、青训体系设计师、技术分析师、运动科学专家、数据分析师
  - 生产制造: CTO、研发总监、技术专家、产品经理、专利工程师、实验室主任
  - 文化娱乐: 创意总监、编剧、艺术总监、内容创新者、概念设计师、新媒体专家
  - 政治外交: 政策设计师、智库专家、战略规划师、改革推动者、国际关系研究员
  - 教育培训: 课程设计师、教学方法研究者、教育技术专家、学习科学研究员
  - 医疗健康: 医学研究者、治疗方案设计师、医疗技术创新者、药物研发专家
  - 金融服务: 金融产品设计师、风险模型专家、金融科技创新者、投资策略师
  - 科技互联网: 算法工程师、产品设计师、技术架构师、用户体验设计师

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他创新专业职位。
  重点关注该领域的技术前沿、方法创新、理念突破等方面的专业人员。

信息收集视角:
  - 技术发展趋势和创新方向
  - 竞争对手的创新动态
  - 新技术、新方法的可行性分析
  - 创新项目的里程碑和进展
  - 知识产权和技术保护策略
  - 创新团队建设和能力提升

典型信息节点:
  - 关键技术突破和创新成果
  - 创新项目里程碑和进展
  - 技术瓶颈识别和解决方案
  - 专利申请和标准制定
  - 创新团队建设和投资
  - 前沿技术和方法探索
```

**🔍 自检自查要求**：
完成创新驱动者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"执行实施者"角色**

#### **🏭 执行实施者**
```yaml
通用原理:
  在任何领域中，都需要有专门负责将想法转化为现实、具体执行操作的角色。
  他们的核心功能是按照既定方案进行实施、确保质量、管理执行过程、解决实施中的具体问题。
  这些角色通常具有丰富的实践经验，能够将抽象的计划转化为具体的行动和结果。

应用示例（包括但不限于）:
  - 体育竞技: 主教练、球员、训练师、队医、体能教练、战术执行教练
  - 生产制造: 生产总监、车间主任、质量经理、设备工程师、供应链经理、成本控制专员
  - 文化娱乐: 导演、演员、制片人、技术人员、后期制作、现场执行
  - 政治外交: 外交官、谈判代表、执行官员、项目实施者、领事官员
  - 教育培训: 授课教师、培训师、教务管理、实验指导、班主任、教学督导
  - 医疗健康: 临床医生、护士、技师、医疗设备操作员、药剂师、康复师
  - 金融服务: 客户经理、风险控制专员、交易员、审计师、合规专员
  - 科技互联网: 开发工程师、测试工程师、运维工程师、项目经理、技术支持

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他执行专业职位。
  重点关注该领域的具体操作、质量控制、流程管理、风险防控等方面的专业人员。

信息收集视角:
  - 具体执行过程中的问题和挑战
  - 质量控制和标准执行情况
  - 资源配置和效率优化
  - 团队管理和人员培训
  - 设备设施和技术支持
  - 成本控制和预算管理

典型信息节点:
  - 执行过程中的重要事件和里程碑
  - 质量问题和改进措施
  - 效率提升和流程优化
  - 团队建设和能力提升
  - 技术升级和设备改造
  - 成本控制和资源优化
```

**🔍 自检自查要求**：
完成执行实施者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"价值实现者"角色**

#### **💼 价值实现者**
```yaml
通用原理:
  在任何领域中，都需要有专门负责将产品或服务转化为价值、实现商业化或社会效益的角色。
  他们的核心功能是识别价值机会、设计价值实现路径、管理商业模式、确保可持续发展。
  这些角色通常具有商业敏感度，能够平衡各方利益，实现多赢局面。

应用示例（包括但不限于）:
  - 体育竞技: 俱乐部CEO、商务总监、赞助商代表、转播权谈判专家、品牌管理
  - 生产制造: 商务总监、市场总监、业务拓展经理、战略规划经理、产品运营经理
  - 文化娱乐: 制片人、发行商、经纪人、版权运营、商业化总监、IP开发
  - 政治外交: 贸易代表、经济参赞、投资促进官、商务领事、经济合作专员
  - 教育培训: 商务总监、市场开发、课程产品经理、企业培训销售、教育投资
  - 医疗健康: 医院管理者、医疗服务商务、健康产业投资、医疗保险专员
  - 金融服务: 业务总监、产品经理、投资银行家、财富管理、保险经纪
  - 科技互联网: 商业化总监、商务拓展、产品运营、变现策略师、合作伙伴管理

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他价值实现专业职位。
  重点关注该领域的商业模式、盈利方式、价值创造、资源整合等方面的专业人员。

信息收集视角:
  - 商业模式创新和价值实现路径
  - 市场机会识别和商业化策略
  - 合作伙伴关系和战略联盟
  - 收入模式和盈利结构优化
  - 品牌建设和市场定位
  - 投资机会和资源配置

典型信息节点:
  - 商业模式创新和调整
  - 重要合作伙伴和战略联盟
  - 新市场开拓和业务扩张
  - 收入结构和盈利模式变化
  - 品牌价值提升和市场定位
  - 投资并购和资源整合
```

**🔍 自检自查要求**：
完成价值实现者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"传播推广者"角色**

#### **🛒 传播推广者**
```yaml
通用原理:
  在任何领域中，都需要有专门负责传播、推广、渠道建设的角色。
  他们的核心功能是将产品或服务有效传递给目标受众，建立品牌认知，扩大影响力。
  这些角色通常具有传播技巧和渠道资源，能够选择合适的方式和平台进行有效推广。

应用示例（包括但不限于）:
  - 体育竞技: 媒体总监、转播商、体育记者、社交媒体运营、球迷社区管理
  - 生产制造: 销售总监、渠道经理、市场推广经理、客服经理、零售店长
  - 文化娱乐: 宣传总监、发行商、影评人、媒体关系、粉丝运营
  - 政治外交: 新闻发言人、文化参赞、公共外交官、媒体联络、国际传播
  - 教育培训: 招生主任、市场推广、教育顾问、学员服务、校友关系
  - 医疗健康: 医院宣传、健康教育、患者服务、医疗咨询、社区推广
  - 金融服务: 客户经理、渠道拓展、品牌推广、客户服务、理财顾问
  - 科技互联网: 市场推广、用户运营、渠道合作、客户成功、社区运营

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他传播推广专业职位。
  重点关注该领域的传播渠道、推广方式、受众特点、品牌建设等方面的专业人员。

信息收集视角:
  - 传播渠道建设和优化
  - 推广活动效果和反馈
  - 目标受众行为和偏好
  - 品牌认知度和影响力
  - 竞争对手推广策略
  - 媒体关系和公共形象

典型信息节点:
  - 重要推广活动和营销事件
  - 传播渠道建设和扩张
  - 受众反馈和市场反应
  - 品牌形象和认知度变化
  - 媒体报道和公众关注
  - 推广策略和方法创新
```

**🔍 自检自查要求**：
完成传播推广者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"体验接受者"角色**

#### **👥 体验接受者**
```yaml
通用原理:
  在任何领域中，都需要有实际体验、接受、消费产品或服务的角色。
  他们的核心功能是提供真实的使用体验、需求反馈、接受度评价。
  这些角色通常代表最终受益者的利益，能够从用户角度提供最直接的反馈。

应用示例（包括但不限于）:
  - 体育竞技: 球迷、观众、体育爱好者、青少年运动员、体育消费者
  - 生产制造: 个人消费者、企业采购经理、专业用户、早期采用者、意见领袖
  - 文化娱乐: 观众、读者、用户、粉丝、文化消费者、评论家
  - 政治外交: 公民、企业、受影响群体、国际社会、利益相关者
  - 教育培训: 学生、学员、家长、企业HR、终身学习者
  - 医疗健康: 患者、家属、健康消费者、医疗服务使用者、保险受益人
  - 金融服务: 个人客户、企业客户、投资者、借贷者、保险客户
  - 科技互联网: 终端用户、企业用户、开发者、内容创作者、平台参与者

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他体验接受专业职位。
  重点关注该领域的最终受益者、使用者、消费者等方面的专业人员。

信息收集视角:
  - 实际使用体验和满意度
  - 需求变化和期望调整
  - 接受度和认知变化
  - 使用场景和行为模式
  - 问题反馈和改进建议
  - 口碑传播和社会影响

典型信息节点:
  - 用户体验和满意度变化
  - 需求演进和期望调整
  - 接受度和采用率变化
  - 使用场景扩展和创新
  - 问题反馈和改进需求
  - 口碑传播和社会反响
```

**🔍 自检自查要求**：
完成体验接受者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"观察分析者"角色**

#### **📊 观察分析者**
```yaml
通用原理:
  在任何领域中，都需要有专门负责观察、分析、研究的角色。
  他们的核心功能是收集数据、分析趋势、提供洞察、预测发展。
  这些角色通常具有分析能力和研究方法，能够从大量信息中提取有价值的见解。

应用示例（包括但不限于）:
  - 体育竞技: 体育记者、数据分析师、足球评论员、战术分析师、体育研究员
  - 生产制造: 行业分析师、市场研究员、数据科学家、商业智能分析师、政策研究员
  - 文化娱乐: 影评人、文化评论家、市场分析师、内容研究员、趋势观察者
  - 政治外交: 国际关系学者、政策分析师、智库研究员、外交观察员、区域专家
  - 教育培训: 教育研究员、学习分析师、教育政策研究、市场调研员、数据分析师
  - 医疗健康: 医学研究员、流行病学家、健康数据分析师、医疗政策研究员
  - 金融服务: 金融分析师、市场研究员、风险分析师、经济学家、投资研究员
  - 科技互联网: 技术分析师、用户研究员、数据科学家、市场研究员、趋势分析师

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他观察分析专业职位。
  重点关注该领域的数据分析、趋势研究、市场调研、政策分析等方面的专业人员。

信息收集视角:
  - 行业数据和统计分析
  - 发展趋势和预测模型
  - 竞争格局和市场结构
  - 政策影响和环境变化
  - 风险识别和机会评估
  - 研究报告和专业洞察

典型信息节点:
  - 重要研究报告和数据发布
  - 趋势分析和预测更新
  - 市场结构和竞争格局变化
  - 政策影响和环境分析
  - 风险预警和机会识别
  - 专业观点和深度洞察
```

**🔍 自检自查要求**：
完成观察分析者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"资源配置者"角色**

#### **💰 资源配置者**
```yaml
通用原理:
  在任何领域中，都需要有专门负责资源分配、投资、资金管理的角色。
  他们的核心功能是优化资源配置、评估投资价值、管理财务风险、确保资源效率。
  这些角色通常具有财务分析能力和投资判断力，能够在有限资源下实现最大价值。

应用示例（包括但不限于）:
  - 体育竞技: 俱乐部投资人、转会市场经纪人、赞助商、体育投资基金、财务总监
  - 生产制造: 投资总监、基金经理、财务总监、风险控制经理、投行分析师、信贷经理
  - 文化娱乐: 投资人、制片人、版权方、文化产业基金、财务投资者
  - 政治外交: 财政部官员、国际金融机构、援助机构、发展银行、投资促进机构
  - 教育培训: 教育投资人、政府教育预算、教育基金、风险投资、财务管理
  - 医疗健康: 医疗投资基金、保险公司、政府卫生预算、医疗设备投资、药企投资
  - 金融服务: 投资银行、私募基金、风险投资、保险公司、资产管理公司
  - 科技互联网: 风险投资、私募股权、企业投资、政府基金、天使投资人

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他资源配置专业职位。
  重点关注该领域的投资决策、资金管理、风险控制、价值评估等方面的专业人员。

信息收集视角:
  - 投资机会和价值评估
  - 资金流向和配置策略
  - 风险识别和控制措施
  - 财务表现和投资回报
  - 市场估值和定价机制
  - 政策影响和投资环境

典型信息节点:
  - 重要投资事件和融资情况
  - 资金配置策略和调整
  - 风险事件和控制措施
  - 估值变化和市场表现
  - 投资政策和环境变化
  - 财务业绩和投资回报
```

**🔍 自检自查要求**：
完成资源配置者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话继续"协调治理者"角色**

#### **🌐 协调治理者**
```yaml
通用原理:
  在任何领域中，都需要有专门负责协调、治理、标准制定的角色。
  他们的核心功能是建立规则、协调各方、维护秩序、促进合作。
  这些角色通常具有协调能力和治理经验，能够平衡不同利益，建立有效的合作机制。

应用示例（包括但不限于）:
  - 体育竞技: FIFA官员、各国足协、裁判委员会、体育仲裁法庭、联盟管理机构
  - 生产制造: 行业协会、标准组织、监管机构、质量认证机构、行业联盟
  - 文化娱乐: 行业协会、审查机构、版权组织、文化管理部门、行业自律组织
  - 政治外交: 国际组织、多边机构、条约组织、联合国机构、区域合作组织
  - 教育培训: 教育部门、认证机构、教育协会、质量保障机构、国际教育组织
  - 医疗健康: 卫生组织、医疗协会、药监机构、医疗认证、国际卫生组织
  - 金融服务: 金融监管、行业协会、自律组织、国际金融组织、标准制定机构
  - 科技互联网: 技术标准组织、行业联盟、监管机构、国际标准化组织、平台治理

领域适配原则:
  根据具体领域特点，该角色可能还包括该领域相关的其他协调治理专业职位。
  重点关注该领域的规则制定、标准建立、争议解决、合作促进等方面的专业人员。

信息收集视角:
  - 规则制定和标准更新
  - 协调机制和治理结构
  - 争议处理和仲裁案例
  - 合作项目和联盟建设
  - 监管政策和执法动态
  - 国际合作和标准对接

典型信息节点:
  - 重要规则和标准制定
  - 治理机制和结构调整
  - 重大争议和仲裁案例
  - 合作协议和联盟建设
  - 监管政策和执法行动
  - 国际合作和标准化进展
```

**🔍 自检自查要求**：
完成协调治理者角色信息收集后，请进行以下质量自检：
- ✅ 信息节点数量是否达到8-12个？
- ✅ 是否体现了不同职位的独特视角？
- ✅ 信息是否具体务实，避免了编造数据？
- ✅ 每个节点是否正确标注了时间、重要性、可靠性？
- ✅ 是否收集到了深层次的专业信息，而非表面信息？
- ✅ **诚实回答原则**：有就是有，没有就是没有，不存在的职位或信息绝不编造

**🛑 自检确认无误后，立即结束当前对话，开启新对话进行最终整合**

## 📋 **执行操作清单**

### **核心执行原则：8次强制暂停机制**
```yaml
执行流程:
  1. 选择一个角色进行深度信息收集
  2. 完成该角色的所有信息节点收集后立即暂停对话
  3. 用户检查质量并确认
  4. 开启新对话继续下一个角色
  5. 重复直到8个角色全部完成
  6. 最后一次对话进行信息整合

暂停的核心目的:
  - 防止AI在单次对话中处理多个角色导致注意力分散
  - 确保每个角色都能得到深度、专业的信息收集
  - 避免表面化处理，确保收集到深层次的专业信息
  - 每个角色都能获得AI的全部注意力和专业能力
  - 通过自检自查机制进行质量控制和确认

自检自查标准要求:
  - 信息节点数量: 每个角色至少8-12个独特信息节点
  - 职位视角体现: 体现该角色内不同职位的独特关注点
  - 信息务实性: 具体可操作，避免编造数据和案例
  - 标注完整性: 每个节点都有时间、重要性、可靠性标注
  - 专业深度: 收集深层次专业信息，非表面化内容
  - **诚实回答原则**: 有就是有，没有就是没有，不存在的职位或信息绝不编造

错误做法: 在一个对话中依次处理8个角色（会导致信息质量下降）
正确做法: 每个角色单独一次对话，完成后自检确认，确保深度和质量
```

## 📄 **0A阶段最终输出格式**

### **标准化信息节点文档结构**
```yaml
文档标题: [领域名称]信息节点收集报告 - 0A阶段输出

第一部分: 执行摘要
  - 总信息节点数量: X个
  - 角色覆盖完整性: 8/8个角色
  - 质量达标率: X%
  - 执行时间: X天

第二部分: 分角色信息节点清单
  🔬 创新驱动者 (X个节点):
    - [节点ID-001] 节点标题 | 时间 | 重要性★★★★☆ | 可靠性[确定信息]
    - [节点ID-002] 节点标题 | 时间 | 重要性★★★☆☆ | 可靠性[需要验证]
    - ...

  🏭 执行实施者 (X个节点):
    - [节点ID-015] 节点标题 | 时间 | 重要性★★★★★ | 可靠性[确定信息]
    - ...

  💼 价值实现者 (X个节点):
    - ...

  🛒 传播推广者 (X个节点):
    - ...

  👥 体验接受者 (X个节点):
    - ...

  📊 观察分析者 (X个节点):
    - ...

  💰 资源配置者 (X个节点):
    - ...

  🌐 协调治理者 (X个节点):
    - ...

第三部分: 统计分析
  - 时间分布: 历史X%、现在X%、未来X%
  - 重要性分布: 5星X个、4星X个、3星X个、2星X个、1星X个
  - 可靠性分布: 确定信息X个、需要验证X个、专业判断X个、存在争议X个、不确定X个
  - 角色贡献度: 各角色信息节点数量和质量评估

第四部分: 质量评估报告
  - 完整性评估: 是否覆盖了所有重要信息节点
  - 准确性评估: 信息可靠性标注是否准确
  - 专业性评估: 是否体现了各角色的专业视角
  - 0B阶段准备度: 是否为三维坐标分配提供了充分基础

第五部分: 0B阶段输入清单
  - 待分配坐标的信息节点清单
  - 节点间潜在连接关系识别
  - 三维空间分布预期
  - 特殊处理需求说明
```

### **使用说明**
```yaml
执行方式:
  1. 每个角色单独开启一次对话进行深度信息收集
  2. 完成一个角色后立即结束对话，避免注意力分散
  3. 8个角色完成后，第9次对话进行信息整合
  4. 输出标准化文档，为0B阶段提供输入

质量保证:
  - 每个角色至少收集8-12个独特信息节点
  - 每个节点都有明确的时间、重要性、可靠性标注
  - 体现该角色具体职位人员的专业视角
  - 避免编造具体数据和案例
  - 严格遵循诚实回答原则
```

---

**下一阶段预告**：0B阶段将基于这个标准化信息节点文档，为每个节点分配精确的三维信息空间坐标，建立结构化的知识网络。
