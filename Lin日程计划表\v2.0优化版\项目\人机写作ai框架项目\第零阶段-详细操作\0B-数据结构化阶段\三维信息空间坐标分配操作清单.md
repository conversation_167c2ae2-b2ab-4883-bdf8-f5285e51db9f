# 0B阶段：三维信息空间坐标分配操作清单

## 🎯 **阶段目标**

基于0A阶段收集的信息节点，为每个节点分配精确的三维信息空间坐标，建立结构化的知识网络，为0C阶段的可视化实现提供标准化输入。

## 🎭 **核心设计原理**

### **三维信息空间坐标系统**
- **X轴 - 时间轴**：过去(-1.0) ←→ 现在(0.0) ←→ 未来(+1.0)
- **Y轴 - 信息轴**：外在信息(-1.0) ←→ 中性信息(0.0) ←→ 内在信息(+1.0)
- **Z轴 - 注意力轴**：概览认知(-1.0) ←→ 中等认知(0.0) ←→ 深度认知(+1.0)

### **八个空间区域定义**
```yaml
真正的8个空间区域:
  1. 过去-外在-概览: (-1, -1, -1)
  2. 过去-外在-深度: (-1, -1, +1)
  3. 过去-内在-概览: (-1, +1, -1)
  4. 过去-内在-深度: (-1, +1, +1)
  5. 未来-外在-概览: (+1, -1, -1)
  6. 未来-外在-深度: (+1, -1, +1)
  7. 未来-内在-概览: (+1, +1, -1)
  8. 未来-内在-深度: (+1, +1, +1)
```

### **向量空间模型原理**
```yaml
核心概念:
  - 8个角色 = 8个基向量
  - 每个信息节点 = 基向量的线性组合
  - 节点坐标 = Σ(角色影响权重i × 角色基向量i)
  - 权重反映该角色对此信息节点的影响程度

角色基向量定义:
  🔬 创新驱动者: (+0.6, +0.7, +0.8)  # 未来-内在-深度
  🏭 执行实施者: (0.0, +0.6, +0.2)   # 现在-内在-中等
  💼 价值实现者: (+0.5, 0.0, +0.4)   # 未来-中性-中等
  🛒 传播推广者: (+0.2, -0.6, -0.4)  # 现在-外在-概览
  👥 体验接受者: (+0.3, -0.7, -0.6)  # 现在-外在-概览
  📊 观察分析者: (-0.3, 0.0, +0.6)  # 过去-中性-深度
  💰 资源配置者: (+0.4, -0.4, +0.6) # 未来-外在-深度
  🌐 协调治理者: (0.0, 0.0, +0.4)   # 中性-中性-中等

权重动态分配原则:
  - 权重由AI根据具体领域和信息节点内容动态判断
  - 所有权重之和必须 = 1.0 (归一化约束)
  - 至少有一个权重 ≥ 0.4 (主导角色约束)
  - 最多3个角色权重 > 0.1 (避免过度分散)
```

## 📋 **四步骤执行框架**

### **步骤1：角色基向量确定** ⏱️ 5分钟

#### **执行动作**
```yaml
1.1 确认角色基向量坐标:
  - 验证8个角色基向量的三维坐标设定
  - 确保基向量分布覆盖8个主要空间区域
  - 检查基向量的数学合理性(-1~+1范围内)

1.2 建立权重评估标准:
  - 1.0: 该角色是此信息的唯一来源/核心创造者
  - 0.7-0.9: 该角色是此信息的主要参与者/重要影响者
  - 0.4-0.6: 该角色对此信息有显著影响/密切关注
  - 0.1-0.3: 该角色对此信息有间接影响/一般关注
  - 0.0: 该角色与此信息基本无关

1.3 建立权重分配约束:
  - 归一化约束: 所有权重之和 = 1.0
  - 主导角色约束: 至少有一个权重 ≥ 0.4
  - 分散控制约束: 最多3个角色权重 > 0.1
  - 逻辑一致性约束: 权重分配符合生态链逻辑

1.4 建立权重评估问题框架:
  - 这个信息节点主要由哪个角色产生？
  - 哪些角色会对这个信息高度关注？
  - 哪些角色会受到这个信息的影响？
  - 这个信息在生态链中的位置如何？
```

#### **质量检查标准**
- ✅ 8个角色基向量是否合理分布在三维空间？
- ✅ 权重评估标准是否清晰明确？
- ✅ 权重分配约束是否数学严谨？
- ✅ 评估问题框架是否有助于权重判断？

#### **输出交付物**
- 角色基向量坐标表
- 权重评估标准和约束
- 权重判断问题框架

---

### **步骤2：权重分配与坐标计算** ⏱️ 20-30分钟

#### **执行动作**
```yaml
2.1 逐个节点分析角色影响:
  - 对每个信息节点，分析8个角色的影响程度
  - 使用步骤1的评估问题框架进行权重判断
  - 识别主导角色(权重≥0.4)和次要影响角色(权重0.1-0.3)

2.2 权重分配与验证:
  - 为每个节点的8个角色分配具体权重值
  - 验证权重总和是否等于1.0(归一化)
  - 检查是否满足主导角色和分散控制约束
  - 确保权重分配符合生态链逻辑

2.3 应用向量计算公式:
  - 使用公式: 节点坐标 = Σ(权重i × 角色基向量i)
  - 计算每个节点的三维坐标(X, Y, Z)
  - 验证计算结果在-1~+1范围内
  - 检查坐标的合理性和逻辑一致性

2.4 处理特殊情况:
  - 跨角色验证节点: 综合多角色权重进行计算
  - 极端坐标处理: 接近±1.0的坐标需要特别验证
  - 坐标冲突调整: 多个节点坐标过于接近时的微调
  - 精度控制: 坐标值保留小数点后1位

2.5 坐标计算示例:
  某技术突破节点权重分配:
  🔬 创新驱动者: 0.6 × (+0.6, +0.7, +0.8) = (+0.36, +0.42, +0.48)
  🏭 执行实施者: 0.3 × (0.0, +0.6, +0.2) = (0.0, +0.18, +0.06)
  📊 观察分析者: 0.1 × (-0.3, 0.0, +0.6) = (-0.03, 0.0, +0.06)
  最终坐标 = (+0.33, +0.60, +0.60)
```

#### **质量检查标准**
- ✅ 每个节点的权重分配是否合理且符合约束？
- ✅ 向量计算是否正确执行？
- ✅ 最终坐标是否在-1~+1范围内？
- ✅ 坐标分配是否体现了角色影响的逻辑？
- ✅ **诚实分配原则**：权重和坐标基于真实分析，不随意编造

#### **输出交付物**
- 完整的节点权重分配表
- 节点三维坐标清单
- 向量计算过程记录

---

### **步骤3：坐标分布优化** ⏱️ 10-15分钟

#### **执行动作**
```yaml
3.1 空间分布统计分析:
  - 统计8个空间区域的节点分布密度
  - 识别过于集中或空白的区域
  - 分析节点在-1~+1坐标范围内的分布情况

3.2 权重微调优化:
  - 对过于集中的节点群进行权重微调
  - 通过调整次要角色权重来分散节点位置
  - 保持主导角色权重不变，确保逻辑一致性

3.3 八区域覆盖验证:
  - 确认8个主要空间区域都有节点分布
  - 验证每个区域的节点数量合理性
  - 识别需要补充节点的空白区域

3.4 重要性与空间位置匹配:
  - 检查高重要性节点的空间分布
  - 确保关键节点位于合适的坐标区域
  - 验证节点重要性与其空间位置的逻辑关系

3.5 边界值处理:
  - 检查接近±1.0的极端坐标节点
  - 验证极端位置的合理性和必要性
  - 适当调整避免不必要的极端值

3.6 向量计算验证:
  - 重新验证调整后的权重分配
  - 确保所有权重总和仍为1.0
  - 重新计算调整节点的最终坐标
```

#### **质量检查标准**
- ✅ 8个空间区域是否都有合理的节点分布？
- ✅ 权重微调是否保持了逻辑一致性？
- ✅ 重要节点是否位于合适的空间位置？
- ✅ 极端坐标值是否合理且必要？
- ✅ 调整后的向量计算是否正确？

#### **输出交付物**
- 优化后的节点权重和坐标清单
- 空间分布统计分析报告
- 权重调整说明和计算记录

---

### **步骤4：连接关系建立** ⏱️ 10分钟

#### **执行动作**
```yaml
4.1 影响关系识别:
  - 分析节点间的因果关系
  - 识别时间序列上的影响链条
  - 发现跨领域的关联影响

4.2 连接强度评级:
  - 强连接(0.8-1.0): 直接因果关系
  - 中连接(0.5-0.7): 间接影响关系  
  - 弱连接(0.2-0.4): 相关性关系
  - 无连接(0.0-0.1): 基本无关

4.3 连接类型标注:
  - 时间驱动: 历史事件推动后续发展
  - 技术驱动: 技术突破带来应用创新
  - 政策驱动: 政策变化影响市场行为
  - 市场驱动: 市场需求推动技术发展

4.4 连接网络验证:
  - 检查连接关系的逻辑合理性
  - 验证重要节点的连接度
  - 确保网络结构的完整性
```

#### **质量检查标准**
- ✅ 节点间连接关系是否合理识别？
- ✅ 连接强度评级是否准确？
- ✅ 连接类型标注是否清晰？
- ✅ 整体网络结构是否完整？

#### **输出交付物**
- 节点连接关系图
- 连接强度和类型清单
- 网络结构分析报告

## 🔍 **质量控制标准**

### **完整性检查**
- 信息节点坐标分配完整率：100%
- 三维空间区域覆盖率：≥75%
- 节点连接关系识别率：≥80%

### **准确性检查**
- 坐标分配逻辑一致性：≥95%
- 角色特征匹配度：≥90%
- 连接关系合理性：≥85%

### **专业性检查**
- 体现三维信息空间理论
- 保持角色视角的独特性
- 确保坐标分配的科学性

## 📊 **输出交付物**

### **主要交付物**
1. **节点权重分配数据库**：每个节点对应8个角色的权重值
2. **三维坐标节点数据库**：基于向量计算的精确坐标(-1~+1)
3. **角色基向量分析报告**：8个角色在三维空间中的基向量定义
4. **向量计算过程记录**：权重分配和坐标计算的完整过程
5. **连接关系网络图**：节点间的影响关系和强度
6. **空间分布统计报告**：8个区域的节点分布和密度分析
7. **0C阶段输入清单**：为可视化阶段提供标准化向量数据

### **交付标准**
- 权重分配准确性：所有节点权重总和=1.0，符合约束条件
- 坐标计算正确性：向量计算无误，坐标在-1~+1范围内
- 空间分布合理性：8个区域都有适当的节点覆盖
- 数据格式标准化：便于0C阶段直接使用向量数据
- 质量达标率：>95%的数据通过向量空间模型验证

---

**下一阶段预告**：0C阶段将基于这个三维坐标数据库，实现交互式知识图谱的可视化，让用户能够直观地探索和理解领域知识结构。
