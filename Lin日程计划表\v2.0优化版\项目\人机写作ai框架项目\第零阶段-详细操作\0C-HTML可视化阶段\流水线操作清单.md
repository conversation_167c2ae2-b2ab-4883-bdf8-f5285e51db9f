# 0C阶段：HTML可视化流水线操作清单（模块化执行版）

## 🎯 **使用说明**
本文档采用模块化设计，AI执行时请按需阅读对应模块：
- **执行步骤1** → 阅读 [模块1：HTML框架生成]
- **执行步骤2** → 阅读 [模块2：数据注入与渲染]
- **执行步骤3** → 阅读 [模块3：交互功能实现]

## 📊 **总体目标与输入**
- **输入：** 0B阶段的标准化数据对象
- **目标：** 生成完整可交互的HTML知识图谱界面
- **总耗时：** 7分钟
- **核心原则：** 用户体验优先、交互直观、性能良好

---

# 🔧 模块1：HTML框架生成

## 📋 **模块概览**
- **目标：** 生成完整的HTML文档结构和基础样式
- **耗时：** 2分钟
- **输出：** 带有占位符的完整HTML框架

## 🧠 **执行原则**
```yaml
框架生成标准:
  - 结构完整：包含所有必需的HTML元素和容器
  - 语义清晰：使用语义化的HTML标签和类名
  - 响应式：支持桌面端和移动端适配
  - 可扩展：预留足够的扩展空间和接口

样式设计要求:
  - 现代化：使用现代CSS特性和设计风格
  - 一致性：颜色、字体、间距保持一致
  - 可读性：确保文字清晰易读，对比度充足
  - 美观性：整体视觉效果专业美观
```

## 💬 **AI执行提示词**
```
🏗️ HTML框架专家分析：
我现在要生成完整的HTML知识图谱界面框架。作为前端开发专家，我需要创建结构清晰、样式美观、功能完备的HTML文档。

🎯 本步骤目标：
基于0B阶段的数据结构，生成完整的HTML框架，包括头部、主体、底部的完整布局，以及响应式CSS样式。

🤔 核心思考问题：
1. 如何设计清晰的页面布局结构？
2. 如何实现响应式的CSS样式？
3. 如何预留数据注入的占位符？
4. 如何确保代码的可维护性？

现在开始HTML框架生成...
```

## 📋 **详细执行流程**

### **阶段1：HTML结构生成** ⏱️ 1分钟
```yaml
强制执行动作:
  1. 生成标准HTML5文档结构
  2. 创建头部区域（标题、说明、当前位置）
  3. 创建主体区域（可视化区域、信息面板）
  4. 创建底部区域（时间轴、操作按钮）

模板替换规则:
  {{DOMAIN_NAME}} → 0B数据中的domain字段
  {{TIME_RANGE}} → "从{start}年到{end}年"
  {{CURRENT_YEAR}} → 当前年份
  {{NODE_COUNT}} → 节点总数

HTML结构要求:
  - DOCTYPE声明和meta标签完整
  - 语义化标签使用恰当
  - 类名和ID命名规范
  - 注释清晰标注各区域功能

质量检查:
  ✅ HTML结构完整规范
  ✅ 占位符设置正确
  ✅ 语义化标签使用恰当
  ✅ 注释清晰易懂
```

### **阶段2：CSS样式生成** ⏱️ 1分钟
```yaml
样式设计要求:
  1. 响应式布局：支持桌面端(>768px)和移动端(<768px)
  2. 颜色主题：使用专业的配色方案
  3. 字体系统：清晰易读的字体栈
  4. 交互效果：hover、focus、active状态

核心样式模块:
  - 基础重置和全局样式
  - 布局容器和网格系统
  - 知识节点样式
  - 连接线样式
  - 交互效果样式
  - 响应式媒体查询

颜色方案:
  主色调: #2c3e50 (深蓝灰)
  辅助色: #3498db (蓝色)
  强调色: #e74c3c (红色)
  背景色: #f8f9fa (浅灰)

质量检查:
  ✅ 样式模块化清晰
  ✅ 响应式布局正确
  ✅ 颜色搭配协调
  ✅ 交互效果流畅
```

## ✅ **模块1完成标准**
- HTML结构完整且语义化
- CSS样式现代化且响应式
- 占位符设置正确
- 代码规范且易维护

# 🔧 模块2：数据注入与渲染

## 📋 **模块概览**
- **目标：** 将0B数据注入HTML并渲染可视化元素
- **耗时：** 3分钟
- **输出：** 完整的知识节点和连接网络

## 🧠 **执行原则**
```yaml
数据渲染标准:
  - 准确性：数据注入无误，节点位置精确
  - 性能性：渲染效率高，动画流畅
  - 可交互：所有元素支持用户交互
  - 可扩展：代码结构便于功能扩展

渲染质量要求:
  - 节点大小和颜色按规范映射
  - 连接线样式和强度正确表达
  - 层次关系清晰可见
  - 当前位置突出显示
```

## 💬 **AI执行提示词**
```
🎨 数据渲染专家分析：
我现在要将0B阶段的结构化数据渲染为可视化的知识图谱。作为数据可视化专家，我需要准确地将数据转换为DOM元素，确保视觉效果和交互体验。

🎯 本步骤目标：
将0B数据注入JavaScript变量，生成知识节点的DOM元素，渲染连接线网络，应用样式和动画效果。

🤔 核心思考问题：
1. 如何高效地遍历和渲染大量节点？
2. 如何计算和绘制连接线路径？
3. 如何实现流畅的动画效果？
4. 如何优化渲染性能？

现在开始数据注入与渲染...
```

## 📋 **详细执行流程**

### **阶段1：数据注入** ⏱️ 30秒
```yaml
强制执行动作:
  1. 将0B阶段的完整数据对象注入JavaScript
  2. 验证数据结构的完整性
  3. 预处理数据以优化渲染性能
  4. 设置全局变量和配置

数据注入格式:
  const knowledgeData = {
    domain: "领域名称",
    timeRange: { start: 1950, end: 2025 },
    nodes: [...],
    connections: [...],
    config: {...}
  };

数据验证检查:
  - 节点数组不为空
  - 每个节点都有必需属性
  - 连接关系引用有效
  - 配置参数完整

质量检查:
  ✅ 数据注入完整无误
  ✅ 数据结构验证通过
  ✅ 全局变量设置正确
  ✅ 配置参数有效
```

### **阶段2：节点渲染** ⏱️ 1.5分钟
```yaml
节点渲染逻辑:
  1. 遍历nodes数组
  2. 为每个节点创建DOM元素
  3. 设置位置、颜色、大小属性
  4. 添加hover和click事件监听
  5. 插入到可视化容器中

DOM元素结构:
  <div class="knowledge-node" data-id="node_1" data-layer="tech">
    <div class="node-circle"></div>
    <div class="node-label">节点标题</div>
    <div class="node-year">2020</div>
  </div>

样式应用规则:
  - 位置：transform: translate3d(x, y, z)
  - 颜色：background-color根据layer映射
  - 大小：width/height根据impactLevel计算
  - 透明度：opacity根据current状态设置

事件绑定:
  - mouseenter: 显示详细信息
  - mouseleave: 隐藏详细信息
  - click: 打开详情面板
  - touchstart: 移动端触摸支持

质量检查:
  ✅ 所有节点正确渲染
  ✅ 位置和样式准确
  ✅ 事件绑定正常
  ✅ 性能表现良好
```

### **阶段3：连接线渲染** ⏱️ 1分钟
```yaml
连接渲染逻辑:
  1. 遍历connections数组
  2. 计算连接线的起点和终点坐标
  3. 创建SVG或Canvas线条元素
  4. 设置颜色、粗细、样式属性
  5. 添加动画效果

SVG连接线结构:
  <svg class="connection-layer">
    <line x1="x1" y1="y1" x2="x2" y2="y2"
          stroke="color" stroke-width="width"
          stroke-dasharray="pattern" opacity="opacity"/>
  </svg>

样式映射规则:
  - 强连接：实线，宽度3px，不透明度0.9
  - 中连接：虚线，宽度2px，不透明度0.7
  - 弱连接：点线，宽度1px，不透明度0.5

动画效果:
  - 连接线逐渐出现动画
  - hover时高亮相关连接
  - 数据流动动画效果

质量检查:
  ✅ 连接线路径正确
  ✅ 样式映射准确
  ✅ 动画效果流畅
  ✅ 交互响应及时
```

## ✅ **模块2完成标准**
- 数据注入完整准确
- 节点渲染位置和样式正确
- 连接线网络显示完整
- 交互事件响应正常

---

# 🔧 模块3：交互功能实现

## 📋 **模块概览**
- **目标：** 实现完整的用户交互功能
- **耗时：** 2分钟
- **输出：** 功能完备的交互式知识图谱

## 🧠 **执行原则**
```yaml
交互设计标准:
  - 直观性：交互逻辑符合用户直觉
  - 响应性：操作反馈及时准确
  - 一致性：交互行为保持一致
  - 可访问性：支持键盘和屏幕阅读器

功能完整性要求:
  - 核心功能：层次筛选、节点详情、时间导航
  - 增强功能：搜索、缩放、动画、导出
  - 响应式：桌面端和移动端适配
  - 性能：流畅的用户体验
```

## 💬 **AI执行提示词**
```
⚡ 交互功能专家分析：
我现在要实现知识图谱的完整交互功能。作为用户体验专家，我需要确保所有交互都直观易用，响应及时，为用户提供流畅的探索体验。

🎯 本步骤目标：
实现四层关注焦点筛选、节点点击详情显示、时间轴导航功能，优化用户体验细节，确保跨设备兼容。

🤔 核心思考问题：
1. 如何设计直观的层次筛选界面？
2. 如何实现流畅的节点详情展示？
3. 如何优化移动端的交互体验？
4. 如何确保功能的可访问性？

现在开始交互功能实现...
```

## 📋 **详细执行流程**

### **阶段1：核心交互功能** ⏱️ 1分钟
```yaml
必须实现的交互功能:
  1. 层次筛选按钮 - 显示/隐藏特定层的节点
  2. 节点点击事件 - 显示详细信息面板
  3. 当前位置标识 - 高亮当前最佳切入点
  4. 响应式适配 - 移动端友好

层次筛选实现:
  - 四个切换按钮：政策、技术、商业、应用
  - 点击切换显示/隐藏对应层次的节点
  - 视觉反馈：按钮状态和节点透明度变化
  - 连接线联动：隐藏相关连接

节点详情面板:
  - 点击节点显示详情侧边栏
  - 内容：标题、时间、描述、影响力、相关连接
  - 关闭方式：点击关闭按钮或点击空白区域
  - 动画：滑入滑出效果

当前位置标识:
  - 特殊样式标记当前时间的节点
  - 脉冲动画效果吸引注意
  - 工具提示说明当前位置意义

质量检查:
  ✅ 筛选功能正常工作
  ✅ 详情面板显示正确
  ✅ 当前位置突出显示
  ✅ 动画效果流畅
```

### **阶段2：增强功能和优化** ⏱️ 1分钟
```yaml
可选增强功能:
  1. 搜索功能 - 快速定位特定节点
  2. 缩放和拖拽 - 3D空间导航
  3. 动画效果 - 节点出现和连接动画
  4. 导出功能 - 保存图谱为图片

搜索功能实现:
  - 搜索框：实时搜索节点标题
  - 结果高亮：匹配节点高亮显示
  - 快速定位：点击结果自动聚焦
  - 清空功能：一键清除搜索结果

缩放和拖拽:
  - 鼠标滚轮：缩放整个图谱
  - 鼠标拖拽：平移视图
  - 触摸支持：移动端手势操作
  - 边界限制：防止拖拽超出范围

响应式优化:
  - 移动端：触摸友好的按钮大小
  - 平板端：适配中等屏幕尺寸
  - 桌面端：充分利用大屏空间
  - 性能优化：减少重绘和回流

用户体验细节:
  - 加载动画：数据加载时的友好提示
  - 错误处理：网络错误的优雅降级
  - 键盘支持：Tab键导航和快捷键
  - 无障碍：ARIA标签和语义化

质量检查:
  ✅ 增强功能工作正常
  ✅ 响应式适配良好
  ✅ 性能表现优秀
  ✅ 用户体验流畅
```

## ✅ **模块3完成标准**
- 所有核心交互功能正常工作
- 增强功能提升用户体验
- 响应式设计适配各种设备
- 性能优化确保流畅体验

## 🎯 **0C阶段总体完成标准**
- ✅ 所有模块按标准执行完成
- ✅ HTML框架结构完整且语义化
- ✅ 数据注入和渲染准确无误
- ✅ 交互功能完备且响应流畅
- ✅ 用户体验优秀且跨设备兼容

## 📋 **最终输出示例**
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{DOMAIN_NAME}}知识图谱 - 零阶段探索</title>
    <style>
        /* 完整的响应式CSS样式 */
        /* 知识节点和连接线样式 */
        /* 交互效果和动画样式 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{DOMAIN_NAME}}知识图谱</h1>
            <div class="subtitle">探索发展脉络，找到最佳切入点</div>
        </div>

        <div class="main-content">
            <div class="visualization-area">
                <div class="knowledge-space" id="knowledge-space">
                    <!-- 动态生成的知识节点和连接线 -->
                </div>
            </div>

            <div class="info-panel">
                <h3>🎯 四层关注焦点</h3>
                <!-- 层次筛选和节点详情 -->
            </div>
        </div>
    </div>

    <script>
        // 完整的数据注入和交互功能
        const knowledgeData = {/* 0B阶段数据 */};
        // 节点渲染、连接绘制、交互实现
    </script>
</body>
</html>
```

## 📊 **质量检查标准**
- ✅ HTML结构完整，语义清晰
- ✅ CSS样式美观，响应式布局
- ✅ JavaScript功能正常，无错误
- ✅ 数据正确显示，交互流畅
- ✅ 跨浏览器兼容，性能良好

## 👥 **用户体验验证**
- ✅ 用户能快速理解知识结构
- ✅ 能轻松找到感兴趣的切入点
- ✅ 交互直观，学习曲线平缓
- ✅ 信息层次清晰，不会迷失

## ⏰ **总耗时：7分钟**

## 🔄 **最终成果：** 用户可以直接使用的专业知识图谱界面

---

## 🎯 **AI执行者使用说明**

### **模块化执行原则：**
1. **按需阅读**：执行哪个步骤就阅读对应模块，无需处理整个文档
2. **用户体验优先**：所有设计决策都要考虑用户体验
3. **性能优化**：确保页面加载快速，交互响应及时
4. **质量检查**：每个模块都有强制质量检查，必须通过才能继续
5. **跨设备兼容**：确保在不同设备和浏览器上都能正常工作

### **常见问题处理：**
- **数据注入失败**：检查0B数据格式是否正确
- **节点渲染错误**：验证坐标计算和样式映射
- **交互功能异常**：检查事件绑定和DOM操作
- **性能问题**：优化渲染逻辑，减少重绘回流

## 📊 **整个0A→0B→0C流水线总耗时：41-42分钟**
- 0A阶段：28-35分钟
- 0B阶段：6分钟
- 0C阶段：7分钟
