<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识领域全景图 - 零阶段专业模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #2c3e50;
            line-height: 1.6;
        }
        
        .container {
            display: grid;
            grid-template-rows: auto 2fr 1fr;
            min-height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .current-position {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            display: inline-block;
            font-size: 1.1em;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .visualization-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
            position: relative;
        }
        
        .visualization-area h2 {
            color: #495057;
            margin-bottom: 25px;
            font-size: 1.5em;
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .knowledge-space {
            position: relative;
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .knowledge-node {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
            border: 2px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .knowledge-node:hover {
            transform: scale(1.5);
            z-index: 100;
            box-shadow: 0 5px 20px rgba(0,0,0,0.4);
        }
        
        .knowledge-node.current {
            width: 30px;
            height: 30px;
            border: 3px solid #ffd700;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { box-shadow: 0 0 10px rgba(255, 215, 0, 0.6); }
            50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.9); }
        }
        
        .policy { background: #e74c3c; }
        .tech { background: #3498db; }
        .business { background: #f39c12; }
        .application { background: #27ae60; }
        
        .node-label {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 11px;
            white-space: nowrap;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            transform: translateX(-50%);
            top: -35px;
            left: 50%;
        }
        
        .knowledge-node:hover .node-label {
            opacity: 1;
        }
        
        .dimension-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        
        .dimension-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            text-align: center;
        }
        
        .dimension-card:hover {
            transform: translateY(-3px);
        }
        
        .dimension-card.core { border-left-color: #007bff; }
        .dimension-card.evolution { border-left-color: #28a745; }
        .dimension-card.ecosystem { border-left-color: #ffc107; }
        .dimension-card.application { border-left-color: #dc3545; }
        .dimension-card.challenge { border-left-color: #6f42c1; }
        .dimension-card.opportunity { border-left-color: #20c997; }
        
        .dimension-card .icon {
            font-size: 1.5em;
            margin-bottom: 8px;
            display: block;
        }
        
        .dimension-card h4 {
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .dimension-card p {
            color: #6c757d;
            font-size: 0.8em;
        }
        
        .info-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 1px solid #e9ecef;
            height: fit-content;
        }
        
        .info-panel h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        
        .focus-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .focus-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }
        
        .focus-item.policy { border-left-color: #e74c3c; }
        .focus-item.tech { border-left-color: #3498db; }
        .focus-item.business { border-left-color: #f39c12; }
        .focus-item.application { border-left-color: #27ae60; }
        
        .focus-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .focus-icon.policy { background: #e74c3c; }
        .focus-icon.tech { background: #3498db; }
        .focus-icon.business { background: #f39c12; }
        .focus-icon.application { background: #27ae60; }
        
        .focus-content h4 {
            margin-bottom: 5px;
            color: #495057;
        }
        
        .focus-content p {
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .node-details {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #2196f3;
            display: none;
        }
        
        .bottom-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        
        .timeline-section {
            padding: 30px;
            text-align: center;
        }
        
        .timeline-section h3 {
            color: #495057;
            margin-bottom: 20px;
        }
        
        .timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 300px;
            margin: 0 auto;
            position: relative;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #6c757d, #28a745, #007bff);
            z-index: 1;
        }
        
        .timeline-point {
            background: white;
            border: 4px solid;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            z-index: 2;
            position: relative;
            font-size: 0.8em;
        }
        
        .timeline-point.past { border-color: #6c757d; background: #6c757d; }
        .timeline-point.current { border-color: #28a745; background: #28a745; animation: pulse 2s infinite; }
        .timeline-point.future { border-color: #007bff; background: #007bff; }
        
        .action-section {
            padding: 30px;
            text-align: center;
        }
        
        .action-section h3 {
            color: #495057;
            margin-bottom: 20px;
        }
        
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 20px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .dimension-grid {
                grid-template-columns: 1fr 1fr;
            }
            
            .bottom-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="domain-title">人工智能领域</h1>
            <div class="subtitle">全景知识图谱 - 零阶段探索</div>
            <div class="current-position">
                <strong>📍 您当前位置：</strong>初学者 → 了解全貌 → 选择切入点
            </div>
        </div>
        
        <div class="main-content">
            <div class="visualization-area">
                <h2>🗺️ 知识空间全景</h2>
                
                <div class="knowledge-space" id="knowledge-space">
                    <!-- 知识节点将通过JavaScript动态生成 -->
                </div>
                
                <div class="dimension-grid">
                    <div class="dimension-card core">
                        <span class="icon">🎯</span>
                        <h4>核心认知</h4>
                        <p>基本概念原理</p>
                    </div>
                    
                    <div class="dimension-card evolution">
                        <span class="icon">📈</span>
                        <h4>发展演进</h4>
                        <p>历史现状趋势</p>
                    </div>
                    
                    <div class="dimension-card ecosystem">
                        <span class="icon">🌐</span>
                        <h4>生态系统</h4>
                        <p>产业链参与者</p>
                    </div>
                    
                    <div class="dimension-card application">
                        <span class="icon">🚀</span>
                        <h4>应用实践</h4>
                        <p>场景案例效果</p>
                    </div>
                    
                    <div class="dimension-card challenge">
                        <span class="icon">⚠️</span>
                        <h4>挑战风险</h4>
                        <p>瓶颈问题限制</p>
                    </div>
                    
                    <div class="dimension-card opportunity">
                        <span class="icon">💡</span>
                        <h4>机会价值</h4>
                        <p>市场投资发展</p>
                    </div>
                </div>
            </div>
            
            <div class="info-panel">
                <h3>🎯 四层关注焦点</h3>
                
                <div class="focus-item policy" onclick="selectFocus('policy')">
                    <div class="focus-icon policy">🏛️</div>
                    <div class="focus-content">
                        <h4>政策监管层</h4>
                        <p>合规性、安全性、社会影响</p>
                    </div>
                </div>
                
                <div class="focus-item tech" onclick="selectFocus('tech')">
                    <div class="focus-icon tech">🔬</div>
                    <div class="focus-content">
                        <h4>技术创新层</h4>
                        <p>突破性、效率性、创新性</p>
                    </div>
                </div>
                
                <div class="focus-item business" onclick="selectFocus('business')">
                    <div class="focus-icon business">💼</div>
                    <div class="focus-content">
                        <h4>商业价值层</h4>
                        <p>盈利性、市场机会、投资回报</p>
                    </div>
                </div>
                
                <div class="focus-item application" onclick="selectFocus('application')">
                    <div class="focus-icon application">🚀</div>
                    <div class="focus-content">
                        <h4>应用服务层</h4>
                        <p>便利性、易用性、实用性</p>
                    </div>
                </div>
                
                <div id="node-details" class="node-details">
                    <h4 id="detail-title">节点详情</h4>
                    <div id="detail-content"></div>
                </div>
            </div>
        </div>
        
        <div class="bottom-section">
            <div class="timeline-section">
                <h3>⏰ 发展时间线</h3>
                <div class="timeline">
                    <div class="timeline-point past">过去</div>
                    <div class="timeline-point current">现在</div>
                    <div class="timeline-point future">未来</div>
                </div>
                <p style="margin-top: 15px; color: #6c757d;">当前是最佳学习切入时机</p>
            </div>
            
            <div class="action-section">
                <h3>🎯 下一步行动</h3>
                <div class="action-buttons">
                    <button class="action-btn" onclick="selectFocus('tech')">🔬 深入技术层</button>
                    <button class="action-btn" onclick="selectFocus('business')">💼 探索商业层</button>
                    <button class="action-btn" onclick="selectFocus('application')">🚀 关注应用层</button>
                    <button class="action-btn" onclick="selectFocus('policy')">🏛️ 了解政策层</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 通用模板配置 - 可以根据不同领域动态替换
        const domainConfig = {
            title: "人工智能领域",
            nodes: [
                { id: 1, title: "图灵测试", year: 1950, layer: "tech", x: 10, y: 60, current: false },
                { id: 2, title: "AI概念", year: 1956, layer: "tech", x: 15, y: 50, current: false },
                { id: 3, title: "专家系统", year: 1980, layer: "tech", x: 25, y: 40, current: false },
                { id: 4, title: "商业化", year: 1985, layer: "business", x: 30, y: 70, current: false },
                { id: 5, title: "深度学习", year: 2006, layer: "tech", x: 50, y: 30, current: false },
                { id: 6, title: "AlexNet", year: 2012, layer: "tech", x: 60, y: 35, current: false },
                { id: 7, title: "Transformer", year: 2017, layer: "tech", x: 70, y: 25, current: false },
                { id: 8, title: "GPT-3", year: 2020, layer: "tech", x: 80, y: 30, current: false },
                { id: 9, title: "ChatGPT", year: 2022, layer: "application", x: 85, y: 80, current: true },
                { id: 10, title: "AI治理", year: 2023, layer: "policy", x: 88, y: 15, current: false },
                { id: 11, title: "AI工具普及", year: 2024, layer: "application", x: 90, y: 85, current: true },
                { id: 12, title: "AGI突破", year: 2025, layer: "tech", x: 95, y: 20, current: false }
            ]
        };
        
        // 生成知识节点
        function generateNodes() {
            const container = document.getElementById('knowledge-space');
            
            domainConfig.nodes.forEach(node => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `knowledge-node ${node.layer} ${node.current ? 'current' : ''}`;
                nodeElement.style.left = node.x + '%';
                nodeElement.style.top = node.y + '%';
                nodeElement.innerHTML = `<div class="node-label">${node.title} (${node.year})</div>`;
                
                nodeElement.addEventListener('click', () => showNodeDetail(node));
                container.appendChild(nodeElement);
            });
        }
        
        // 显示节点详情
        function showNodeDetail(node) {
            const detailDiv = document.getElementById('node-details');
            const title = document.getElementById('detail-title');
            const content = document.getElementById('detail-content');
            
            const layerNames = {
                'policy': '政策监管层',
                'tech': '技术创新层',
                'business': '商业价值层',
                'application': '应用服务层'
            };
            
            title.textContent = `${node.title} (${node.year}年)`;
            content.innerHTML = `
                <p><strong>🏷️ 类型：</strong>${layerNames[node.layer]}</p>
                <p><strong>📅 时间：</strong>${node.year}年</p>
                ${node.current ? '<p><strong>🔥 这是当前最佳切入点！</strong></p>' : ''}
            `;
            
            detailDiv.style.display = 'block';
        }
        
        // 焦点选择功能
        function selectFocus(layer) {
            const layerNames = {
                'policy': '政策监管层',
                'tech': '技术创新层', 
                'business': '商业价值层',
                'application': '应用服务层'
            };
            
            // 高亮选中的焦点
            document.querySelectorAll('.focus-item').forEach(item => {
                item.style.background = '#f8f9fa';
            });
            document.querySelector(`.focus-item.${layer}`).style.background = '#e3f2fd';
            
            // 筛选显示对应的节点
            document.querySelectorAll('.knowledge-node').forEach(node => {
                if (node.classList.contains(layer)) {
                    node.style.opacity = '1';
                    node.style.transform = 'scale(1.2)';
                } else {
                    node.style.opacity = '0.3';
                    node.style.transform = 'scale(0.8)';
                }
            });
            
            console.log(`用户选择了${layerNames[layer]}作为关注焦点`);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateNodes();
        });
    </script>
</body>
</html>
