# 零阶段完整流水线操作手册

## 🎯 **流水线目标**
将用户问题"我想了解[某个领域]"在35-45分钟内转化为专业的可视化知识图谱

## 🏭 **流水线总览**

```
用户输入 → 0A认知分析(20-26分钟) → 0B数据整理(6分钟) → 0C可视化生成(7分钟) → HTML成果
```

---

## 📋 **标准化操作流程**

### **🔧 0A阶段：领域认知分析（20-26分钟）**

#### **输入：** "我想了解[某个领域]"
#### **输出：** 专家级的结构化认知框架

**操作步骤：**
1. **要素分解分析** (6-8分钟) - 以专家洞察力深入挖掘底层组成要素
2. **生态链构建** (4-5分钟) - 构建完整的四层价值生态链
3. **角色视角重组** (7-9分钟) - 多元化视角全面理解领域复杂性
4. **分层认知输出** (3-4分钟) - 三层递进式认知支持

**专家级质量标准：**
- ✅ **专家级深度**：体现领域专家的洞察力和专业水准
- ✅ **全面性保证**：所有重要要素和角色视角都被识别
- ✅ **主动性体现**：AI主动识别和补充用户可能忽略的重要方面
- ✅ **完整性验证**：经过多轮检查确保无重要遗漏
- ✅ **个性化适配**：根据用户背景提供针对性指导
- ✅ **可操作性强**：提供具体可行的深入路径

---

### **🔧 0B阶段：数据整理（6分钟）**

#### **输入：** 0A阶段的文字分析数据
#### **输出：** 标准化的可视化数据对象

**操作步骤：**
1. **时间排序** (1分钟) - 按年份排序，计算时间密度
2. **空间分配** (2分钟) - 3D坐标计算，避免重叠
3. **关系构建** (2分钟) - 连接验证，强度分级
4. **格式标准化** (1分钟) - 转换为JavaScript对象

**数据结构：**
```javascript
{
  domain: "领域名称",
  timeRange: { start: 年份, end: 年份 },
  nodes: [{ id, year, layer, title, impactLevel, position, style, current }],
  connections: [{ from, to, type, strength, style }],
  config: { colors, layout, interaction }
}
```

---

### **🔧 0C阶段：HTML生成（7分钟）**

#### **输入：** 0B阶段的标准化数据对象
#### **输出：** 完整可交互的HTML知识图谱

**操作步骤：**
1. **框架生成** (2分钟) - HTML结构、CSS样式、JS框架
2. **数据渲染** (3分钟) - 节点生成、连接绘制、样式应用
3. **交互实现** (2分钟) - 筛选、点击、导航功能

**必须功能：**
- ✅ 四层关注焦点筛选
- ✅ 节点点击详情显示
- ✅ 当前位置标识
- ✅ 响应式布局

---

## 🎯 **最终成果标准**

### **用户体验目标：**
1. **5秒内理解** - 用户能快速理解这是什么领域
2. **30秒内定位** - 找到自己感兴趣的关注焦点
3. **2分钟内决策** - 选择具体的学习切入点

### **技术质量标准：**
1. **数据准确性** - 历史事件真实，关系逻辑正确
2. **视觉清晰度** - 信息层次分明，不会迷失
3. **交互流畅性** - 响应及时，操作直观
4. **兼容性** - 跨设备、跨浏览器正常工作

---

## 📊 **流水线效率指标**

### **时间指标：**
- **总耗时：** 33-39分钟
- **0A阶段：** 20-26分钟 (60-67%)
- **0B阶段：** 6分钟 (15-18%)
- **0C阶段：** 7分钟 (18-21%)

### **质量指标：**
- **节点数量：** 8-15个
- **连接数量：** 节点数 × 0.8-1.2
- **层次覆盖：** 四层都有节点
- **时间跨度：** 通常20-80年

---

## 🔄 **标准化检查清单**

### **0A阶段检查：**
- [ ] **要素分解**：是否从多个维度全面分解了领域要素？
- [ ] **专家补充**：是否主动补充了用户可能忽略的重要要素？
- [ ] **分解深度**：是否达到了合适的分解深度？
- [ ] **生态链构建**：四层生态结构是否逻辑完整？
- [ ] **角色视角**：政策/技术/商业/应用层是否全面分析？
- [ ] **扩展层次**：是否根据领域特点添加了必要的扩展层次？
- [ ] **五要素分析**：每层是否都包含现状/关键/趋势/争议/不确定性？
- [ ] **分层输出**：三层认知是否形成清晰的递进关系？
- [ ] **个性化适配**：Level 2是否根据用户背景深度定制？
- [ ] **完整性验证**：是否经过多轮检查确保无重要遗漏？

### **0B阶段检查：**
- [ ] 时间排序正确无误
- [ ] 3D坐标计算合理
- [ ] 节点无重叠现象
- [ ] 连接关系有效
- [ ] 数据格式标准

### **0C阶段检查：**
- [ ] HTML结构完整
- [ ] CSS样式美观
- [ ] JavaScript功能正常
- [ ] 交互体验流畅
- [ ] 跨设备兼容

---

## 🚀 **使用方法**

### **对于AI执行者：**
1. 严格按照流水线步骤执行
2. 每个阶段完成后进行质量检查
3. 确保数据在阶段间正确传递
4. 遇到问题及时调整和优化

### **对于用户：**
1. 提供清晰的领域描述
2. 配合回答背景调研问题
3. 对生成结果进行反馈
4. 选择感兴趣的切入点继续深入

---

## 📈 **持续优化**

### **数据积累：**
- 记录每次执行的时间和质量
- 收集用户反馈和使用数据
- 建立领域知识库和模板库

### **流程改进：**
- 优化耗时较长的步骤
- 提高数据准确性和完整性
- 增强用户体验和交互效果

---

**🎯 目标：让任何人都能在40分钟内获得任何领域的专家级知识全景图！**
