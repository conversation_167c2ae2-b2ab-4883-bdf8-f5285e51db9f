# 零阶段通用操作模式 - AI标准执行流程

## 🎯 **零阶段核心目标**
将用户从"我想了解某个东西"转化为"我知道我不知道的东西，并选择了切入点"

## 📋 **三步标准操作流程**

### 第1步：文字版分解拆分 📝

#### 1.1 用户输入分析模板
```yaml
输入处理:
  - 领域识别: [用户提到的主题] → [学科分类]
  - 复杂度评估: [简单/中等/高复杂度]
  - 输出格式决策: [文字说明/2D图表/3D知识图谱]

用户背景调研问题:
  - 您的专业背景是什么？
  - 您希望从哪个角度了解[主题]？
  - 您的学习目标是什么？
  - 您希望投入多少时间？
```

#### 1.2 知识结构分解模板
```yaml
四层关注焦点模型:
  政策层 (Policy Layer):
    关注焦点: 合规性、安全性、社会影响
    典型角色: 政府官员、法律专家、政策制定者
    核心问题: "如何确保[主题]安全合规发展？"
    
  技术层 (Technology Layer):
    关注焦点: 突破性、效率性、创新性
    典型角色: 研究员、工程师、技术专家
    核心问题: "如何实现[主题]技术突破和创新？"
    
  商业层 (Business Layer):
    关注焦点: 盈利性、市场机会、投资回报
    典型角色: 企业家、投资人、商业分析师
    核心问题: "如何将[主题]转化为商业价值？"
    
  应用层 (Application Layer):
    关注焦点: 便利性、易用性、实用性
    典型角色: 普通用户、产品经理、应用开发者
    核心问题: "如何让[主题]真正便民利民？"
```

#### 1.3 时间线节点提取模板
```yaml
历史节点提取原则:
  - 选择真实的历史事件
  - 每个节点必须有明确的时间、层次、影响力
  - 节点间要有逻辑关联关系
  - 突出当前时间点的位置

节点数据结构:
  id: 唯一标识符
  year: 具体年份
  layer: [policy/tech/business/application/cross]
  title: 简洁标题
  impactLevel: 1-5影响力等级
  focus: 该层次的具体关注焦点
  description: 客观历史描述
  impact: [具体影响列表]
  current: 是否为当前切入点
```

#### 1.4 关联关系梳理模板
```yaml
影响关系网络:
  技术驱动商业:
    - [技术突破] → [商业机会] → [市场成功]
    
  商业推动应用:
    - [投资资金] → [产品开发] → [用户普及]
    
  应用反馈政策:
    - [社会影响] → [监管需求] → [政策制定]
    
  政策影响技术:
    - [监管要求] → [技术约束] → [发展方向]

连接强度定义:
  - 强连接 (0.8-1.0): 直接技术传承或因果关系
  - 中连接 (0.6-0.8): 间接影响关系
  - 弱连接 (0.4-0.6): 背景影响因素
```

### 第2步：生成HTML基础版本 🏗️

#### 2.1 HTML结构生成模板
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>[主题]知识图谱</title>
    <!-- 必要的库引入 -->
</head>
<body>
    <div class="container">
        <!-- 头部：标题和说明 -->
        <div class="header">
            <h1>[主题]知识图谱</h1>
            <p>探索发展脉络，找到最佳切入点</p>
        </div>
        
        <!-- 主体：可视化区域 + 信息面板 -->
        <div class="main-content">
            <div class="visualization-area">
                <!-- 图谱展示区 -->
            </div>
            <div class="info-panel">
                <!-- 四层关注焦点说明 -->
                <!-- 节点详情显示 -->
            </div>
        </div>
        
        <!-- 底部：时间线和操作说明 -->
        <div class="bottom-section">
            <!-- 当前位置标识 -->
            <!-- 操作指南 -->
        </div>
    </div>
</body>
</html>
```

#### 2.2 数据结构转换模板
```javascript
// 将第1步的文字分解转换为JavaScript数据
const knowledgeNodes = [
    {
        id: '[唯一标识]',
        year: [年份],
        layer: '[层次]',
        title: '[标题]',
        impactLevel: [1-5],
        focus: '[关注焦点]',
        description: '[描述]',
        impact: ['影响1', '影响2'],
        current: [true/false]
    }
    // ... 更多节点
];

const connections = [
    { from: '[节点ID1]', to: '[节点ID2]', strength: [0.4-1.0] }
    // ... 更多连接
];
```

#### 2.3 基础功能实现清单
```yaml
必须实现的功能:
  - 节点展示: 根据数据生成可视化节点
  - 层次筛选: 按四个层次筛选显示
  - 节点详情: 点击查看详细信息
  - 时间导航: 显示当前时间位置
  - 响应式布局: 适配不同屏幕尺寸

可选增强功能:
  - 连接线显示: 展示节点间关系
  - 动画效果: 增强视觉体验
  - 搜索功能: 快速定位节点
```

### 第3步：3D逻辑严谨优化 🎨

#### 3.1 三轴数学逻辑定义
```javascript
// X轴 - 时间维度 (Time Dimension)
function calculateTimeX(year) {
    const minYear = [最早年份];
    const maxYear = [最晚年份];
    const range = 800; // 3D空间总长度
    return ((year - minYear) / (maxYear - minYear)) * range - 400;
}

// Y轴 - 影响力维度 (Impact Dimension)
function calculateImpactY(impactLevel) {
    // 1-5级影响力映射到-200到200的Y坐标
    return ((impactLevel - 1) / 4) * 400 - 200;
}

// Z轴 - 关注焦点维度 (Focus Dimension)
function calculateFocusZ(layer) {
    const depths = {
        'policy': -150,      // 政策层（最远）
        'tech': -50,         // 技术层
        'business': 50,      // 商业层  
        'application': 150,  // 应用层（最近）
        'cross': 0           // 跨层面（中间）
    };
    return depths[layer] || 0;
}
```

#### 3.2 3D场景构建标准
```javascript
// 场景初始化标准流程
function init3DScene() {
    // 1. 创建基础组件
    scene = new THREE.Scene();
    camera = new THREE.PerspectiveCamera(75, width/height, 0.1, 2000);
    renderer = new THREE.WebGLRenderer({ antialias: true });
    
    // 2. 设置环境
    scene.background = new THREE.Color(0x0a0a0a);
    scene.fog = new THREE.Fog(0x0a0a0a, 100, 1000);
    
    // 3. 添加光源系统
    addLightingSystem();
    
    // 4. 创建坐标轴
    createCoordinateAxes();
    
    // 5. 生成知识节点
    createKnowledgeNodes();
    
    // 6. 建立连接关系
    createNodeConnections();
    
    // 7. 设置交互控制
    setupInteractionControls();
    
    // 8. 启动渲染循环
    startRenderLoop();
}
```

#### 3.3 节点创建标准
```javascript
// 节点创建标准函数
function createKnowledgeNode(nodeData) {
    const position = calculate3DPosition(nodeData);
    
    // 基础几何体
    const geometry = new THREE.SphereGeometry(
        nodeData.current ? 12 : 8, // 当前节点更大
        16, 16
    );
    
    // 材质设置
    const material = new THREE.MeshPhongMaterial({ 
        color: getLayerColor(nodeData.layer),
        transparent: true,
        opacity: 0.8,
        shininess: 100
    });
    
    const node = new THREE.Mesh(geometry, material);
    node.position.set(position.x, position.y, position.z);
    node.userData = nodeData;
    
    // 当前时间节点特殊效果
    if (nodeData.current) {
        addCurrentNodeEffects(node);
    }
    
    // 添加文字标签
    addTextLabel(nodeData.title, position);
    
    return node;
}
```

#### 3.4 交互功能标准
```javascript
// 交互功能实现标准
const interactionFeatures = {
    // 鼠标点击检测
    nodeSelection: {
        method: 'raycaster',
        action: 'showNodeDetail'
    },
    
    // 视角控制
    cameraControl: {
        rotation: 'mouse_drag',
        zoom: 'mouse_wheel',
        reset: 'button_click'
    },
    
    // 筛选功能
    layerFilter: {
        buttons: ['policy', 'tech', 'business', 'application'],
        action: 'toggleNodeVisibility'
    },
    
    // 动画控制
    animationControl: {
        nodeRotation: 'continuous',
        currentNodePulse: 'sine_wave',
        connectionFlow: 'opacity_animation'
    }
};
```

## 🎯 **AI执行检查清单**

### 第1步完成标准
- [ ] 用户输入已分析并分类
- [ ] 四层关注焦点已定义
- [ ] 时间线节点已提取（至少15个）
- [ ] 节点间关联关系已梳理
- [ ] 当前时间点已标识

### 第2步完成标准
- [ ] HTML基础结构已生成
- [ ] CSS样式已实现
- [ ] JavaScript数据结构已转换
- [ ] 基础交互功能已实现
- [ ] 响应式布局已适配

### 第3步完成标准
- [ ] 三轴数学逻辑已严谨定义
- [ ] 3D场景已正确构建
- [ ] 节点位置计算准确
- [ ] 连接关系可视化清晰
- [ ] 交互功能完整流畅
- [ ] 视觉效果专业美观

## 🚀 **质量评估标准**

### 数据准确性
- 历史事件真实可靠
- 时间节点准确无误
- 影响关系符合逻辑
- 层次分类合理清晰

### 技术实现质量
- 代码结构清晰规范
- 数学计算精确严谨
- 3D效果流畅自然
- 交互体验直观友好

### 用户体验效果
- 信息层次分明
- 视觉引导清晰
- 操作逻辑合理
- 学习目标明确

## 📝 **使用说明**

AI在执行零阶段任务时，必须严格按照以上三步流程执行：

1. **第1步**：深入分析用户需求，进行详细的知识结构分解
2. **第2步**：生成功能完整的HTML基础版本
3. **第3步**：基于严谨的数学逻辑优化3D效果

每一步都要达到相应的完成标准，确保最终输出的知识图谱既有严谨的逻辑基础，又有优秀的视觉效果和用户体验。
